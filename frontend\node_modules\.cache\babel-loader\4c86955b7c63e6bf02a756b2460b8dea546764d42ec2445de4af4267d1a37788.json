{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isPlainObject(object) {\n  if (typeof object !== 'object') {\n    return false;\n  }\n  if (object == null) {\n    return false;\n  }\n  if (Object.getPrototypeOf(object) === null) {\n    return true;\n  }\n  if (Object.prototype.toString.call(object) !== '[object Object]') {\n    const tag = object[Symbol.toStringTag];\n    if (tag == null) {\n      return false;\n    }\n    const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n    if (isTagReadonly) {\n      return false;\n    }\n    return object.toString() === `[object ${tag}]`;\n  }\n  let proto = object;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(object) === proto;\n}\nexports.isPlainObject = isPlainObject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isPlainObject", "object", "getPrototypeOf", "prototype", "toString", "call", "tag", "isTagReadonly", "getOwnPropertyDescriptor", "writable", "proto"], "sources": ["E:/code1/task3/frontend/node_modules/es-toolkit/dist/compat/predicate/isPlainObject.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isPlainObject(object) {\n    if (typeof object !== 'object') {\n        return false;\n    }\n    if (object == null) {\n        return false;\n    }\n    if (Object.getPrototypeOf(object) === null) {\n        return true;\n    }\n    if (Object.prototype.toString.call(object) !== '[object Object]') {\n        const tag = object[Symbol.toStringTag];\n        if (tag == null) {\n            return false;\n        }\n        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n        if (isTagReadonly) {\n            return false;\n        }\n        return object.toString() === `[object ${tag}]`;\n    }\n    let proto = object;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(object) === proto;\n}\n\nexports.isPlainObject = isPlainObject;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC3B,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAO,KAAK;EAChB;EACA,IAAIA,MAAM,IAAI,IAAI,EAAE;IAChB,OAAO,KAAK;EAChB;EACA,IAAIP,MAAM,CAACQ,cAAc,CAACD,MAAM,CAAC,KAAK,IAAI,EAAE;IACxC,OAAO,IAAI;EACf;EACA,IAAIP,MAAM,CAACS,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,MAAM,CAAC,KAAK,iBAAiB,EAAE;IAC9D,MAAMK,GAAG,GAAGL,MAAM,CAACJ,MAAM,CAACC,WAAW,CAAC;IACtC,IAAIQ,GAAG,IAAI,IAAI,EAAE;MACb,OAAO,KAAK;IAChB;IACA,MAAMC,aAAa,GAAG,CAACb,MAAM,CAACc,wBAAwB,CAACP,MAAM,EAAEJ,MAAM,CAACC,WAAW,CAAC,EAAEW,QAAQ;IAC5F,IAAIF,aAAa,EAAE;MACf,OAAO,KAAK;IAChB;IACA,OAAON,MAAM,CAACG,QAAQ,CAAC,CAAC,KAAK,WAAWE,GAAG,GAAG;EAClD;EACA,IAAII,KAAK,GAAGT,MAAM;EAClB,OAAOP,MAAM,CAACQ,cAAc,CAACQ,KAAK,CAAC,KAAK,IAAI,EAAE;IAC1CA,KAAK,GAAGhB,MAAM,CAACQ,cAAc,CAACQ,KAAK,CAAC;EACxC;EACA,OAAOhB,MAAM,CAACQ,cAAc,CAACD,MAAM,CAAC,KAAKS,KAAK;AAClD;AAEAd,OAAO,CAACI,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}