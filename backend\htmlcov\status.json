{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "475c821b8d9025097c6760cd9332b9f7", "files": {"z_5f5a17c013354698___init___py": {"hash": "3c77fc9ef7f887ac2508d4109cf92472", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b___init___py": {"hash": "a0899e42f16157564e3e7c113d67f494", "index": {"url": "z_4a9cca768ff3c21b___init___py.html", "file": "app\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_script_versions_py": {"hash": "42b9b6a9d98160fe66f569db5deeeaa1", "index": {"url": "z_4a9cca768ff3c21b_script_versions_py.html", "file": "app\\api\\script_versions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_scripts_py": {"hash": "a269b9451e91d984dbf6cb64a0ff500d", "index": {"url": "z_4a9cca768ff3c21b_scripts_py.html", "file": "app\\api\\scripts.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 48, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_settings_py": {"hash": "c730969783f51bd2f21fcd3282ef181a", "index": {"url": "z_4a9cca768ff3c21b_settings_py.html", "file": "app\\api\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 47, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_statistics_py": {"hash": "0cd4accd45c636b80bd8e58a85fcb9e5", "index": {"url": "z_4a9cca768ff3c21b_statistics_py.html", "file": "app\\api\\statistics.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 38, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_system_settings_py": {"hash": "2657528c5e86d8bcdc7518f0e07e0878", "index": {"url": "z_4a9cca768ff3c21b_system_settings_py.html", "file": "app\\api\\system_settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_task_workflows_py": {"hash": "c667f8736a2e84918dd2b2e6f0943116", "index": {"url": "z_4a9cca768ff3c21b_task_workflows_py.html", "file": "app\\api\\task_workflows.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 227, "n_excluded": 0, "n_missing": 227, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4a9cca768ff3c21b_tasks_py": {"hash": "171e2f3f49da834b8297a96216f04d3e", "index": {"url": "z_4a9cca768ff3c21b_tasks_py.html", "file": "app\\api\\tasks.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_config_py": {"hash": "3ecbdd984617e697b03cc41f67b23a95", "index": {"url": "z_5f5a17c013354698_config_py.html", "file": "app\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 12, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_database_py": {"hash": "b0cfc7d734071442200a3fb9e9f6d532", "index": {"url": "z_5f5a17c013354698_database_py.html", "file": "app\\database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 12, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d___init___py": {"hash": "52d149d590b7ad5dcbf6bbfd511ef143", "index": {"url": "z_1374716a89f3e08d___init___py.html", "file": "app\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_notification_py": {"hash": "129de8677f5a160a828220e5ffa6f615", "index": {"url": "z_1374716a89f3e08d_notification_py.html", "file": "app\\models\\notification.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_script_py": {"hash": "49dcdf53f876238be82ac1fbc2012ee7", "index": {"url": "z_1374716a89f3e08d_script_py.html", "file": "app\\models\\script.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_script_version_py": {"hash": "ebd8949797dff41ac28b3860c8bdf3d8", "index": {"url": "z_1374716a89f3e08d_script_version_py.html", "file": "app\\models\\script_version.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_setting_py": {"hash": "36f7c0709e587e172328ce4f7f1b7d10", "index": {"url": "z_1374716a89f3e08d_setting_py.html", "file": "app\\models\\setting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_system_setting_py": {"hash": "6d13c95dd857a9254bbbb1613481ee84", "index": {"url": "z_1374716a89f3e08d_system_setting_py.html", "file": "app\\models\\system_setting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 10, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_task_py": {"hash": "00ac6c0218fcebd8ebff67d4ce4eacb3", "index": {"url": "z_1374716a89f3e08d_task_py.html", "file": "app\\models\\task.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_task_execution_py": {"hash": "921442fa6cfea3fcbab7448018392ce9", "index": {"url": "z_1374716a89f3e08d_task_execution_py.html", "file": "app\\models\\task_execution.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_task_workflow_py": {"hash": "53542e9645572e0a5e64c76d3ac7f516", "index": {"url": "z_1374716a89f3e08d_task_workflow_py.html", "file": "app\\models\\task_workflow.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1374716a89f3e08d_workflow_task_py": {"hash": "1ebe6d5ff2b0fef5ec0da92db8adaa4c", "index": {"url": "z_1374716a89f3e08d_workflow_task_py.html", "file": "app\\models\\workflow_task.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_scheduler_py": {"hash": "343fd70b10cd021bbcd8f02aa1ed7b37", "index": {"url": "z_5f5a17c013354698_scheduler_py.html", "file": "app\\scheduler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 0, "n_missing": 211, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174___init___py": {"hash": "8f96e0487d400bed7b4c91e861310773", "index": {"url": "z_b4c115836e286174___init___py.html", "file": "app\\schemas\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_script_py": {"hash": "d373e1b3d2ff101dd344f7a12e610742", "index": {"url": "z_b4c115836e286174_script_py.html", "file": "app\\schemas\\script.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 18, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_setting_py": {"hash": "bcb45bc79d7d26cd0ea46f24c4718293", "index": {"url": "z_b4c115836e286174_setting_py.html", "file": "app\\schemas\\setting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b4c115836e286174_task_py": {"hash": "ed3a2e17e04b2c26e3d529120592a017", "index": {"url": "z_b4c115836e286174_task_py.html", "file": "app\\schemas\\task.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 19, "n_excluded": 0, "n_missing": 19, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_condition_parser_py": {"hash": "6534097ce1635bd52ffaef0f34e2443f", "index": {"url": "z_a7b07432402c05f1_condition_parser_py.html", "file": "app\\utils\\condition_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_dependency_manager_py": {"hash": "ecb10545da2596d2f60fe74bb559c32e", "index": {"url": "z_a7b07432402c05f1_dependency_manager_py.html", "file": "app\\utils\\dependency_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_execution_manager_py": {"hash": "ea3a99c3346f30f6582721b05b0b7d29", "index": {"url": "z_a7b07432402c05f1_execution_manager_py.html", "file": "app\\utils\\execution_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 202, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_parameter_manager_py": {"hash": "d917add5a1ced7c37025e2f9c58de75d", "index": {"url": "z_a7b07432402c05f1_parameter_manager_py.html", "file": "app\\utils\\parameter_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 22, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_security_py": {"hash": "5a3d7067d5f4ff67c4524a4999ff6425", "index": {"url": "z_a7b07432402c05f1_security_py.html", "file": "app\\utils\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_websocket_py": {"hash": "86abca2d6fd32bec438a0f1949b1d0d3", "index": {"url": "z_5f5a17c013354698_websocket_py.html", "file": "app\\websocket.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}