/**
 * TaskWorkflow组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import axios from 'axios';
import TaskWorkflow from '../TaskWorkflow';

// Mock axios
const mockedAxios = axios;

// 测试工具函数
const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

// Mock数据
const mockWorkflows = [
  {
    id: 1,
    name: '测试工作流1',
    description: '这是第一个测试工作流',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    workflow_definition: {
      nodes: [
        { id: 'task1', name: '任务1', task_id: 1 }
      ],
      edges: []
    }
  },
  {
    id: 2,
    name: '测试工作流2',
    description: '这是第二个测试工作流',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    workflow_definition: {
      nodes: [
        { id: 'task1', name: '任务1', task_id: 1 },
        { id: 'task2', name: '任务2', task_id: 2 }
      ],
      edges: [
        { source: 'task1', target: 'task2' }
      ]
    }
  }
];

const mockTasks = [
  { id: 1, name: '任务1', description: '第一个任务' },
  { id: 2, name: '任务2', description: '第二个任务' }
];

describe('TaskWorkflow组件', () => {
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 默认API响应
    mockedAxios.get.mockImplementation((url) => {
      if (url.includes('/task-workflows/workflows')) {
        return mockApiResponse({ code: 200, data: mockWorkflows });
      }
      if (url.includes('/tasks/')) {
        return mockApiResponse(mockTasks);
      }
      return mockApiResponse({ code: 200, data: [] });
    });
  });

  test('渲染工作流列表', async () => {
    renderWithRouter(<TaskWorkflow />);
    
    // 检查页面标题
    expect(screen.getByText('任务编排管理')).toBeInTheDocument();
    
    // 检查创建按钮
    expect(screen.getByText('创建工作流')).toBeInTheDocument();
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
      expect(screen.getByText('测试工作流2')).toBeInTheDocument();
    });
  });

  test('显示空状态', async () => {
    mockedAxios.get.mockResolvedValue(mockApiResponse({ code: 200, data: [] }));
    
    renderWithRouter(<TaskWorkflow />);
    
    await waitFor(() => {
      expect(screen.getByText('创建工作流')).toBeInTheDocument();
    });
  });

  test('打开创建工作流模态框', async () => {
    const user = userEvent.setup();
    renderWithRouter(<TaskWorkflow />);
    
    // 点击创建按钮
    const createButton = screen.getByText('创建工作流');
    await user.click(createButton);
    
    // 检查模态框是否打开
    expect(screen.getByText('创建工作流')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入工作流名称')).toBeInTheDocument();
  });

  test('创建新工作流', async () => {
    const user = userEvent.setup();
    mockedAxios.post.mockResolvedValue(mockApiResponse({
      code: 200,
      data: { id: 3, name: '新工作流', description: '新的描述' }
    }));
    
    renderWithRouter(<TaskWorkflow />);
    
    // 打开创建模态框
    await user.click(screen.getByText('创建工作流'));
    
    // 填写表单
    await user.type(screen.getByPlaceholderText('请输入工作流名称'), '新工作流');
    await user.type(screen.getByPlaceholderText('请输入工作流描述'), '新的描述');
    
    // 提交表单
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 验证API调用
    await waitFor(() => {
      expect(mockedAxios.post).toHaveBeenCalledWith('/task-workflows/workflows', {
        name: '新工作流',
        description: '新的描述',
        workflow_definition: {
          nodes: [],
          edges: [],
          version: '1.0'
        }
      });
    });
  });

  test('编辑工作流', async () => {
    const user = userEvent.setup();
    mockedAxios.put.mockResolvedValue(mockApiResponse({
      code: 200,
      data: { id: 1, name: '更新的工作流', description: '更新的描述' }
    }));
    
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
    });
    
    // 点击编辑按钮
    const editButtons = screen.getAllByText('编辑');
    await user.click(editButtons[0]);
    
    // 检查模态框标题
    expect(screen.getByText('编辑工作流')).toBeInTheDocument();
    
    // 修改名称
    const nameInput = screen.getByDisplayValue('测试工作流1');
    await user.clear(nameInput);
    await user.type(nameInput, '更新的工作流');
    
    // 提交表单
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 验证API调用
    await waitFor(() => {
      expect(mockedAxios.put).toHaveBeenCalledWith('/task-workflows/workflows/1', {
        name: '更新的工作流',
        description: '这是第一个测试工作流'
      });
    });
  });

  test('删除工作流', async () => {
    const user = userEvent.setup();
    mockedAxios.delete.mockResolvedValue(mockApiResponse({ code: 200 }));
    
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
    });
    
    // 点击删除按钮
    const deleteButtons = screen.getAllByText('删除');
    await user.click(deleteButtons[0]);
    
    // 确认删除
    await user.click(screen.getByText('确定'));
    
    // 验证API调用
    await waitFor(() => {
      expect(mockedAxios.delete).toHaveBeenCalledWith('/task-workflows/workflows/1');
    });
  });

  test('执行工作流', async () => {
    const user = userEvent.setup();
    mockedAxios.post.mockResolvedValue(mockApiResponse({
      code: 200,
      data: { execution_id: 123, status: 'running' }
    }));
    
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
    });
    
    // 点击执行按钮
    const executeButtons = screen.getAllByText('执行');
    await user.click(executeButtons[0]);
    
    // 验证API调用
    await waitFor(() => {
      expect(mockedAxios.post).toHaveBeenCalledWith('/task-workflows/workflows/1/execute');
    });
  });

  test('查看工作流详情', async () => {
    const user = userEvent.setup();
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
    });
    
    // 点击查看按钮
    const viewButtons = screen.getAllByText('查看');
    await user.click(viewButtons[0]);
    
    // 检查抽屉是否打开
    expect(screen.getByText('工作流详情')).toBeInTheDocument();
    expect(screen.getByText('基本信息')).toBeInTheDocument();
    expect(screen.getByText('工作流定义')).toBeInTheDocument();
  });

  test('打开工作流设计器', async () => {
    const user = userEvent.setup();
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
    });
    
    // 点击设计按钮
    const designButtons = screen.getAllByText('设计');
    await user.click(designButtons[0]);
    
    // 检查设计器模态框是否打开
    expect(screen.getByText('设计工作流: 测试工作流1')).toBeInTheDocument();
  });

  test('处理API错误', async () => {
    mockedAxios.get.mockRejectedValue(mockApiError('网络错误'));
    
    renderWithRouter(<TaskWorkflow />);
    
    // 等待错误处理
    await waitFor(() => {
      expect(screen.getByText('创建工作流')).toBeInTheDocument();
    });
  });

  test('表单验证', async () => {
    const user = userEvent.setup();
    renderWithRouter(<TaskWorkflow />);
    
    // 打开创建模态框
    await user.click(screen.getByText('创建工作流'));
    
    // 不填写名称直接提交
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 检查验证错误
    expect(screen.getByText('请输入工作流名称')).toBeInTheDocument();
  });

  test('分页功能', async () => {
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('共 2 条记录')).toBeInTheDocument();
    });
  });

  test('搜索和过滤功能', async () => {
    renderWithRouter(<TaskWorkflow />);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('测试工作流1')).toBeInTheDocument();
      expect(screen.getByText('测试工作流2')).toBeInTheDocument();
    });
    
    // 这里可以添加搜索功能的测试，如果组件支持的话
  });
});
