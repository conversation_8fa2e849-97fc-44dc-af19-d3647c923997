/**
 * WorkflowDesigner组件测试
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import axios from 'axios';
import WorkflowDesigner from '../WorkflowDesigner';

// Mock axios
const mockedAxios = axios;

// Mock数据
const mockWorkflow = {
  id: 1,
  name: '测试工作流',
  workflow_definition: {
    nodes: [
      {
        id: 'task1',
        name: '任务1',
        task_id: 1,
        execution_type: 'serial',
        position: { x: 100, y: 100 }
      },
      {
        id: 'task2',
        name: '任务2',
        task_id: 2,
        execution_type: 'parallel',
        position: { x: 300, y: 100 }
      }
    ],
    edges: [
      {
        id: 'edge1',
        source: 'task1',
        target: 'task2',
        condition: 'success'
      }
    ]
  }
};

const mockTasks = [
  { id: 1, name: '数据提取任务' },
  { id: 2, name: '数据处理任务' },
  { id: 3, name: '报告生成任务' }
];

const mockOnSave = jest.fn();
const mockOnClose = jest.fn();

describe('WorkflowDesigner组件', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // 默认API响应
    mockedAxios.post.mockImplementation((url) => {
      if (url.includes('/validate')) {
        return mockApiResponse({
          code: 200,
          data: { valid: true, message: '验证通过' }
        });
      }
      return mockApiResponse({ code: 200 });
    });
  });

  const defaultProps = {
    workflow: mockWorkflow,
    tasks: mockTasks,
    onSave: mockOnSave,
    onClose: mockOnClose
  };

  test('渲染工作流设计器', () => {
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 检查工具栏按钮
    expect(screen.getByText('添加节点')).toBeInTheDocument();
    expect(screen.getByText('添加连接')).toBeInTheDocument();
    expect(screen.getByText('验证工作流')).toBeInTheDocument();
    expect(screen.getByText('保存工作流')).toBeInTheDocument();
    expect(screen.getByText('执行工作流')).toBeInTheDocument();
    
    // 检查节点列表
    expect(screen.getByText('节点列表')).toBeInTheDocument();
    expect(screen.getByText('任务1')).toBeInTheDocument();
    expect(screen.getByText('任务2')).toBeInTheDocument();
    
    // 检查连接列表
    expect(screen.getByText('连接列表')).toBeInTheDocument();
    expect(screen.getByText('task1 → task2')).toBeInTheDocument();
  });

  test('添加新节点', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击添加节点按钮
    await user.click(screen.getByText('添加节点'));
    
    // 检查模态框是否打开
    expect(screen.getByText('添加节点')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('请输入节点名称')).toBeInTheDocument();
    
    // 填写节点信息
    await user.type(screen.getByPlaceholderText('请输入节点名称'), '新节点');
    await user.click(screen.getByText('请选择关联任务'));
    await user.click(screen.getByText('数据提取任务'));
    
    // 提交表单
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 验证节点是否添加
    await waitFor(() => {
      expect(screen.getByText('新节点')).toBeInTheDocument();
    });
  });

  test('编辑现有节点', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击编辑按钮
    const editButtons = screen.getAllByText('编辑');
    await user.click(editButtons[0]);
    
    // 检查模态框是否打开并预填数据
    expect(screen.getByText('编辑节点')).toBeInTheDocument();
    expect(screen.getByDisplayValue('任务1')).toBeInTheDocument();
    
    // 修改节点名称
    const nameInput = screen.getByDisplayValue('任务1');
    await user.clear(nameInput);
    await user.type(nameInput, '修改后的任务1');
    
    // 提交表单
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 验证节点是否更新
    await waitFor(() => {
      expect(screen.getByText('修改后的任务1')).toBeInTheDocument();
    });
  });

  test('删除节点', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击删除按钮
    const deleteButtons = screen.getAllByText('删除');
    await user.click(deleteButtons[0]);
    
    // 验证节点是否被删除
    await waitFor(() => {
      expect(screen.queryByText('任务1')).not.toBeInTheDocument();
    });
  });

  test('添加连接', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击添加连接按钮
    await user.click(screen.getByText('添加连接'));
    
    // 检查模态框是否打开
    expect(screen.getByText('添加连接')).toBeInTheDocument();
    expect(screen.getByText('请选择源节点')).toBeInTheDocument();
    expect(screen.getByText('请选择目标节点')).toBeInTheDocument();
    
    // 选择源节点和目标节点
    await user.click(screen.getByText('请选择源节点'));
    await user.click(screen.getByText('任务2'));
    
    await user.click(screen.getByText('请选择目标节点'));
    await user.click(screen.getByText('任务1'));
    
    // 提交表单
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 验证连接是否添加
    await waitFor(() => {
      expect(screen.getByText('task2 → task1')).toBeInTheDocument();
    });
  });

  test('删除连接', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 找到连接卡片中的删除按钮
    const connectionCard = screen.getByText('task1 → task2').closest('.ant-card');
    const deleteButton = connectionCard.querySelector('button');
    
    await user.click(deleteButton);
    
    // 验证连接是否被删除
    await waitFor(() => {
      expect(screen.queryByText('task1 → task2')).not.toBeInTheDocument();
    });
  });

  test('验证工作流', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击验证按钮
    await user.click(screen.getByText('验证工作流'));
    
    // 验证API调用
    await waitFor(() => {
      expect(mockedAxios.post).toHaveBeenCalledWith('/task-workflows/workflows/validate', {
        workflow_definition: expect.objectContaining({
          nodes: expect.any(Array),
          edges: expect.any(Array),
          version: '1.0'
        })
      });
    });
  });

  test('保存工作流', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击保存按钮
    await user.click(screen.getByText('保存工作流'));
    
    // 验证onSave回调是否被调用
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(expect.objectContaining({
        nodes: expect.any(Array),
        edges: expect.any(Array),
        version: '1.0',
        updatedAt: expect.any(String)
      }));
    });
  });

  test('执行工作流', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 点击执行按钮
    await user.click(screen.getByText('执行工作流'));
    
    // 验证消息提示
    // 注意：这里应该显示"正在开发中"的消息
  });

  test('处理空工作流', () => {
    const emptyWorkflow = {
      ...mockWorkflow,
      workflow_definition: {
        nodes: [],
        edges: []
      }
    };
    
    render(<WorkflowDesigner {...{ ...defaultProps, workflow: emptyWorkflow }} />);
    
    // 检查空状态
    expect(screen.getByText('节点数: 0, 连接数: 0')).toBeInTheDocument();
  });

  test('表单验证 - 节点', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 打开添加节点模态框
    await user.click(screen.getByText('添加节点'));
    
    // 不填写必填字段直接提交
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 检查验证错误
    expect(screen.getByText('请输入节点名称')).toBeInTheDocument();
    expect(screen.getByText('请选择关联任务')).toBeInTheDocument();
  });

  test('表单验证 - 连接', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 打开添加连接模态框
    await user.click(screen.getByText('添加连接'));
    
    // 不填写必填字段直接提交
    await user.click(screen.getByRole('button', { name: '确定' }));
    
    // 检查验证错误
    expect(screen.getByText('请选择源节点')).toBeInTheDocument();
    expect(screen.getByText('请选择目标节点')).toBeInTheDocument();
  });

  test('循环依赖检测', async () => {
    const user = userEvent.setup();
    
    // Mock API返回循环依赖错误
    mockedAxios.post.mockResolvedValue(mockApiResponse({
      code: 400,
      data: { valid: false, message: '存在循环依赖' }
    }));
    
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 尝试保存会产生循环依赖的工作流
    await user.click(screen.getByText('保存工作流'));
    
    // 验证错误处理
    await waitFor(() => {
      expect(mockedAxios.post).toHaveBeenCalled();
    });
  });

  test('节点配置选项', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 打开添加节点模态框
    await user.click(screen.getByText('添加节点'));
    
    // 检查执行类型选项
    expect(screen.getByText('串行')).toBeInTheDocument();
    expect(screen.getByText('并行')).toBeInTheDocument();
    
    // 检查超时设置
    expect(screen.getByPlaceholderText('默认3600秒')).toBeInTheDocument();
  });

  test('连接条件配置', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 打开添加连接模态框
    await user.click(screen.getByText('添加连接'));
    
    // 检查条件选项
    expect(screen.getByText('成功时执行')).toBeInTheDocument();
    expect(screen.getByText('失败时执行')).toBeInTheDocument();
    expect(screen.getByText('总是执行')).toBeInTheDocument();
    expect(screen.getByText('完成时执行（成功或失败）')).toBeInTheDocument();
    expect(screen.getByText('自定义条件')).toBeInTheDocument();
  });

  test('参数配置功能', async () => {
    const user = userEvent.setup();
    render(<WorkflowDesigner {...defaultProps} />);
    
    // 打开添加节点模态框
    await user.click(screen.getByText('添加节点'));
    
    // 检查参数配置字段
    expect(screen.getByText('输入参数')).toBeInTheDocument();
    expect(screen.getByText('输出参数提取规则')).toBeInTheDocument();
  });
});
