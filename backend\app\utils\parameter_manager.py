"""
任务参数管理器
"""
import json
import re
from typing import Any, Dict, List, Union, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ParameterType(Enum):
    """参数类型"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    JSON = "json"
    LIST = "list"
    FILE_PATH = "file_path"

class ParameterManager:
    """参数管理器"""
    
    def __init__(self):
        self.type_converters = {
            ParameterType.STRING: str,
            ParameterType.INTEGER: int,
            ParameterType.FLOAT: float,
            ParameterType.BOOLEAN: self._convert_to_bool,
            ParameterType.JSON: self._convert_to_json,
            ParameterType.LIST: self._convert_to_list,
            ParameterType.FILE_PATH: str,
        }
    
    def _convert_to_bool(self, value: Any) -> bool:
        """转换为布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)
    
    def _convert_to_json(self, value: Any) -> Dict:
        """转换为JSON对象"""
        if isinstance(value, dict):
            return value
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                raise ValueError(f"无法解析JSON: {value}")
        raise ValueError(f"无法转换为JSON: {type(value)}")
    
    def _convert_to_list(self, value: Any) -> List:
        """转换为列表"""
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            try:
                # 尝试解析JSON数组
                parsed = json.loads(value)
                if isinstance(parsed, list):
                    return parsed
            except json.JSONDecodeError:
                pass
            # 按逗号分割字符串
            return [item.strip() for item in value.split(',') if item.strip()]
        if hasattr(value, '__iter__') and not isinstance(value, (str, dict)):
            return list(value)
        return [value]
    
    def validate_parameter(self, value: Any, param_type: ParameterType, 
                          constraints: Optional[Dict] = None) -> tuple[bool, str]:
        """
        验证参数
        
        Args:
            value: 参数值
            param_type: 参数类型
            constraints: 约束条件
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            # 类型转换
            converted_value = self.convert_parameter(value, param_type)
            
            # 约束检查
            if constraints:
                return self._check_constraints(converted_value, param_type, constraints)
            
            return True, "参数有效"
            
        except Exception as e:
            return False, str(e)
    
    def convert_parameter(self, value: Any, param_type: ParameterType) -> Any:
        """转换参数类型"""
        if value is None:
            return None
        
        converter = self.type_converters.get(param_type)
        if converter:
            return converter(value)
        else:
            raise ValueError(f"不支持的参数类型: {param_type}")
    
    def _check_constraints(self, value: Any, param_type: ParameterType, 
                          constraints: Dict) -> tuple[bool, str]:
        """检查约束条件"""
        # 必填检查
        if constraints.get('required', False) and value is None:
            return False, "参数不能为空"
        
        if value is None:
            return True, "参数有效"
        
        # 数值范围检查
        if param_type in [ParameterType.INTEGER, ParameterType.FLOAT]:
            min_val = constraints.get('min')
            max_val = constraints.get('max')
            
            if min_val is not None and value < min_val:
                return False, f"参数值不能小于 {min_val}"
            if max_val is not None and value > max_val:
                return False, f"参数值不能大于 {max_val}"
        
        # 字符串长度检查
        if param_type == ParameterType.STRING:
            min_length = constraints.get('min_length')
            max_length = constraints.get('max_length')
            
            if min_length is not None and len(value) < min_length:
                return False, f"字符串长度不能小于 {min_length}"
            if max_length is not None and len(value) > max_length:
                return False, f"字符串长度不能大于 {max_length}"
            
            # 正则表达式检查
            pattern = constraints.get('pattern')
            if pattern and not re.match(pattern, value):
                return False, f"字符串格式不匹配: {pattern}"
        
        # 列表长度检查
        if param_type == ParameterType.LIST:
            min_items = constraints.get('min_items')
            max_items = constraints.get('max_items')
            
            if min_items is not None and len(value) < min_items:
                return False, f"列表项数不能少于 {min_items}"
            if max_items is not None and len(value) > max_items:
                return False, f"列表项数不能多于 {max_items}"
        
        # 枚举值检查
        enum_values = constraints.get('enum')
        if enum_values and value not in enum_values:
            return False, f"参数值必须是以下之一: {enum_values}"
        
        return True, "参数有效"
    
    def extract_parameters_from_output(self, output: str, 
                                     extraction_rules: List[Dict]) -> Dict[str, Any]:
        """
        从输出中提取参数
        
        Args:
            output: 任务输出
            extraction_rules: 提取规则列表
            
        Returns:
            Dict: 提取的参数
        """
        extracted_params = {}
        
        for rule in extraction_rules:
            param_name = rule.get('name')
            pattern = rule.get('pattern')
            param_type = ParameterType(rule.get('type', 'string'))
            default_value = rule.get('default')
            
            if not param_name or not pattern:
                continue
            
            try:
                # 使用正则表达式提取
                match = re.search(pattern, output)
                if match:
                    # 获取第一个捕获组或整个匹配
                    value = match.group(1) if match.groups() else match.group(0)
                    # 类型转换
                    converted_value = self.convert_parameter(value, param_type)
                    extracted_params[param_name] = converted_value
                elif default_value is not None:
                    extracted_params[param_name] = default_value
                    
            except Exception as e:
                logger.error(f"参数提取失败 {param_name}: {str(e)}")
                if default_value is not None:
                    extracted_params[param_name] = default_value
        
        return extracted_params
    
    def resolve_parameter_references(self, parameters: Dict[str, Any], 
                                   context: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析参数引用
        
        Args:
            parameters: 参数字典
            context: 上下文变量
            
        Returns:
            Dict: 解析后的参数
        """
        resolved_params = {}
        
        for key, value in parameters.items():
            try:
                resolved_value = self._resolve_value(value, context)
                resolved_params[key] = resolved_value
            except Exception as e:
                logger.error(f"参数引用解析失败 {key}: {str(e)}")
                resolved_params[key] = value
        
        return resolved_params
    
    def _resolve_value(self, value: Any, context: Dict[str, Any]) -> Any:
        """解析单个值的引用"""
        if isinstance(value, str):
            # 解析变量引用 ${variable_name}
            def replace_reference(match):
                var_name = match.group(1)
                if var_name in context:
                    return str(context[var_name])
                else:
                    logger.warning(f"未找到变量: {var_name}")
                    return match.group(0)  # 保持原样
            
            resolved = re.sub(r'\$\{([^}]+)\}', replace_reference, value)
            
            # 如果整个字符串都是一个引用，尝试保持原始类型
            if re.match(r'^\$\{[^}]+\}$', value):
                var_name = re.match(r'^\$\{([^}]+)\}$', value).group(1)
                if var_name in context:
                    return context[var_name]
            
            return resolved
            
        elif isinstance(value, dict):
            return {k: self._resolve_value(v, context) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._resolve_value(item, context) for item in value]
        else:
            return value
    
    def create_parameter_mapping(self, source_params: Dict[str, Any], 
                               mapping_rules: List[Dict]) -> Dict[str, Any]:
        """
        创建参数映射
        
        Args:
            source_params: 源参数
            mapping_rules: 映射规则
            
        Returns:
            Dict: 映射后的参数
        """
        mapped_params = {}
        
        for rule in mapping_rules:
            source_key = rule.get('source')
            target_key = rule.get('target')
            transform = rule.get('transform')
            default_value = rule.get('default')
            
            if not source_key or not target_key:
                continue
            
            try:
                # 获取源值
                source_value = source_params.get(source_key, default_value)
                
                # 应用转换
                if transform and source_value is not None:
                    transformed_value = self._apply_transform(source_value, transform)
                    mapped_params[target_key] = transformed_value
                else:
                    mapped_params[target_key] = source_value
                    
            except Exception as e:
                logger.error(f"参数映射失败 {source_key} -> {target_key}: {str(e)}")
                if default_value is not None:
                    mapped_params[target_key] = default_value
        
        return mapped_params
    
    def _apply_transform(self, value: Any, transform: str) -> Any:
        """应用参数转换"""
        if transform == 'upper':
            return str(value).upper()
        elif transform == 'lower':
            return str(value).lower()
        elif transform == 'strip':
            return str(value).strip()
        elif transform == 'int':
            return int(value)
        elif transform == 'float':
            return float(value)
        elif transform == 'bool':
            return self._convert_to_bool(value)
        elif transform == 'json':
            return self._convert_to_json(value)
        elif transform == 'list':
            return self._convert_to_list(value)
        elif transform.startswith('regex:'):
            # 正则表达式转换
            pattern = transform[6:]
            match = re.search(pattern, str(value))
            return match.group(1) if match and match.groups() else str(value)
        else:
            logger.warning(f"未知的转换类型: {transform}")
            return value
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        """获取参数模式定义"""
        return {
            "types": [t.value for t in ParameterType],
            "constraints": {
                "required": "是否必填",
                "min": "最小值（数值类型）",
                "max": "最大值（数值类型）",
                "min_length": "最小长度（字符串类型）",
                "max_length": "最大长度（字符串类型）",
                "min_items": "最小项数（列表类型）",
                "max_items": "最大项数（列表类型）",
                "pattern": "正则表达式（字符串类型）",
                "enum": "枚举值列表"
            },
            "transforms": [
                "upper", "lower", "strip", "int", "float", "bool", 
                "json", "list", "regex:pattern"
            ],
            "examples": {
                "parameter_definition": {
                    "name": "input_file",
                    "type": "string",
                    "required": True,
                    "description": "输入文件路径"
                },
                "extraction_rule": {
                    "name": "result_count",
                    "pattern": r"处理了 (\d+) 条记录",
                    "type": "integer",
                    "default": 0
                },
                "mapping_rule": {
                    "source": "output_file",
                    "target": "input_file",
                    "transform": "strip"
                }
            }
        }
