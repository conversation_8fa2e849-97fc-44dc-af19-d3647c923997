[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --verbose
    --tb=short
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=90
    --strict-markers
markers =
    unit: Unit tests
    integration: Integration tests
    api: API tests
    slow: Slow running tests
    database: Tests that require database
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::sqlalchemy.exc.MovedIn20Warning
    ignore::pytest.PytestUnknownMarkWarning
asyncio_default_fixture_loop_scope = function
