[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = --verbose --tb=short --disable-warnings
markers =
    api: API接口测试
    workflow: 工作流相关测试
    performance: 性能测试
    integration: 集成测试
    slow: 慢速测试
    unit: 单元测试
    smoke: 冒烟测试
    regression: 回归测试
    security: 安全测试
    database: 数据库测试
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
minversion = 6.0
