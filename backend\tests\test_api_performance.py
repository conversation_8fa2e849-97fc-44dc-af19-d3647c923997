"""
API性能和错误处理测试
"""
import pytest
import json
import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed


class TestAPIPerformance:
    """API性能测试"""
    
    def test_api_response_time_tasks(self, client, performance_monitor):
        """测试任务API响应时间"""
        performance_monitor.start()
        response = client.get('/api/tasks')
        performance_monitor.stop()
        
        assert response.status_code == 200
        performance_monitor.assert_duration_under(1.0)  # 1秒内响应
    
    def test_api_response_time_workflows(self, client, performance_monitor):
        """测试工作流API响应时间"""
        performance_monitor.start()
        response = client.get('/api/workflows')
        performance_monitor.stop()
        
        assert response.status_code == 200
        performance_monitor.assert_duration_under(1.0)  # 1秒内响应
    
    def test_api_response_time_scripts(self, client, performance_monitor):
        """测试脚本API响应时间"""
        performance_monitor.start()
        response = client.get('/api/scripts')
        performance_monitor.stop()
        
        assert response.status_code == 200
        performance_monitor.assert_duration_under(1.0)  # 1秒内响应
    
    def test_concurrent_get_requests(self, client):
        """测试并发GET请求"""
        def make_request(endpoint):
            try:
                response = client.get(endpoint)
                return {
                    'endpoint': endpoint,
                    'status_code': response.status_code,
                    'response_time': time.time()
                }
            except Exception as e:
                return {
                    'endpoint': endpoint,
                    'error': str(e),
                    'response_time': time.time()
                }
        
        endpoints = ['/api/tasks', '/api/scripts', '/api/workflows'] * 5
        
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request, endpoint) for endpoint in endpoints]
            results = [future.result() for future in as_completed(futures)]
        end_time = time.time()
        
        # 验证结果
        success_count = sum(1 for r in results if r.get('status_code') == 200)
        total_requests = len(endpoints)
        
        assert success_count >= total_requests * 0.8  # 至少80%成功
        assert end_time - start_time < 5.0  # 总时间小于5秒
    
    def test_concurrent_post_requests(self, client, auth_headers):
        """测试并发POST请求"""
        def create_task(task_name):
            try:
                task_data = {
                    'name': f'并发测试任务_{task_name}',
                    'description': f'并发创建的任务 {task_name}',
                    'cron_expression': '0 0 * * *',
                    'script_id': 1
                }
                
                response = client.post('/api/tasks',
                                     data=json.dumps(task_data),
                                     headers=auth_headers)
                
                return {
                    'task_name': task_name,
                    'status_code': response.status_code,
                    'success': response.status_code in [201, 400]  # 201成功，400可能是验证失败
                }
            except Exception as e:
                return {
                    'task_name': task_name,
                    'error': str(e),
                    'success': False
                }
        
        task_names = [f'task_{i}' for i in range(10)]
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_task, name) for name in task_names]
            results = [future.result() for future in as_completed(futures)]
        
        # 验证结果
        success_count = sum(1 for r in results if r.get('success', False))
        assert success_count >= 7  # 至少70%成功
    
    def test_large_payload_handling(self, client, auth_headers):
        """测试大负载处理"""
        # 创建一个较大的工作流定义
        large_workflow = {
            'name': '大型工作流测试',
            'description': '测试大负载处理能力',
            'workflow_definition': {
                'nodes': [],
                'edges': []
            }
        }
        
        # 生成100个节点
        for i in range(100):
            node = {
                'id': f'task_{i}',
                'name': f'任务_{i}',
                'task_id': (i % 3) + 1,  # 循环使用任务ID 1, 2, 3
                'execution_type': 'serial' if i % 2 == 0 else 'parallel',
                'parameters': {
                    'param1': f'value_{i}',
                    'param2': i * 10,
                    'param3': f'description_for_task_{i}' * 10  # 增加数据量
                }
            }
            large_workflow['workflow_definition']['nodes'].append(node)
        
        # 生成边连接
        for i in range(99):
            edge = {
                'source': f'task_{i}',
                'target': f'task_{i+1}',
                'condition': 'success' if i % 2 == 0 else f'task_{i}.status == "success"'
            }
            large_workflow['workflow_definition']['edges'].append(edge)
        
        start_time = time.time()
        response = client.post('/api/workflows',
                             data=json.dumps(large_workflow),
                             headers=auth_headers)
        end_time = time.time()
        
        # 验证响应
        assert response.status_code in [201, 400, 413]  # 201成功，400验证失败，413负载过大
        assert end_time - start_time < 10.0  # 处理时间小于10秒
    
    def test_api_memory_usage(self, client):
        """测试API内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多次API调用
        for _ in range(50):
            client.get('/api/tasks')
            client.get('/api/scripts')
            client.get('/api/workflows')
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于50MB）
        assert memory_increase < 50, f"内存增长过多: {memory_increase:.2f}MB"


class TestAPIErrorHandling:
    """API错误处理测试"""
    
    def test_invalid_json_request(self, client, auth_headers):
        """测试无效JSON请求"""
        response = client.post('/api/tasks',
                             data='{"invalid": json}',  # 无效JSON
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert 'json' in data['message'].lower() or 'format' in data['message'].lower()
    
    def test_missing_required_fields(self, client, auth_headers):
        """测试缺少必需字段"""
        # 测试任务缺少名称
        task_data = {
            'description': '缺少名称的任务',
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert 'name' in data['message'].lower()
    
    def test_invalid_field_types(self, client, auth_headers):
        """测试无效字段类型"""
        # 测试任务ID为字符串而非整数
        task_data = {
            'name': '类型错误任务',
            'script_id': 'not_a_number',  # 应该是整数
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
    
    def test_invalid_cron_expression(self, client, auth_headers):
        """测试无效cron表达式"""
        task_data = {
            'name': '无效cron任务',
            'cron_expression': 'invalid-cron-expression',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert 'cron' in data['message'].lower()
    
    def test_nonexistent_resource(self, client):
        """测试访问不存在的资源"""
        response = client.get('/api/tasks/99999')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert data['code'] == 404
        assert 'not found' in data['message'].lower()
    
    def test_method_not_allowed(self, client):
        """测试不允许的HTTP方法"""
        response = client.patch('/api/tasks')
        assert response.status_code == 405
    
    def test_unauthorized_access(self, client):
        """测试未授权访问"""
        # 不提供认证头部
        task_data = {
            'name': '未授权任务',
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers={'Content-Type': 'application/json'})
        
        # 根据实际认证实现调整期望状态码
        assert response.status_code in [401, 403, 400]
    
    def test_malformed_authorization_header(self, client):
        """测试格式错误的认证头部"""
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'InvalidFormat'  # 错误格式
        }
        
        task_data = {
            'name': '认证错误任务',
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=headers)
        
        assert response.status_code in [401, 403, 400]
    
    def test_sql_injection_attempt(self, client):
        """测试SQL注入尝试"""
        # 尝试在任务名称中注入SQL
        task_data = {
            'name': "'; DROP TABLE tasks; --",
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers={'Content-Type': 'application/json'})
        
        # 应该被安全处理，不会导致服务器错误
        assert response.status_code in [201, 400, 401, 403]
        
        # 验证数据库仍然正常
        get_response = client.get('/api/tasks')
        assert get_response.status_code == 200
    
    def test_xss_attempt(self, client, auth_headers):
        """测试XSS攻击尝试"""
        # 尝试在描述中注入脚本
        task_data = {
            'name': '正常任务名',
            'description': '<script>alert("XSS")</script>',
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        # 应该被安全处理
        if response.status_code == 201:
            data = json.loads(response.data)
            # 验证脚本标签被转义或移除
            description = data['data'].get('description', '')
            assert '<script>' not in description
    
    def test_rate_limiting(self, client):
        """测试速率限制"""
        # 快速发送多个请求
        responses = []
        for _ in range(20):
            response = client.get('/api/tasks')
            responses.append(response.status_code)
        
        # 检查是否有速率限制响应
        rate_limited = any(status == 429 for status in responses)
        success_responses = sum(1 for status in responses if status == 200)
        
        # 至少应该有一些成功响应
        assert success_responses > 0
        
        # 如果实现了速率限制，应该有429响应
        # 如果没有实现，所有响应都应该是200
        assert all(status in [200, 429] for status in responses)
    
    def test_large_request_body(self, client, auth_headers):
        """测试过大的请求体"""
        # 创建一个非常大的描述
        large_description = 'x' * 100000  # 100KB
        
        task_data = {
            'name': '大请求体任务',
            'description': large_description,
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        # 应该被处理或拒绝，不应该导致服务器崩溃
        assert response.status_code in [201, 400, 413, 414]
    
    def test_concurrent_resource_access(self, client, auth_headers):
        """测试并发资源访问"""
        # 创建一个任务
        task_data = {
            'name': '并发访问测试任务',
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        create_response = client.post('/api/tasks',
                                    data=json.dumps(task_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            task_id = json.loads(create_response.data)['data']['id']
            
            def update_task():
                update_data = {
                    'description': f'更新时间: {time.time()}'
                }
                return client.put(f'/api/tasks/{task_id}',
                                data=json.dumps(update_data),
                                headers=auth_headers)
            
            # 并发更新同一个任务
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(update_task) for _ in range(5)]
                results = [future.result() for future in as_completed(futures)]
            
            # 验证没有服务器错误
            for response in results:
                assert response.status_code in [200, 400, 409]  # 200成功，400验证失败，409冲突


class TestAPIIntegrationErrors:
    """API集成错误测试"""
    
    def test_database_connection_error(self, client, auth_headers):
        """测试数据库连接错误处理"""
        # 这个测试需要模拟数据库连接失败
        # 在实际环境中可能需要特殊配置
        pass
    
    def test_external_service_timeout(self, client, auth_headers):
        """测试外部服务超时处理"""
        # 模拟外部服务调用超时
        with pytest.raises(Exception):
            # 这里应该有实际的外部服务调用
            pass
    
    def test_disk_space_full(self, client, auth_headers):
        """测试磁盘空间不足处理"""
        # 这个测试需要特殊的环境配置
        pass
