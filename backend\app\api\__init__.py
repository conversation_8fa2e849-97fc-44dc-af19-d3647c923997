"""
API蓝图模块
"""
from flask import Blueprint

# 创建主API蓝图
api_bp = Blueprint('api', __name__)

# 导入所有API路由
from . import tasks
from . import scripts
from . import task_workflows
from . import script_versions
from . import system_settings
from . import settings
from . import statistics

# 注册子蓝图
try:
    from .script_versions import script_versions_bp
    api_bp.register_blueprint(script_versions_bp)
except ImportError:
    pass

try:
    from .system_settings import system_settings_bp
    api_bp.register_blueprint(system_settings_bp)
except ImportError:
    pass

try:
    from .task_workflows import task_workflows_bp
    api_bp.register_blueprint(task_workflows_bp)
except ImportError:
    pass