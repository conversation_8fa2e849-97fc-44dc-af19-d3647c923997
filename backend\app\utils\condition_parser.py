"""
条件表达式解析器
"""
import re
import ast
import operator
from typing import Any, Dict, List, Union
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ConditionOperator(Enum):
    """条件操作符"""
    EQ = "=="      # 等于
    NE = "!="      # 不等于
    GT = ">"       # 大于
    GE = ">="      # 大于等于
    LT = "<"       # 小于
    LE = "<="      # 小于等于
    AND = "and"    # 逻辑与
    OR = "or"      # 逻辑或
    NOT = "not"    # 逻辑非
    IN = "in"      # 包含
    CONTAINS = "contains"  # 字符串包含

class ConditionParser:
    """条件表达式解析器"""
    
    # 操作符映射
    OPERATORS = {
        '==': operator.eq,
        '!=': operator.ne,
        '>': operator.gt,
        '>=': operator.ge,
        '<': operator.lt,
        '<=': operator.le,
        'and': operator.and_,
        'or': operator.or_,
        'not': operator.not_,
        'in': lambda x, y: x in y,
        'contains': lambda x, y: y in x,
    }
    
    # 预定义的条件常量
    PREDEFINED_CONDITIONS = {
        'success': 'status == "success"',
        'failed': 'status == "failed"',
        'always': 'True',
        'never': 'False',
        'completed': 'status in ["success", "failed"]',
        'running': 'status == "running"',
        'pending': 'status == "pending"',
        'skipped': 'status == "skipped"'
    }
    
    def __init__(self):
        self.context = {}
    
    def parse_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """
        解析并评估条件表达式
        
        Args:
            condition: 条件表达式字符串
            context: 上下文变量字典
            
        Returns:
            bool: 条件评估结果
        """
        try:
            # 更新上下文
            self.context = context
            
            # 处理预定义条件
            if condition in self.PREDEFINED_CONDITIONS:
                condition = self.PREDEFINED_CONDITIONS[condition]
            
            # 预处理表达式
            processed_condition = self._preprocess_expression(condition)
            
            # 安全评估表达式
            result = self._safe_eval(processed_condition)
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"条件表达式解析失败: {condition}, 错误: {str(e)}")
            return False
    
    def _preprocess_expression(self, expression: str) -> str:
        """预处理表达式，替换变量和函数"""
        # 替换常见的变量引用 - 直接使用变量名而不是context.get()
        expression = re.sub(r'\bstatus\b', 'status', expression)
        expression = re.sub(r'\boutput\b', 'output', expression)
        expression = re.sub(r'\berror_message\b', 'error_message', expression)
        expression = re.sub(r'\bduration\b', 'duration', expression)
        expression = re.sub(r'\breturn_code\b', 'return_code', expression)

        # 替换contains操作符
        expression = re.sub(r'(\w+)\s+contains\s+"([^"]+)"', r'"\2" in \1', expression)
        expression = re.sub(r'(\w+)\s+contains\s+\'([^\']+)\'', r"'\2' in \1", expression)

        # 替换自定义函数
        expression = re.sub(r'\blen\(([^)]+)\)', r'len(\1)', expression)
        expression = re.sub(r'\bstr\(([^)]+)\)', r'str(\1)', expression)
        expression = re.sub(r'\bint\(([^)]+)\)', r'int(\1)', expression)
        expression = re.sub(r'\bfloat\(([^)]+)\)', r'float(\1)', expression)

        return expression
    
    def _safe_eval(self, expression: str) -> Any:
        """安全地评估表达式"""
        # 定义允许的名称和函数
        safe_names = {
            'True': True,
            'False': False,
            'None': None,
            'len': len,
            'str': str,
            'int': int,
            'float': float,
            'bool': bool,
            'abs': abs,
            'min': min,
            'max': max,
            'sum': sum,
            'any': any,
            'all': all,
            # 直接添加上下文变量
            'status': self.context.get('status', ''),
            'output': self.context.get('output', ''),
            'error_message': self.context.get('error_message', ''),
            'duration': self.context.get('duration', 0),
            'return_code': self.context.get('return_code', 0),
        }
        
        # 定义允许的操作符
        safe_operators = {
            ast.Add: operator.add,
            ast.Sub: operator.sub,
            ast.Mult: operator.mul,
            ast.Div: operator.truediv,
            ast.Mod: operator.mod,
            ast.Pow: operator.pow,
            ast.LShift: operator.lshift,
            ast.RShift: operator.rshift,
            ast.BitOr: operator.or_,
            ast.BitXor: operator.xor,
            ast.BitAnd: operator.and_,
            ast.FloorDiv: operator.floordiv,
            ast.Eq: operator.eq,
            ast.NotEq: operator.ne,
            ast.Lt: operator.lt,
            ast.LtE: operator.le,
            ast.Gt: operator.gt,
            ast.GtE: operator.ge,
            ast.Is: operator.is_,
            ast.IsNot: operator.is_not,
            ast.In: lambda x, y: x in y,
            ast.NotIn: lambda x, y: x not in y,
            ast.And: lambda x, y: x and y,
            ast.Or: lambda x, y: x or y,
            ast.Not: operator.not_,
            ast.UAdd: operator.pos,
            ast.USub: operator.neg,
        }
        
        def _eval_node(node):
            if isinstance(node, ast.Constant):
                return node.value
            elif isinstance(node, ast.Name):
                if node.id in safe_names:
                    return safe_names[node.id]
                else:
                    raise ValueError(f"不允许的变量名: {node.id}")
            elif isinstance(node, ast.BinOp):
                left = _eval_node(node.left)
                right = _eval_node(node.right)
                op = safe_operators.get(type(node.op))
                if op:
                    return op(left, right)
                else:
                    raise ValueError(f"不允许的二元操作符: {type(node.op)}")
            elif isinstance(node, ast.UnaryOp):
                operand = _eval_node(node.operand)
                op = safe_operators.get(type(node.op))
                if op:
                    return op(operand)
                else:
                    raise ValueError(f"不允许的一元操作符: {type(node.op)}")
            elif isinstance(node, ast.Compare):
                left = _eval_node(node.left)
                for op, comparator in zip(node.ops, node.comparators):
                    right = _eval_node(comparator)
                    op_func = safe_operators.get(type(op))
                    if op_func:
                        if not op_func(left, right):
                            return False
                        left = right
                    else:
                        raise ValueError(f"不允许的比较操作符: {type(op)}")
                return True
            elif isinstance(node, ast.BoolOp):
                values = [_eval_node(value) for value in node.values]
                if isinstance(node.op, ast.And):
                    return all(values)
                elif isinstance(node.op, ast.Or):
                    return any(values)
                else:
                    raise ValueError(f"不允许的布尔操作符: {type(node.op)}")
            elif isinstance(node, ast.Call):
                func = _eval_node(node.func)
                args = [_eval_node(arg) for arg in node.args]
                kwargs = {kw.arg: _eval_node(kw.value) for kw in node.keywords}
                if callable(func) and func in safe_names.values():
                    return func(*args, **kwargs)
                else:
                    raise ValueError(f"不允许的函数调用: {func}")
            elif isinstance(node, ast.Attribute):
                value = _eval_node(node.value)
                if hasattr(value, node.attr):
                    attr = getattr(value, node.attr)
                    if callable(attr) and node.attr in ['get', 'keys', 'values', 'items']:
                        return attr
                    elif not callable(attr):
                        return attr
                    else:
                        raise ValueError(f"不允许的方法调用: {node.attr}")
                else:
                    raise ValueError(f"属性不存在: {node.attr}")
            elif isinstance(node, ast.Subscript):
                value = _eval_node(node.value)
                slice_value = _eval_node(node.slice)
                return value[slice_value]
            elif isinstance(node, ast.List):
                return [_eval_node(elt) for elt in node.elts]
            elif isinstance(node, ast.Tuple):
                return tuple(_eval_node(elt) for elt in node.elts)
            elif isinstance(node, ast.Dict):
                keys = [_eval_node(k) for k in node.keys]
                values = [_eval_node(v) for v in node.values]
                return dict(zip(keys, values))
            else:
                raise ValueError(f"不支持的AST节点类型: {type(node)}")
        
        try:
            # 解析表达式为AST
            tree = ast.parse(expression, mode='eval')
            # 评估AST
            return _eval_node(tree.body)
        except Exception as e:
            logger.error(f"表达式评估失败: {expression}, 错误: {str(e)}")
            raise
    
    def validate_condition(self, condition: str) -> tuple[bool, str]:
        """
        验证条件表达式的语法
        
        Args:
            condition: 条件表达式字符串
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            # 处理预定义条件
            if condition in self.PREDEFINED_CONDITIONS:
                return True, "预定义条件有效"
            
            # 预处理表达式
            processed_condition = self._preprocess_expression(condition)
            
            # 尝试解析AST
            ast.parse(processed_condition, mode='eval')
            
            return True, "条件表达式语法有效"
            
        except SyntaxError as e:
            return False, f"语法错误: {str(e)}"
        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def get_supported_conditions(self) -> Dict[str, str]:
        """获取支持的条件类型"""
        return {
            "预定义条件": list(self.PREDEFINED_CONDITIONS.keys()),
            "变量": ["status", "output", "error_message", "duration", "return_code"],
            "操作符": ["==", "!=", ">", ">=", "<", "<=", "and", "or", "not", "in", "contains"],
            "函数": ["len", "str", "int", "float", "bool", "abs", "min", "max"],
            "示例": [
                'status == "success"',
                'duration > 60',
                'output contains "error"',
                'status == "success" and duration < 30',
                'return_code != 0 or error_message != ""'
            ]
        }
