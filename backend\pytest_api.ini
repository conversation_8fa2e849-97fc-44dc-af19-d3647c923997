[tool:pytest]
# API测试专用配置

# 测试路径
testpaths = tests

# 测试文件模式
python_files = test_api_*.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 默认选项
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=app.api
    --cov=app.models
    --cov=app.schemas
    --cov-report=html:htmlcov_api
    --cov-report=term-missing
    --cov-fail-under=80
    --maxfail=10
    --durations=10

# 测试标记
markers =
    api: API接口测试
    workflow: 工作流相关测试
    performance: 性能测试
    integration: 集成测试
    slow: 慢速测试 (运行时间 > 1秒)
    unit: 单元测试
    smoke: 冒烟测试
    regression: 回归测试
    security: 安全测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:flask_sqlalchemy
    ignore::UserWarning:sqlalchemy
    ignore::pytest.PytestUnraisableExceptionWarning

# 最小版本要求
minversion = 6.0

# 测试发现
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    htmlcov*
    reports

# 日志配置
log_cli = false
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests/logs/api_tests.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 并行测试配置 (如果安装了pytest-xdist)
# addopts = -n auto

# JUnit XML报告 (用于CI/CD)
# junit_family = xunit2
# junit_logging = all

# HTML报告配置 (如果安装了pytest-html)
# --html=reports/api_test_report.html
# --self-contained-html
