"""
简化的测试配置文件
"""
import os
import sys
import tempfile
import pytest
from sqlalchemy import create_engine, Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 创建基础模型
Base = declarative_base()

class Task(Base):
    __tablename__ = 'tasks'
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    cron_expression = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Script(Base):
    __tablename__ = 'scripts'
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    type = Column(String(50), nullable=False)
    content = Column(Text)
    version = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)

class TaskWorkflow(Base):
    __tablename__ = 'task_workflows'
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    workflow_definition = Column(Text)  # JSON字符串
    created_at = Column(DateTime, default=datetime.utcnow)

@pytest.fixture(scope='session')
def test_engine():
    """创建测试数据库引擎"""
    # 使用内存数据库
    engine = create_engine('sqlite:///:memory:', echo=False)
    Base.metadata.create_all(engine)
    return engine

@pytest.fixture
def db_session(test_engine):
    """创建数据库会话"""
    Session = sessionmaker(bind=test_engine)
    session = Session()
    
    yield session
    
    # 清理
    session.rollback()
    session.close()

@pytest.fixture
def sample_task_data():
    """示例任务数据"""
    return {
        'name': '测试任务',
        'description': '这是一个测试任务',
        'cron_expression': '0 0 * * *',
        'is_active': True
    }

@pytest.fixture
def sample_script_data():
    """示例脚本数据"""
    return {
        'name': '测试脚本',
        'description': '这是一个测试脚本',
        'type': 'python',
        'content': 'print("Hello, World!")',
        'version': '1.0.0'
    }

@pytest.fixture
def sample_workflow_data():
    """示例工作流数据"""
    return {
        'name': '测试工作流',
        'description': '这是一个测试工作流',
        'workflow_definition': {
            'nodes': [
                {
                    'id': 'task1',
                    'name': '任务1',
                    'task_id': 1,
                    'execution_type': 'serial'
                },
                {
                    'id': 'task2',
                    'name': '任务2',
                    'task_id': 2,
                    'execution_type': 'parallel'
                }
            ],
            'edges': [
                {
                    'source': 'task1',
                    'target': 'task2',
                    'condition': 'success'
                }
            ]
        }
    }

class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    def create_task(session, **kwargs):
        """创建测试任务"""
        default_data = {
            'name': '测试任务',
            'description': '测试描述',
            'cron_expression': '0 0 * * *',
            'is_active': True
        }
        default_data.update(kwargs)
        
        task = Task(**default_data)
        session.add(task)
        session.commit()
        session.refresh(task)
        return task
    
    @staticmethod
    def create_script(session, **kwargs):
        """创建测试脚本"""
        default_data = {
            'name': '测试脚本',
            'description': '测试描述',
            'type': 'python',
            'content': 'print("test")',
            'version': '1.0.0'
        }
        default_data.update(kwargs)
        
        script = Script(**default_data)
        session.add(script)
        session.commit()
        session.refresh(script)
        return script
    
    @staticmethod
    def create_workflow(session, **kwargs):
        """创建测试工作流"""
        import json
        
        default_data = {
            'name': '测试工作流',
            'description': '测试描述',
            'workflow_definition': json.dumps({
                'nodes': [],
                'edges': []
            })
        }
        default_data.update(kwargs)
        
        # 如果workflow_definition是字典，转换为JSON字符串
        if isinstance(default_data['workflow_definition'], dict):
            default_data['workflow_definition'] = json.dumps(default_data['workflow_definition'])
        
        workflow = TaskWorkflow(**default_data)
        session.add(workflow)
        session.commit()
        session.refresh(workflow)
        return workflow

@pytest.fixture
def test_factory():
    """测试数据工厂实例"""
    return TestDataFactory

@pytest.fixture(autouse=True)
def clean_database(db_session):
    """自动清理数据库"""
    yield
    # 测试后清理所有表
    for table in reversed(Base.metadata.sorted_tables):
        db_session.execute(table.delete())
    db_session.commit()
