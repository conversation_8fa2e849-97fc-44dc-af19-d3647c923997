{"ast": null, "code": "/** Scale the ratio base */\nexport var BASE_SCALE_RATIO = 1;\n/** The maximum zoom ratio when the mouse zooms in, adjustable */\nexport var WHEEL_MAX_SCALE_RATIO = 1;", "map": {"version": 3, "names": ["BASE_SCALE_RATIO", "WHEEL_MAX_SCALE_RATIO"], "sources": ["E:/code1/task3/frontend/node_modules/rc-image/es/previewConfig.js"], "sourcesContent": ["/** Scale the ratio base */\nexport var BASE_SCALE_RATIO = 1;\n/** The maximum zoom ratio when the mouse zooms in, adjustable */\nexport var WHEEL_MAX_SCALE_RATIO = 1;"], "mappings": "AAAA;AACA,OAAO,IAAIA,gBAAgB,GAAG,CAAC;AAC/B;AACA,OAAO,IAAIC,qBAAqB,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}