from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, func
from sqlalchemy.orm import relationship
from app.database import Base

class WorkflowTask(Base):
    """工作流任务关系表"""
    __tablename__ = 'workflow_tasks'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    workflow_id = Column(Integer, ForeignKey('task_workflows.id'), nullable=False)
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False)
    dependency_task_id = Column(Integer, ForeignKey('tasks.id'), nullable=True)  # 依赖的任务ID
    execution_order = Column(Integer, default=0)  # 执行顺序
    condition_expression = Column(Text)  # 条件表达式
    node_id = Column(String(50))  # 工作流中的节点ID
    node_config = Column(JSON)  # 节点配置信息
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    workflow = relationship("TaskWorkflow", backref="workflow_tasks")
    task = relationship("Task", foreign_keys=[task_id])
    dependency_task = relationship("Task", foreign_keys=[dependency_task_id])
