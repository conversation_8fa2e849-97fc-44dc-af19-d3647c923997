["simple_test.py::test_condition_parser", "simple_test.py::test_dependency_manager", "simple_test.py::test_parameter_manager", "test_api.py::test_tasks_api", "test_simple.py::test_condition_parser", "test_simple.py::test_dependency_manager", "test_simple.py::test_execution_manager", "test_simple.py::test_imports", "test_simple.py::test_parameter_manager", "test_workflow.py::test_condition_parser", "test_workflow.py::test_dependency_manager", "test_workflow.py::test_execution_manager", "test_workflow.py::test_parameter_manager", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_api_error_handling", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_create_workflow_invalid_definition", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_create_workflow_missing_name", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_create_workflow_success", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_delete_workflow", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_execute_workflow", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_condition_help", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_execution_plan", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_parameter_schema", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_workflow_by_id", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_workflow_not_found", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_workflows_empty", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_update_workflow", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_condition_invalid", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_condition_valid", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_parameters", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_workflow_invalid", "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_workflow_valid", "tests/test_condition_parser.py::TestConditionParser::test_complex_conditions", "tests/test_condition_parser.py::TestConditionParser::test_edge_cases", "tests/test_condition_parser.py::TestConditionParser::test_get_supported_conditions", "tests/test_condition_parser.py::TestConditionParser::test_invalid_conditions", "tests/test_condition_parser.py::TestConditionParser::test_logical_operators", "tests/test_condition_parser.py::TestConditionParser::test_missing_variables", "tests/test_condition_parser.py::TestConditionParser::test_numeric_operations", "tests/test_condition_parser.py::TestConditionParser::test_predefined_conditions", "tests/test_condition_parser.py::TestConditionParser::test_security_restrictions", "tests/test_condition_parser.py::TestConditionParser::test_simple_comparisons", "tests/test_condition_parser.py::TestConditionParser::test_string_operations", "tests/test_condition_parser.py::TestConditionParser::test_type_conversions", "tests/test_condition_parser.py::TestConditionParser::test_validate_condition_invalid", "tests/test_condition_parser.py::TestConditionParser::test_validate_condition_valid", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_complex_logical_expressions", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_condition_validation_comprehensive", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_error_handling_edge_cases", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_list_operations", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_mathematical_operations", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_nested_conditions", "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_string_methods", "tests/test_core_simple.py::TestCoreFeatures::test_condition_parser_basic", "tests/test_core_simple.py::TestCoreFeatures::test_dependency_manager_basic", "tests/test_core_simple.py::TestCoreFeatures::test_execution_manager_basic", "tests/test_core_simple.py::TestCoreFeatures::test_parameter_manager_basic", "tests/test_dependency_manager.py::TestDependencyManager::test_add_dependency", "tests/test_dependency_manager.py::TestDependencyManager::test_can_add_dependency_safe", "tests/test_dependency_manager.py::TestDependencyManager::test_complex_workflow", "tests/test_dependency_manager.py::TestDependencyManager::test_empty_graph", "tests/test_dependency_manager.py::TestDependencyManager::test_find_cycle", "tests/test_dependency_manager.py::TestDependencyManager::test_find_cycle_no_cycle", "tests/test_dependency_manager.py::TestDependencyManager::test_get_all_dependencies", "tests/test_dependency_manager.py::TestDependencyManager::test_get_all_dependents", "tests/test_dependency_manager.py::TestDependencyManager::test_get_dependencies", "tests/test_dependency_manager.py::TestDependencyManager::test_get_dependents", "tests/test_dependency_manager.py::TestDependencyManager::test_get_execution_plan_invalid", "tests/test_dependency_manager.py::TestDependencyManager::test_get_execution_plan_valid", "tests/test_dependency_manager.py::TestDependencyManager::test_has_cycle_no_cycle", "tests/test_dependency_manager.py::TestDependencyManager::test_has_cycle_self_dependency", "tests/test_dependency_manager.py::TestDependencyManager::test_has_cycle_with_cycle", "tests/test_dependency_manager.py::TestDependencyManager::test_remove_dependency", "tests/test_dependency_manager.py::TestDependencyManager::test_topological_sort_parallel", "tests/test_dependency_manager.py::TestDependencyManager::test_topological_sort_simple", "tests/test_dependency_manager.py::TestDependencyManager::test_topological_sort_with_cycle", "tests/test_dependency_manager.py::TestDependencyManager::test_validate_workflow_cycle", "tests/test_dependency_manager.py::TestDependencyManager::test_validate_workflow_missing_node", "tests/test_dependency_manager.py::TestDependencyManager::test_validate_workflow_valid", "tests/test_execution_manager.py::TestExecutionManager::test_add_status_callback", "tests/test_execution_manager.py::TestExecutionManager::test_cancel_task", "tests/test_execution_manager.py::TestExecutionManager::test_cancel_task_not_found", "tests/test_execution_manager.py::TestExecutionManager::test_cleanup", "tests/test_execution_manager.py::TestExecutionManager::test_complex_workflow_execution", "tests/test_execution_manager.py::TestExecutionManager::test_condition_evaluation_in_workflow", "tests/test_execution_manager.py::TestExecutionManager::test_execute_parallel_tasks", "tests/test_execution_manager.py::TestExecutionManager::test_execute_single_task_failure", "tests/test_execution_manager.py::TestExecutionManager::test_execute_single_task_success", "tests/test_execution_manager.py::TestExecutionManager::test_execute_workflow_simple", "tests/test_execution_manager.py::TestExecutionManager::test_execute_workflow_with_failure", "tests/test_execution_manager.py::TestExecutionManager::test_get_task_result", "tests/test_execution_manager.py::TestExecutionManager::test_get_task_status", "tests/test_execution_manager.py::TestExecutionManager::test_initialization", "tests/test_execution_manager.py::TestExecutionManager::test_notify_status_change", "tests/test_execution_manager.py::TestExecutionManager::test_should_execute_node_always_condition", "tests/test_execution_manager.py::TestExecutionManager::test_should_execute_node_no_dependencies", "tests/test_execution_manager.py::TestExecutionManager::test_should_execute_node_with_dependencies_failed", "tests/test_execution_manager.py::TestExecutionManager::test_should_execute_node_with_dependencies_success", "tests/test_integration.py::TestConditionIntegration::test_condition_help_api", "tests/test_integration.py::TestConditionIntegration::test_condition_validation_api", "tests/test_integration.py::TestParameterIntegration::test_parameter_schema_api", "tests/test_integration.py::TestParameterIntegration::test_parameter_validation_api", "tests/test_integration.py::TestPerformanceIntegration::test_concurrent_workflow_operations", "tests/test_integration.py::TestPerformanceIntegration::test_large_workflow_validation", "tests/test_integration.py::TestWorkflowIntegration::test_complete_workflow_lifecycle", "tests/test_integration.py::TestWorkflowIntegration::test_workflow_with_conditions", "tests/test_integration.py::TestWorkflowIntegration::test_workflow_with_parallel_execution", "tests/test_models.py::TestModelIntegration::test_complete_workflow_setup", "tests/test_models.py::TestScriptModel::test_create_script", "tests/test_models.py::TestScriptModel::test_script_name_required", "tests/test_models.py::TestScriptModel::test_script_type_required", "tests/test_models.py::TestTaskExecutionModel::test_create_execution", "tests/test_models.py::TestTaskExecutionModel::test_execution_duration_calculation", "tests/test_models.py::TestTaskExecutionModel::test_execution_task_required", "tests/test_models.py::TestTaskModel::test_create_task", "tests/test_models.py::TestTaskModel::test_task_executions_relationship", "tests/test_models.py::TestTaskModel::test_task_name_required", "tests/test_models.py::TestTaskModel::test_task_script_relationship", "tests/test_models.py::TestTaskWorkflowExecutionModel::test_create_workflow_execution", "tests/test_models.py::TestTaskWorkflowExecutionModel::test_workflow_execution_workflow_required", "tests/test_models.py::TestTaskWorkflowModel::test_create_workflow", "tests/test_models.py::TestTaskWorkflowModel::test_workflow_executions_relationship", "tests/test_models.py::TestTaskWorkflowModel::test_workflow_name_required", "tests/test_models.py::TestWorkflowTaskModel::test_create_workflow_task", "tests/test_models.py::TestWorkflowTaskModel::test_workflow_task_relationships", "tests/test_parameter_manager.py::TestParameterManager::test_apply_transform", "tests/test_parameter_manager.py::TestParameterManager::test_convert_parameter_boolean", "tests/test_parameter_manager.py::TestParameterManager::test_convert_parameter_float", "tests/test_parameter_manager.py::TestParameterManager::test_convert_parameter_integer", "tests/test_parameter_manager.py::TestParameterManager::test_convert_parameter_json", "tests/test_parameter_manager.py::TestParameterManager::test_convert_parameter_list", "tests/test_parameter_manager.py::TestParameterManager::test_convert_parameter_string", "tests/test_parameter_manager.py::TestParameterManager::test_create_parameter_mapping", "tests/test_parameter_manager.py::TestParameterManager::test_edge_cases", "tests/test_parameter_manager.py::TestParameterManager::test_extract_parameters_from_output", "tests/test_parameter_manager.py::TestParameterManager::test_get_parameter_schema", "tests/test_parameter_manager.py::TestParameterManager::test_resolve_parameter_references", "tests/test_parameter_manager.py::TestParameterManager::test_resolve_parameter_references_missing_variable", "tests/test_parameter_manager.py::TestParameterManager::test_validate_parameter_enum", "tests/test_parameter_manager.py::TestParameterManager::test_validate_parameter_numeric_range", "tests/test_parameter_manager.py::TestParameterManager::test_validate_parameter_pattern", "tests/test_parameter_manager.py::TestParameterManager::test_validate_parameter_required", "tests/test_parameter_manager.py::TestParameterManager::test_validate_parameter_string_length", "tests/test_parameter_manager_additional.py::TestParameterManagerAdditional::test_json_parameter_complex", "tests/test_parameter_manager_additional.py::TestParameterManagerAdditional::test_list_parameter_various_formats", "tests/test_parameter_manager_additional.py::TestParameterManagerAdditional::test_output_parameter_extraction", "tests/test_parameter_manager_additional.py::TestParameterManagerAdditional::test_parameter_reference_resolution_complex", "tests/test_parameter_manager_additional.py::TestParameterManagerAdditional::test_parameter_schema_completeness", "tests/test_parameter_manager_additional.py::TestParameterManagerAdditional::test_parameter_validation_comprehensive", "tests/test_script_versions.py::TestScriptVersions::test_create_script_version", "tests/test_script_versions.py::TestScriptVersions::test_get_script_versions", "tests/test_script_versions.py::TestScriptVersions::test_version_comparison", "tests/test_script_versions.py::TestScriptVersions::test_version_rollback", "tests/test_security.py::TestSecurity::test_limit_execution_time_decorator", "tests/test_security.py::TestSecurity::test_limit_execution_time_no_timeout", "tests/test_security.py::TestSecurity::test_validate_file_path_empty_allowed_dirs", "tests/test_security.py::TestSecurity::test_validate_file_path_invalid", "tests/test_security.py::TestSecurity::test_validate_file_path_valid", "tests/test_security_additional.py::TestSecurityAdditional::test_limit_execution_time_decorator", "tests/test_security_additional.py::TestSecurityAdditional::test_limit_execution_time_long_running", "tests/test_security_additional.py::TestSecurityAdditional::test_limit_memory_usage_function", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_absolute", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_dangerous", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_edge_cases", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_none", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_relative", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_unicode", "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_with_spaces", "tests/test_system_settings.py::TestSystemSettings::test_create_system_setting", "tests/test_system_settings.py::TestSystemSettings::test_get_system_setting_by_key", "tests/test_system_settings.py::TestSystemSettings::test_unique_key_constraint", "tests/test_system_settings.py::TestSystemSettings::test_update_system_setting", "tests/test_task_workflows.py::TestTaskWorkflows::test_create_workflow", "tests/test_task_workflows.py::TestTaskWorkflows::test_create_workflow_execution", "tests/test_task_workflows.py::TestTaskWorkflows::test_update_workflow", "tests/test_task_workflows.py::TestTaskWorkflows::test_workflow_execution_status_transition"]