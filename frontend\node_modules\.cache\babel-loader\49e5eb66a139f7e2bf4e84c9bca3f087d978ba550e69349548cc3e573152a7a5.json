{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DiffOutlinedSvg from \"@ant-design/icons-svg/es/asn/DiffOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DiffOutlined = function DiffOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DiffOutlinedSvg\n  }));\n};\n\n/**![diff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3NiAzOTkuMWMwLTMuOS0zLjEtNy4xLTctNy4xaC00MmMtMy44IDAtNyAzLjItNyA3LjFWNDg0aC04NC41Yy00LjEgMC03LjUgMy4xLTcuNSA3djQyYzAgMy44IDMuNCA3IDcuNSA3SDQyMHY4NC45YzAgMy45IDMuMiA3LjEgNyA3LjFoNDJjMy45IDAgNy0zLjIgNy03LjFWNTQwaDg0LjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjktMy40LTctNy41LTdINDc2di04NC45ek01NjAuNSA3MDRoLTIyNWMtNC4xIDAtNy41IDMuMi03LjUgN3Y0MmMwIDMuOCAzLjQgNyA3LjUgN2gyMjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjgtMy40LTctNy41LTd6bS03LjEtNTAyLjZjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjcwNGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg1MTJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzk3LjNjMC04LjUtMy40LTE2LjYtOS40LTIyLjZMNTUzLjQgMjAxLjR6TTY2NCA4ODhIMjMyVjI2NGgyODIuMkw2NjQgNDEzLjhWODg4em0xOTAuMi01ODEuNEw2MTEuMyA3Mi45Yy02LTUuNy0xMy45LTguOS0yMi4yLTguOUgyOTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoMjc3bDIxOSAyMTAuNlY4MjRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYzMjkuNmMwLTguNy0zLjUtMTctOS44LTIzeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DiffOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DiffOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "DiffOutlinedSvg", "AntdIcon", "DiffOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["E:/code1/task3/frontend/node_modules/@ant-design/icons/es/icons/DiffOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DiffOutlinedSvg from \"@ant-design/icons-svg/es/asn/DiffOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DiffOutlined = function DiffOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DiffOutlinedSvg\n  }));\n};\n\n/**![diff](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ3NiAzOTkuMWMwLTMuOS0zLjEtNy4xLTctNy4xaC00MmMtMy44IDAtNyAzLjItNyA3LjFWNDg0aC04NC41Yy00LjEgMC03LjUgMy4xLTcuNSA3djQyYzAgMy44IDMuNCA3IDcuNSA3SDQyMHY4NC45YzAgMy45IDMuMiA3LjEgNyA3LjFoNDJjMy45IDAgNy0zLjIgNy03LjFWNTQwaDg0LjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjktMy40LTctNy41LTdINDc2di04NC45ek01NjAuNSA3MDRoLTIyNWMtNC4xIDAtNy41IDMuMi03LjUgN3Y0MmMwIDMuOCAzLjQgNyA3LjUgN2gyMjVjNC4xIDAgNy41LTMuMiA3LjUtN3YtNDJjMC0zLjgtMy40LTctNy41LTd6bS03LjEtNTAyLjZjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjcwNGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg1MTJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzk3LjNjMC04LjUtMy40LTE2LjYtOS40LTIyLjZMNTUzLjQgMjAxLjR6TTY2NCA4ODhIMjMyVjI2NGgyODIuMkw2NjQgNDEzLjhWODg4em0xOTAuMi01ODEuNEw2MTEuMyA3Mi45Yy02LTUuNy0xMy45LTguOS0yMi4yLTguOUgyOTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoMjc3bDIxOSAyMTAuNlY4MjRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYzMjkuNmMwLTguNy0zLjUtMTctOS44LTIzeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DiffOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DiffOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,YAAY,CAAC;AACzD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,cAAc;AACtC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}