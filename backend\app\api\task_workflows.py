from flask import Blueprint, request, jsonify
from app.database import get_db
from app.models.task_workflow import TaskWorkflow, TaskWorkflowExecution
from app.utils.dependency_manager import DependencyManager
from app.utils.condition_parser import ConditionParser
from app.utils.parameter_manager import ParameterManager
import json
import logging
from datetime import datetime

task_workflows_bp = Blueprint('task_workflows', __name__)
logger = logging.getLogger(__name__)

@task_workflows_bp.route('/workflows', methods=['GET'])
def get_workflows():
    """获取所有工作流"""
    try:
        db = next(get_db())
        workflows = db.query(TaskWorkflow).all()
        
        result = []
        for workflow in workflows:
            result.append({
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'created_at': workflow.created_at.isoformat() if workflow.created_at else None,
                'updated_at': workflow.updated_at.isoformat() if workflow.updated_at else None
            })
        
        return jsonify({
            'code': 200,
            'data': result,
            'message': '获取工作流列表成功'
        })
    except Exception as e:
        logger.error(f'获取工作流列表失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'获取工作流列表失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows', methods=['POST'])
def create_workflow():
    """创建工作流"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空'
            }), 400
        
        name = data.get('name')
        description = data.get('description')
        workflow_definition = data.get('workflow_definition')

        if not name:
            return jsonify({
                'code': 400,
                'message': '工作流名称不能为空'
            }), 400

        # 验证工作流定义
        if workflow_definition:
            nodes = workflow_definition.get('nodes', [])
            edges = workflow_definition.get('edges', [])

            dependency_manager = DependencyManager()
            is_valid, message = dependency_manager.validate_workflow(nodes, edges)

            if not is_valid:
                return jsonify({
                    'code': 400,
                    'message': f'工作流定义无效: {message}'
                }), 400
        
        db = next(get_db())
        
        # 创建工作流
        workflow = TaskWorkflow(
            name=name,
            description=description,
            workflow_definition=workflow_definition
        )
        
        db.add(workflow)
        db.commit()
        db.refresh(workflow)
        
        return jsonify({
            'code': 200,
            'data': {
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'workflow_definition': workflow.workflow_definition,
                'created_at': workflow.created_at.isoformat() if workflow.created_at else None,
                'updated_at': workflow.updated_at.isoformat() if workflow.updated_at else None
            },
            'message': '创建工作流成功'
        })
    except Exception as e:
        db.rollback()
        logger.error(f'创建工作流失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'创建工作流失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows/<int:workflow_id>', methods=['GET'])
def get_workflow(workflow_id):
    """获取单个工作流详情"""
    try:
        db = next(get_db())
        workflow = db.query(TaskWorkflow).filter(TaskWorkflow.id == workflow_id).first()
        
        if not workflow:
            return jsonify({
                'code': 404,
                'message': '工作流不存在'
            }), 404
        
        return jsonify({
            'code': 200,
            'data': {
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'workflow_definition': workflow.workflow_definition,
                'created_at': workflow.created_at.isoformat() if workflow.created_at else None,
                'updated_at': workflow.updated_at.isoformat() if workflow.updated_at else None
            },
            'message': '获取工作流详情成功'
        })
    except Exception as e:
        logger.error(f'获取工作流详情失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'获取工作流详情失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows/<int:workflow_id>', methods=['PUT'])
def update_workflow(workflow_id):
    """更新工作流"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空'
            }), 400
        
        db = next(get_db())
        workflow = db.query(TaskWorkflow).filter(TaskWorkflow.id == workflow_id).first()
        
        if not workflow:
            return jsonify({
                'code': 404,
                'message': '工作流不存在'
            }), 404
        
        # 更新工作流
        if 'name' in data:
            workflow.name = data['name']
        if 'description' in data:
            workflow.description = data['description']
        if 'workflow_definition' in data:
            workflow.workflow_definition = data['workflow_definition']
        
        workflow.updated_at = datetime.utcnow()
        db.commit()
        
        return jsonify({
            'code': 200,
            'data': {
                'id': workflow.id,
                'name': workflow.name,
                'description': workflow.description,
                'workflow_definition': workflow.workflow_definition,
                'created_at': workflow.created_at.isoformat() if workflow.created_at else None,
                'updated_at': workflow.updated_at.isoformat() if workflow.updated_at else None
            },
            'message': '更新工作流成功'
        })
    except Exception as e:
        db.rollback()
        logger.error(f'更新工作流失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'更新工作流失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows/<int:workflow_id>', methods=['DELETE'])
def delete_workflow(workflow_id):
    """删除工作流"""
    try:
        db = next(get_db())
        workflow = db.query(TaskWorkflow).filter(TaskWorkflow.id == workflow_id).first()
        
        if not workflow:
            return jsonify({
                'code': 404,
                'message': '工作流不存在'
            }), 404
        
        db.delete(workflow)
        db.commit()
        
        return jsonify({
            'code': 200,
            'message': '删除工作流成功'
        })
    except Exception as e:
        db.rollback()
        logger.error(f'删除工作流失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'删除工作流失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows/<int:workflow_id>/execute', methods=['POST'])
def execute_workflow(workflow_id):
    """执行工作流"""
    try:
        db = next(get_db())
        workflow = db.query(TaskWorkflow).filter(TaskWorkflow.id == workflow_id).first()
        
        if not workflow:
            return jsonify({
                'code': 404,
                'message': '工作流不存在'
            }), 404
        
        # 创建执行记录
        execution = TaskWorkflowExecution(
            workflow_id=workflow.id,
            status='running',
            start_time=datetime.utcnow()
        )
        db.add(execution)
        db.commit()
        db.refresh(execution)
        
        # 使用工作流执行引擎执行工作流
        try:
            # 获取调度器实例
            from flask import current_app
            if hasattr(current_app, 'scheduler'):
                # 在后台线程中执行工作流
                import threading
                thread = threading.Thread(
                    target=current_app.scheduler.execute_workflow,
                    args=(workflow.id,)
                )
                thread.daemon = True
                thread.start()

                # 立即返回执行ID，实际执行在后台进行
                return jsonify({
                    'code': 200,
                    'data': {
                        'execution_id': execution.id,
                        'status': 'running',
                        'message': '工作流已开始执行'
                    },
                    'message': '启动工作流执行成功'
                })
            else:
                raise Exception("调度器未初始化")

        except Exception as e:
            # 更新执行记录
            execution.status = 'failed'
            execution.error_message = str(e)
            execution.end_time = datetime.utcnow()
            execution.duration = (execution.end_time - execution.start_time).total_seconds()
            db.commit()
        
        return jsonify({
            'code': 200,
            'data': {
                'execution_id': execution.id,
                'status': execution.status,
                'output': execution.output,
                'error_message': execution.error_message,
                'duration': execution.duration
            },
            'message': '执行工作流成功'
        })
    except Exception as e:
        db.rollback()
        logger.error(f'执行工作流失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'执行工作流失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows/validate', methods=['POST'])
def validate_workflow():
    """验证工作流定义"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空'
            }), 400

        workflow_definition = data.get('workflow_definition')
        if not workflow_definition:
            return jsonify({
                'code': 400,
                'message': '工作流定义不能为空'
            }), 400

        nodes = workflow_definition.get('nodes', [])
        edges = workflow_definition.get('edges', [])

        dependency_manager = DependencyManager()
        is_valid, message = dependency_manager.validate_workflow(nodes, edges)

        if is_valid:
            # 获取执行计划
            execution_plan = dependency_manager.get_execution_plan(nodes, edges)
            return jsonify({
                'code': 200,
                'data': {
                    'valid': True,
                    'message': message,
                    'execution_plan': execution_plan
                },
                'message': '工作流验证成功'
            })
        else:
            return jsonify({
                'code': 400,
                'data': {
                    'valid': False,
                    'message': message
                },
                'message': '工作流验证失败'
            })

    except Exception as e:
        logger.error(f'验证工作流失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'验证工作流失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/workflows/<int:workflow_id>/execution-plan', methods=['GET'])
def get_execution_plan(workflow_id):
    """获取工作流执行计划"""
    try:
        db = next(get_db())
        workflow = db.query(TaskWorkflow).filter(TaskWorkflow.id == workflow_id).first()

        if not workflow:
            return jsonify({
                'code': 404,
                'message': '工作流不存在'
            }), 404

        workflow_definition = workflow.workflow_definition or {}
        nodes = workflow_definition.get('nodes', [])
        edges = workflow_definition.get('edges', [])

        dependency_manager = DependencyManager()
        execution_plan = dependency_manager.get_execution_plan(nodes, edges)

        return jsonify({
            'code': 200,
            'data': execution_plan,
            'message': '获取执行计划成功'
        })

    except Exception as e:
        logger.error(f'获取执行计划失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'获取执行计划失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/conditions/validate', methods=['POST'])
def validate_condition():
    """验证条件表达式"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空'
            }), 400

        condition = data.get('condition')
        if not condition:
            return jsonify({
                'code': 400,
                'message': '条件表达式不能为空'
            }), 400

        condition_parser = ConditionParser()
        is_valid, message = condition_parser.validate_condition(condition)

        return jsonify({
            'code': 200,
            'data': {
                'valid': is_valid,
                'message': message
            },
            'message': '条件验证完成'
        })

    except Exception as e:
        logger.error(f'验证条件表达式失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'验证条件表达式失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/conditions/help', methods=['GET'])
def get_condition_help():
    """获取条件表达式帮助信息"""
    try:
        condition_parser = ConditionParser()
        help_info = condition_parser.get_supported_conditions()

        return jsonify({
            'code': 200,
            'data': help_info,
            'message': '获取条件帮助信息成功'
        })

    except Exception as e:
        logger.error(f'获取条件帮助信息失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'获取条件帮助信息失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/parameters/validate', methods=['POST'])
def validate_parameters():
    """验证参数配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'message': '请求数据不能为空'
            }), 400

        parameters = data.get('parameters', {})
        parameter_manager = ParameterManager()

        validation_results = []
        all_valid = True

        for param_name, param_config in parameters.items():
            param_type = param_config.get('type', 'string')
            param_value = param_config.get('value')
            constraints = param_config.get('constraints', {})

            try:
                from app.utils.parameter_manager import ParameterType
                param_type_enum = ParameterType(param_type)
                is_valid, message = parameter_manager.validate_parameter(
                    param_value, param_type_enum, constraints
                )

                validation_results.append({
                    'parameter': param_name,
                    'valid': is_valid,
                    'message': message
                })

                if not is_valid:
                    all_valid = False

            except Exception as e:
                validation_results.append({
                    'parameter': param_name,
                    'valid': False,
                    'message': str(e)
                })
                all_valid = False

        return jsonify({
            'code': 200,
            'data': {
                'all_valid': all_valid,
                'results': validation_results
            },
            'message': '参数验证完成'
        })

    except Exception as e:
        logger.error(f'验证参数失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'验证参数失败: {str(e)}'
        }), 500

@task_workflows_bp.route('/parameters/schema', methods=['GET'])
def get_parameter_schema():
    """获取参数模式定义"""
    try:
        parameter_manager = ParameterManager()
        schema = parameter_manager.get_parameter_schema()

        return jsonify({
            'code': 200,
            'data': schema,
            'message': '获取参数模式成功'
        })

    except Exception as e:
        logger.error(f'获取参数模式失败: {str(e)}')
        return jsonify({
            'code': 500,
            'message': f'获取参数模式失败: {str(e)}'
        }), 500