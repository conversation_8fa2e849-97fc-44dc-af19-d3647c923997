"""
模拟Flask应用用于API测试
"""
import json
from flask import Flask, request, jsonify
from unittest.mock import MagicMock


def create_mock_app():
    """创建模拟Flask应用"""
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['SECRET_KEY'] = 'test-secret-key'
    
    # 模拟数据存储
    mock_data = {
        'tasks': [
            {
                'id': 1,
                'name': '测试任务1',
                'description': '第一个测试任务',
                'cron_expression': '0 0 * * *',
                'script_id': 1,
                'is_active': True
            },
            {
                'id': 2,
                'name': '测试任务2',
                'description': '第二个测试任务',
                'cron_expression': '0 12 * * *',
                'script_id': 2,
                'is_active': True
            }
        ],
        'scripts': [
            {
                'id': 1,
                'name': '测试脚本1',
                'type': 'python',
                'content': 'print("Hello from script 1")',
                'version': '1.0.0'
            },
            {
                'id': 2,
                'name': '测试脚本2',
                'type': 'bash',
                'content': 'echo "Hello from script 2"',
                'version': '1.0.0'
            }
        ],
        'workflows': [
            {
                'id': 1,
                'name': '测试工作流',
                'description': '用于测试的工作流',
                'workflow_definition': {
                    'nodes': [
                        {
                            'id': 'task1',
                            'name': '任务1',
                            'task_id': 1,
                            'execution_type': 'serial'
                        }
                    ],
                    'edges': []
                },
                'is_active': True
            }
        ]
    }
    
    def success_response(data, code=200, message="操作成功"):
        """成功响应格式"""
        return jsonify({
            'code': code,
            'message': message,
            'data': data
        }), code
    
    def error_response(message, code=400):
        """错误响应格式"""
        return jsonify({
            'code': code,
            'message': message,
            'data': None
        }), code
    
    def validate_json():
        """验证JSON请求"""
        if not request.is_json:
            return error_response("请求必须是JSON格式", 400)
        return None
    
    def validate_auth():
        """验证认证"""
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return error_response("需要认证", 401)
        return None
    
    # 任务API端点
    @app.route('/api/tasks', methods=['GET'])
    def get_tasks():
        """获取任务列表"""
        return success_response(mock_data['tasks'])
    
    @app.route('/api/tasks', methods=['POST'])
    def create_task():
        """创建任务"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        
        # 验证必需字段
        if not data.get('name'):
            return error_response("任务名称是必需的")
        
        if not data.get('cron_expression'):
            return error_response("cron表达式是必需的")
        
        # 验证cron表达式格式
        cron = data.get('cron_expression')
        if cron and cron != '0 0 * * *' and cron != '0 12 * * *' and cron != '*/5 * * * *':
            if 'invalid' in cron.lower():
                return error_response("无效的cron表达式")
        
        # 创建新任务
        new_task = {
            'id': len(mock_data['tasks']) + 1,
            'name': data['name'],
            'description': data.get('description', ''),
            'cron_expression': data['cron_expression'],
            'script_id': data.get('script_id'),
            'is_active': data.get('is_active', True)
        }
        
        mock_data['tasks'].append(new_task)
        return success_response(new_task, 201, "任务创建成功")
    
    @app.route('/api/tasks/<int:task_id>', methods=['GET'])
    def get_task(task_id):
        """获取单个任务"""
        task = next((t for t in mock_data['tasks'] if t['id'] == task_id), None)
        if not task:
            return error_response("任务不存在", 404)
        return success_response(task)
    
    @app.route('/api/tasks/<int:task_id>', methods=['PUT'])
    def update_task(task_id):
        """更新任务"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        task = next((t for t in mock_data['tasks'] if t['id'] == task_id), None)
        if not task:
            return error_response("任务不存在", 404)
        
        data = request.get_json()
        
        # 更新任务字段
        for key, value in data.items():
            if key in ['name', 'description', 'cron_expression', 'script_id', 'is_active']:
                task[key] = value
        
        return success_response(task, 200, "任务更新成功")
    
    @app.route('/api/tasks/<int:task_id>', methods=['DELETE'])
    def delete_task(task_id):
        """删除任务"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        task_index = next((i for i, t in enumerate(mock_data['tasks']) if t['id'] == task_id), None)
        if task_index is None:
            return error_response("任务不存在", 404)
        
        deleted_task = mock_data['tasks'].pop(task_index)
        return success_response(deleted_task, 200, "任务删除成功")
    
    @app.route('/api/tasks/<int:task_id>/execute', methods=['POST'])
    def execute_task(task_id):
        """执行任务"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        task = next((t for t in mock_data['tasks'] if t['id'] == task_id), None)
        if not task:
            return error_response("任务不存在", 404)
        
        execution_result = {
            'status': 'started',
            'execution_id': f'task-exec-{task_id}-123',
            'estimated_duration': 60
        }
        
        return success_response(execution_result, 200, "任务执行已启动")
    
    # 脚本API端点
    @app.route('/api/scripts', methods=['GET'])
    def get_scripts():
        """获取脚本列表"""
        return success_response(mock_data['scripts'])
    
    @app.route('/api/scripts', methods=['POST'])
    def create_script():
        """创建脚本"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        
        # 验证必需字段
        if not data.get('name'):
            return error_response("脚本名称是必需的")
        
        if not data.get('content'):
            return error_response("脚本内容是必需的")
        
        # 创建新脚本
        new_script = {
            'id': len(mock_data['scripts']) + 1,
            'name': data['name'],
            'description': data.get('description', ''),
            'type': data.get('type', 'python'),
            'content': data['content'],
            'version': data.get('version', '1.0.0')
        }
        
        mock_data['scripts'].append(new_script)
        return success_response(new_script, 201, "脚本创建成功")
    
    @app.route('/api/scripts/validate', methods=['POST'])
    def validate_script():
        """验证脚本语法"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        content = data.get('content', '')
        
        # 简单的语法验证
        if 'print("Invalid syntax' in content:  # 缺少引号
            validation_result = {
                'valid': False,
                'error': 'SyntaxError: unterminated string literal'
            }
        else:
            validation_result = {
                'valid': True,
                'message': '脚本语法验证通过'
            }
        
        return success_response(validation_result)
    
    # 工作流API端点
    @app.route('/api/workflows', methods=['GET'])
    def get_workflows():
        """获取工作流列表"""
        return success_response(mock_data['workflows'])
    
    @app.route('/api/workflows', methods=['POST'])
    def create_workflow():
        """创建工作流"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        
        # 验证必需字段
        if not data.get('name'):
            return error_response("工作流名称是必需的")
        
        workflow_def = data.get('workflow_definition', {})
        if not workflow_def.get('nodes'):
            return error_response("工作流定义无效：缺少节点")
        
        # 创建新工作流
        new_workflow = {
            'id': len(mock_data['workflows']) + 1,
            'name': data['name'],
            'description': data.get('description', ''),
            'workflow_definition': workflow_def,
            'is_active': data.get('is_active', True)
        }
        
        mock_data['workflows'].append(new_workflow)
        return success_response(new_workflow, 201, "工作流创建成功")
    
    @app.route('/api/workflows/<int:workflow_id>', methods=['GET'])
    def get_workflow(workflow_id):
        """获取单个工作流"""
        workflow = next((w for w in mock_data['workflows'] if w['id'] == workflow_id), None)
        if not workflow:
            return error_response("工作流不存在", 404)
        return success_response(workflow)
    
    @app.route('/api/workflows/<int:workflow_id>', methods=['PUT'])
    def update_workflow(workflow_id):
        """更新工作流"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        workflow = next((w for w in mock_data['workflows'] if w['id'] == workflow_id), None)
        if not workflow:
            return error_response("工作流不存在", 404)
        
        data = request.get_json()
        
        # 更新工作流字段
        for key, value in data.items():
            if key in ['name', 'description', 'workflow_definition', 'is_active']:
                workflow[key] = value
        
        return success_response(workflow, 200, "工作流更新成功")
    
    @app.route('/api/workflows/<int:workflow_id>', methods=['DELETE'])
    def delete_workflow(workflow_id):
        """删除工作流"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        workflow_index = next((i for i, w in enumerate(mock_data['workflows']) if w['id'] == workflow_id), None)
        if workflow_index is None:
            return error_response("工作流不存在", 404)
        
        deleted_workflow = mock_data['workflows'].pop(workflow_index)
        return success_response(deleted_workflow, 200, "工作流删除成功")
    
    @app.route('/api/workflows/validate', methods=['POST'])
    def validate_workflow():
        """验证工作流"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        workflow_def = data.get('workflow_definition', {})
        
        # 简单的工作流验证
        nodes = workflow_def.get('nodes', [])
        edges = workflow_def.get('edges', [])
        
        if not nodes:
            validation_result = {
                'valid': False,
                'errors': ['工作流必须包含至少一个节点']
            }
        else:
            # 检查循环依赖
            node_ids = {node['id'] for node in nodes}
            for edge in edges:
                if edge.get('source') == 'task3' and edge.get('target') == 'task1':
                    validation_result = {
                        'valid': False,
                        'errors': ['检测到循环依赖']
                    }
                    break
            else:
                validation_result = {
                    'valid': True,
                    'execution_plan': {
                        'execution_order': [['task1'], ['task2'], ['task3']],
                        'estimated_duration': 300
                    }
                }
        
        return success_response(validation_result)
    
    @app.route('/api/workflows/execution-plan', methods=['POST'])
    def get_execution_plan():
        """获取执行计划"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        execution_plan = {
            'execution_order': [['start_task'], ['process_task'], ['end_task']],
            'estimated_duration': 300,
            'parallel_groups': []
        }
        
        return success_response(execution_plan)
    
    @app.route('/api/workflows/<int:workflow_id>/execute', methods=['POST'])
    def execute_workflow(workflow_id):
        """执行工作流"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        workflow = next((w for w in mock_data['workflows'] if w['id'] == workflow_id), None)
        if not workflow:
            return error_response("工作流不存在", 404)
        
        execution_result = {
            'status': 'started',
            'execution_id': f'workflow-exec-{workflow_id}-123',
            'estimated_duration': 300
        }
        
        return success_response(execution_result, 200, "工作流执行已启动")
    
    @app.route('/api/workflows/validate-condition', methods=['POST'])
    def validate_condition():
        """验证条件"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        condition = data.get('condition', '')
        context = data.get('context', {})
        
        # 简单的条件验证
        if 'invalid syntax' in condition.lower():
            validation_result = {
                'valid': False,
                'error': 'SyntaxError: invalid syntax'
            }
        else:
            # 模拟条件评估
            if condition == 'status == "success" and duration < 300':
                result = context.get('status') == 'success' and context.get('duration', 0) < 300
            else:
                result = True
            
            validation_result = {
                'valid': True,
                'result': result
            }
        
        return success_response(validation_result)
    
    @app.route('/api/workflows/condition-help', methods=['GET'])
    def get_condition_help():
        """获取条件帮助"""
        help_data = {
            'predefined_conditions': ['success', 'failed', 'always'],
            'operators': ['==', '!=', '>', '<', '>=', '<=', 'and', 'or'],
            'functions': ['len', 'str', 'int', 'float'],
            'variables': ['status', 'duration', 'output', 'error_message'],
            'examples': [
                'success',
                'status == "success"',
                'duration < 300',
                'status == "success" and duration < 300'
            ]
        }
        
        return success_response(help_data)
    
    @app.route('/api/workflows/validate-parameters', methods=['POST'])
    def validate_parameters():
        """验证参数"""
        # 验证认证
        auth_error = validate_auth()
        if auth_error:
            return auth_error
        
        # 验证JSON
        json_error = validate_json()
        if json_error:
            return json_error
        
        data = request.get_json()
        parameters = data.get('parameters', {})
        schema = data.get('schema', {})
        
        errors = []
        
        # 简单的参数验证
        for param_name, param_schema in schema.items():
            param_value = parameters.get(param_name)
            
            # 检查最大值
            if param_schema.get('max') and isinstance(param_value, (int, float)):
                if param_value > param_schema['max']:
                    errors.append(f'{param_name} 超过最大值 {param_schema["max"]}')
            
            # 检查最小值
            if param_schema.get('min') and isinstance(param_value, (int, float)):
                if param_value < param_schema['min']:
                    errors.append(f'{param_name} 低于最小值 {param_schema["min"]}')
        
        validation_result = {
            'valid': len(errors) == 0,
            'errors': errors
        }
        
        return success_response(validation_result)
    
    @app.route('/api/workflows/parameter-schema', methods=['GET'])
    def get_parameter_schema():
        """获取参数模式"""
        schema_data = {
            'types': ['string', 'integer', 'float', 'boolean', 'json', 'list'],
            'constraints': ['required', 'min', 'max', 'min_length', 'max_length', 'pattern'],
            'examples': {
                'string': 'hello world',
                'integer': 42,
                'float': 3.14,
                'boolean': True,
                'json': {'key': 'value'},
                'list': ['item1', 'item2']
            }
        }
        
        return success_response(schema_data)
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return error_response("资源不存在", 404)
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        return error_response("方法不允许", 405)
    
    @app.errorhandler(500)
    def internal_error(error):
        return error_response("内部服务器错误", 500)
    
    return app


if __name__ == '__main__':
    app = create_mock_app()
    app.run(debug=True, port=5000)
