{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport EyeInvisibleOutlined from \"@ant-design/icons/es/icons/EyeInvisibleOutlined\";\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport useRemovePasswordTimeout from './hooks/useRemovePasswordTimeout';\nimport Input from './Input';\nconst defaultIconRender = visible => visible ? /*#__PURE__*/React.createElement(EyeOutlined, null) : /*#__PURE__*/React.createElement(EyeInvisibleOutlined, null);\nconst actionMap = {\n  click: 'onClick',\n  hover: 'onMouseOver'\n};\nconst Password = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    disabled: customDisabled,\n    action = 'click',\n    visibilityToggle = true,\n    iconRender = defaultIconRender\n  } = props;\n  // ===================== Disabled =====================\n  const disabled = React.useContext(DisabledContext);\n  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  const visibilityControlled = typeof visibilityToggle === 'object' && visibilityToggle.visible !== undefined;\n  const [visible, setVisible] = useState(() => visibilityControlled ? visibilityToggle.visible : false);\n  const inputRef = useRef(null);\n  React.useEffect(() => {\n    if (visibilityControlled) {\n      setVisible(visibilityToggle.visible);\n    }\n  }, [visibilityControlled, visibilityToggle]);\n  // Remove Password value\n  const removePasswordTimeout = useRemovePasswordTimeout(inputRef);\n  const onVisibleChange = () => {\n    var _a;\n    if (mergedDisabled) {\n      return;\n    }\n    if (visible) {\n      removePasswordTimeout();\n    }\n    const nextVisible = !visible;\n    setVisible(nextVisible);\n    if (typeof visibilityToggle === 'object') {\n      (_a = visibilityToggle.onVisibleChange) === null || _a === void 0 ? void 0 : _a.call(visibilityToggle, nextVisible);\n    }\n  };\n  const getIcon = prefixCls => {\n    const iconTrigger = actionMap[action] || '';\n    const icon = iconRender(visible);\n    const iconProps = {\n      [iconTrigger]: onVisibleChange,\n      className: `${prefixCls}-icon`,\n      key: 'passwordIcon',\n      onMouseDown: e => {\n        // Prevent focused state lost\n        // https://github.com/ant-design/ant-design/issues/15173\n        e.preventDefault();\n      },\n      onMouseUp: e => {\n        // Prevent caret position change\n        // https://github.com/ant-design/ant-design/issues/23524\n        e.preventDefault();\n      }\n    };\n    return /*#__PURE__*/React.cloneElement(/*#__PURE__*/React.isValidElement(icon) ? icon : /*#__PURE__*/React.createElement(\"span\", null, icon), iconProps);\n  };\n  const {\n      className,\n      prefixCls: customizePrefixCls,\n      inputPrefixCls: customizeInputPrefixCls,\n      size\n    } = props,\n    restProps = __rest(props, [\"className\", \"prefixCls\", \"inputPrefixCls\", \"size\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const inputPrefixCls = getPrefixCls('input', customizeInputPrefixCls);\n  const prefixCls = getPrefixCls('input-password', customizePrefixCls);\n  const suffixIcon = visibilityToggle && getIcon(prefixCls);\n  const inputClassName = classNames(prefixCls, className, {\n    [`${prefixCls}-${size}`]: !!size\n  });\n  const omittedProps = Object.assign(Object.assign({}, omit(restProps, ['suffix', 'iconRender', 'visibilityToggle'])), {\n    type: visible ? 'text' : 'password',\n    className: inputClassName,\n    prefixCls: inputPrefixCls,\n    suffix: suffixIcon\n  });\n  if (size) {\n    omittedProps.size = size;\n  }\n  return /*#__PURE__*/React.createElement(Input, Object.assign({\n    ref: composeRef(ref, inputRef)\n  }, omittedProps));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Password.displayName = 'Input.Password';\n}\nexport default Password;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useRef", "useState", "EyeInvisibleOutlined", "EyeOutlined", "classNames", "omit", "composeRef", "ConfigContext", "DisabledContext", "useRemovePasswordTimeout", "Input", "defaultIconRender", "visible", "createElement", "actionMap", "click", "hover", "Password", "forwardRef", "props", "ref", "disabled", "customDisabled", "action", "visibilityToggle", "iconRender", "useContext", "mergedDisabled", "visibilityControlled", "undefined", "setVisible", "inputRef", "useEffect", "removePasswordTimeout", "onVisibleChange", "_a", "nextVisible", "getIcon", "prefixCls", "iconTrigger", "icon", "iconProps", "className", "key", "onMouseDown", "preventDefault", "onMouseUp", "cloneElement", "isValidElement", "customizePrefixCls", "inputPrefixCls", "customizeInputPrefixCls", "size", "restProps", "getPrefixCls", "suffixIcon", "inputClassName", "omittedProps", "assign", "type", "suffix", "process", "env", "NODE_ENV", "displayName"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/input/Password.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport EyeInvisibleOutlined from \"@ant-design/icons/es/icons/EyeInvisibleOutlined\";\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport { ConfigContext } from '../config-provider';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport useRemovePasswordTimeout from './hooks/useRemovePasswordTimeout';\nimport Input from './Input';\nconst defaultIconRender = visible => visible ? /*#__PURE__*/React.createElement(EyeOutlined, null) : /*#__PURE__*/React.createElement(EyeInvisibleOutlined, null);\nconst actionMap = {\n  click: 'onClick',\n  hover: 'onMouseOver'\n};\nconst Password = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    disabled: customDisabled,\n    action = 'click',\n    visibilityToggle = true,\n    iconRender = defaultIconRender\n  } = props;\n  // ===================== Disabled =====================\n  const disabled = React.useContext(DisabledContext);\n  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  const visibilityControlled = typeof visibilityToggle === 'object' && visibilityToggle.visible !== undefined;\n  const [visible, setVisible] = useState(() => visibilityControlled ? visibilityToggle.visible : false);\n  const inputRef = useRef(null);\n  React.useEffect(() => {\n    if (visibilityControlled) {\n      setVisible(visibilityToggle.visible);\n    }\n  }, [visibilityControlled, visibilityToggle]);\n  // Remove Password value\n  const removePasswordTimeout = useRemovePasswordTimeout(inputRef);\n  const onVisibleChange = () => {\n    var _a;\n    if (mergedDisabled) {\n      return;\n    }\n    if (visible) {\n      removePasswordTimeout();\n    }\n    const nextVisible = !visible;\n    setVisible(nextVisible);\n    if (typeof visibilityToggle === 'object') {\n      (_a = visibilityToggle.onVisibleChange) === null || _a === void 0 ? void 0 : _a.call(visibilityToggle, nextVisible);\n    }\n  };\n  const getIcon = prefixCls => {\n    const iconTrigger = actionMap[action] || '';\n    const icon = iconRender(visible);\n    const iconProps = {\n      [iconTrigger]: onVisibleChange,\n      className: `${prefixCls}-icon`,\n      key: 'passwordIcon',\n      onMouseDown: e => {\n        // Prevent focused state lost\n        // https://github.com/ant-design/ant-design/issues/15173\n        e.preventDefault();\n      },\n      onMouseUp: e => {\n        // Prevent caret position change\n        // https://github.com/ant-design/ant-design/issues/23524\n        e.preventDefault();\n      }\n    };\n    return /*#__PURE__*/React.cloneElement(/*#__PURE__*/React.isValidElement(icon) ? icon : /*#__PURE__*/React.createElement(\"span\", null, icon), iconProps);\n  };\n  const {\n      className,\n      prefixCls: customizePrefixCls,\n      inputPrefixCls: customizeInputPrefixCls,\n      size\n    } = props,\n    restProps = __rest(props, [\"className\", \"prefixCls\", \"inputPrefixCls\", \"size\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const inputPrefixCls = getPrefixCls('input', customizeInputPrefixCls);\n  const prefixCls = getPrefixCls('input-password', customizePrefixCls);\n  const suffixIcon = visibilityToggle && getIcon(prefixCls);\n  const inputClassName = classNames(prefixCls, className, {\n    [`${prefixCls}-${size}`]: !!size\n  });\n  const omittedProps = Object.assign(Object.assign({}, omit(restProps, ['suffix', 'iconRender', 'visibilityToggle'])), {\n    type: visible ? 'text' : 'password',\n    className: inputClassName,\n    prefixCls: inputPrefixCls,\n    suffix: suffixIcon\n  });\n  if (size) {\n    omittedProps.size = size;\n  }\n  return /*#__PURE__*/React.createElement(Input, Object.assign({\n    ref: composeRef(ref, inputRef)\n  }, omittedProps));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Password.displayName = 'Input.Password';\n}\nexport default Password;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,OAAOC,oBAAoB,MAAM,iDAAiD;AAClF,OAAOC,WAAW,MAAM,wCAAwC;AAChE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,OAAOC,wBAAwB,MAAM,kCAAkC;AACvE,OAAOC,KAAK,MAAM,SAAS;AAC3B,MAAMC,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,GAAG,aAAab,KAAK,CAACc,aAAa,CAACV,WAAW,EAAE,IAAI,CAAC,GAAG,aAAaJ,KAAK,CAACc,aAAa,CAACX,oBAAoB,EAAE,IAAI,CAAC;AACjK,MAAMY,SAAS,GAAG;EAChBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE;AACT,CAAC;AACD,MAAMC,QAAQ,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC7D,MAAM;IACJC,QAAQ,EAAEC,cAAc;IACxBC,MAAM,GAAG,OAAO;IAChBC,gBAAgB,GAAG,IAAI;IACvBC,UAAU,GAAGd;EACf,CAAC,GAAGQ,KAAK;EACT;EACA,MAAME,QAAQ,GAAGtB,KAAK,CAAC2B,UAAU,CAAClB,eAAe,CAAC;EAClD,MAAMmB,cAAc,GAAGL,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGD,QAAQ;EACvG,MAAMO,oBAAoB,GAAG,OAAOJ,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,CAACZ,OAAO,KAAKiB,SAAS;EAC3G,MAAM,CAACjB,OAAO,EAAEkB,UAAU,CAAC,GAAG7B,QAAQ,CAAC,MAAM2B,oBAAoB,GAAGJ,gBAAgB,CAACZ,OAAO,GAAG,KAAK,CAAC;EACrG,MAAMmB,QAAQ,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC7BD,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB,IAAIJ,oBAAoB,EAAE;MACxBE,UAAU,CAACN,gBAAgB,CAACZ,OAAO,CAAC;IACtC;EACF,CAAC,EAAE,CAACgB,oBAAoB,EAAEJ,gBAAgB,CAAC,CAAC;EAC5C;EACA,MAAMS,qBAAqB,GAAGxB,wBAAwB,CAACsB,QAAQ,CAAC;EAChE,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,EAAE;IACN,IAAIR,cAAc,EAAE;MAClB;IACF;IACA,IAAIf,OAAO,EAAE;MACXqB,qBAAqB,CAAC,CAAC;IACzB;IACA,MAAMG,WAAW,GAAG,CAACxB,OAAO;IAC5BkB,UAAU,CAACM,WAAW,CAAC;IACvB,IAAI,OAAOZ,gBAAgB,KAAK,QAAQ,EAAE;MACxC,CAACW,EAAE,GAAGX,gBAAgB,CAACU,eAAe,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1C,IAAI,CAAC+B,gBAAgB,EAAEY,WAAW,CAAC;IACrH;EACF,CAAC;EACD,MAAMC,OAAO,GAAGC,SAAS,IAAI;IAC3B,MAAMC,WAAW,GAAGzB,SAAS,CAACS,MAAM,CAAC,IAAI,EAAE;IAC3C,MAAMiB,IAAI,GAAGf,UAAU,CAACb,OAAO,CAAC;IAChC,MAAM6B,SAAS,GAAG;MAChB,CAACF,WAAW,GAAGL,eAAe;MAC9BQ,SAAS,EAAE,GAAGJ,SAAS,OAAO;MAC9BK,GAAG,EAAE,cAAc;MACnBC,WAAW,EAAEzD,CAAC,IAAI;QAChB;QACA;QACAA,CAAC,CAAC0D,cAAc,CAAC,CAAC;MACpB,CAAC;MACDC,SAAS,EAAE3D,CAAC,IAAI;QACd;QACA;QACAA,CAAC,CAAC0D,cAAc,CAAC,CAAC;MACpB;IACF,CAAC;IACD,OAAO,aAAa9C,KAAK,CAACgD,YAAY,CAAC,aAAahD,KAAK,CAACiD,cAAc,CAACR,IAAI,CAAC,GAAGA,IAAI,GAAG,aAAazC,KAAK,CAACc,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE2B,IAAI,CAAC,EAAEC,SAAS,CAAC;EAC1J,CAAC;EACD,MAAM;MACFC,SAAS;MACTJ,SAAS,EAAEW,kBAAkB;MAC7BC,cAAc,EAAEC,uBAAuB;MACvCC;IACF,CAAC,GAAGjC,KAAK;IACTkC,SAAS,GAAGpE,MAAM,CAACkC,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;EACjF,MAAM;IACJmC;EACF,CAAC,GAAGvD,KAAK,CAAC2B,UAAU,CAACnB,aAAa,CAAC;EACnC,MAAM2C,cAAc,GAAGI,YAAY,CAAC,OAAO,EAAEH,uBAAuB,CAAC;EACrE,MAAMb,SAAS,GAAGgB,YAAY,CAAC,gBAAgB,EAAEL,kBAAkB,CAAC;EACpE,MAAMM,UAAU,GAAG/B,gBAAgB,IAAIa,OAAO,CAACC,SAAS,CAAC;EACzD,MAAMkB,cAAc,GAAGpD,UAAU,CAACkC,SAAS,EAAEI,SAAS,EAAE;IACtD,CAAC,GAAGJ,SAAS,IAAIc,IAAI,EAAE,GAAG,CAAC,CAACA;EAC9B,CAAC,CAAC;EACF,MAAMK,YAAY,GAAGnE,MAAM,CAACoE,MAAM,CAACpE,MAAM,CAACoE,MAAM,CAAC,CAAC,CAAC,EAAErD,IAAI,CAACgD,SAAS,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC,CAAC,EAAE;IACnHM,IAAI,EAAE/C,OAAO,GAAG,MAAM,GAAG,UAAU;IACnC8B,SAAS,EAAEc,cAAc;IACzBlB,SAAS,EAAEY,cAAc;IACzBU,MAAM,EAAEL;EACV,CAAC,CAAC;EACF,IAAIH,IAAI,EAAE;IACRK,YAAY,CAACL,IAAI,GAAGA,IAAI;EAC1B;EACA,OAAO,aAAarD,KAAK,CAACc,aAAa,CAACH,KAAK,EAAEpB,MAAM,CAACoE,MAAM,CAAC;IAC3DtC,GAAG,EAAEd,UAAU,CAACc,GAAG,EAAEW,QAAQ;EAC/B,CAAC,EAAE0B,YAAY,CAAC,CAAC;AACnB,CAAC,CAAC;AACF,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC9C,QAAQ,CAAC+C,WAAW,GAAG,gBAAgB;AACzC;AACA,eAAe/C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}