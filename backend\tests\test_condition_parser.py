"""
条件解析器测试
"""
import pytest
from app.utils.condition_parser import ConditionParser


class TestConditionParser:
    """条件解析器测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.parser = ConditionParser()
    
    def test_predefined_conditions(self):
        """测试预定义条件"""
        test_cases = [
            ('success', {'status': 'success'}, True),
            ('success', {'status': 'failed'}, False),
            ('failed', {'status': 'failed'}, True),
            ('failed', {'status': 'success'}, False),
            ('always', {'status': 'success'}, True),
            ('always', {'status': 'failed'}, True),
            ('never', {'status': 'success'}, False),
            ('completed', {'status': 'success'}, True),
            ('completed', {'status': 'failed'}, True),
            ('completed', {'status': 'running'}, False),
            ('running', {'status': 'running'}, True),
            ('pending', {'status': 'pending'}, True),
            ('skipped', {'status': 'skipped'}, True)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 在上下文 {context} 中应该返回 {expected}"
    
    def test_simple_comparisons(self):
        """测试简单比较条件"""
        test_cases = [
            ('status == "success"', {'status': 'success'}, True),
            ('status == "success"', {'status': 'failed'}, False),
            ('status != "failed"', {'status': 'success'}, True),
            ('duration > 60', {'duration': 120}, True),
            ('duration > 60', {'duration': 30}, False),
            ('duration >= 60', {'duration': 60}, True),
            ('duration < 30', {'duration': 15}, True),
            ('duration <= 30', {'duration': 30}, True),
            ('return_code == 0', {'return_code': 0}, True),
            ('return_code != 0', {'return_code': 1}, True)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 在上下文 {context} 中应该返回 {expected}"
    
    def test_logical_operators(self):
        """测试逻辑操作符"""
        test_cases = [
            ('status == "success" and duration < 60', {'status': 'success', 'duration': 30}, True),
            ('status == "success" and duration < 60', {'status': 'success', 'duration': 90}, False),
            ('status == "failed" or duration > 120', {'status': 'success', 'duration': 150}, True),
            ('status == "failed" or duration > 120', {'status': 'success', 'duration': 60}, False),
            ('not status == "failed"', {'status': 'success'}, True),
            ('not status == "failed"', {'status': 'failed'}, False)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 在上下文 {context} 中应该返回 {expected}"
    
    def test_string_operations(self):
        """测试字符串操作"""
        test_cases = [
            ('"error" in output', {'output': 'This is an error message'}, True),
            ('"error" in output', {'output': 'This is a success message'}, False),
            ('"success" in output', {'output': 'Operation completed successfully'}, True),
            ('len(output) > 10', {'output': 'short'}, False),
            ('len(output) > 10', {'output': 'this is a longer message'}, True)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 在上下文 {context} 中应该返回 {expected}"
    
    def test_numeric_operations(self):
        """测试数值操作"""
        test_cases = [
            ('duration + 10 > 60', {'duration': 55}, True),
            ('duration * 2 < 100', {'duration': 40}, True),
            ('abs(return_code) == 1', {'return_code': -1}, True),
            ('min(duration, 60) == 60', {'duration': 120}, True),
            ('max(duration, 30) == 45', {'duration': 45}, True)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 在上下文 {context} 中应该返回 {expected}"
    
    def test_complex_conditions(self):
        """测试复杂条件"""
        context = {
            'status': 'success',
            'duration': 45,
            'output': 'Processing completed with 150 records',
            'error_message': '',
            'return_code': 0
        }
        
        test_cases = [
            ('status == "success" and duration < 60 and return_code == 0', True),
            ('status == "success" and ("error" in output or return_code != 0)', False),
            ('(status == "success" or status == "completed") and duration < 120', True),
            ('len(output) > 20 and "completed" in output', True),
            ('error_message == "" and return_code == 0', True)
        ]
        
        for condition, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 应该返回 {expected}"
    
    def test_missing_variables(self):
        """测试缺少变量的情况"""
        # 当变量不存在时，应该使用默认值
        test_cases = [
            ('status == ""', {}, True),  # 默认空字符串
            ('duration == 0', {}, True),  # 默认0
            ('return_code == 0', {}, True),  # 默认0
            ('output == ""', {}, True),  # 默认空字符串
            ('error_message == ""', {}, True)  # 默认空字符串
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 在空上下文中应该返回 {expected}"
    
    def test_type_conversions(self):
        """测试类型转换"""
        test_cases = [
            ('int("42") == 42', {}, True),
            ('float("3.14") > 3', {}, True),
            ('str(42) == "42"', {}, True),
            ('bool("true")', {}, True),
            ('bool("")', {}, False),
            ('len([1, 2, 3]) == 3', {}, True)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 应该返回 {expected}"
    
    def test_invalid_conditions(self):
        """测试无效条件"""
        invalid_conditions = [
            'invalid syntax ==',
            'status = "success"',  # 单个等号
            'undefined_function()',
            'import os',  # 不允许的导入
            'exec("print(1)")',  # 不允许的函数
        ]
        
        for condition in invalid_conditions:
            result = self.parser.parse_condition(condition, {'status': 'success'})
            assert result is False, f"无效条件 '{condition}' 应该返回 False"
    
    def test_validate_condition_valid(self):
        """测试验证有效条件"""
        valid_conditions = [
            'success',
            'status == "success"',
            'duration > 60',
            'status == "success" and duration < 120',
            '"error" in output'
        ]
        
        for condition in valid_conditions:
            is_valid, message = self.parser.validate_condition(condition)
            assert is_valid, f"条件 '{condition}' 应该是有效的: {message}"
    
    def test_validate_condition_invalid(self):
        """测试验证无效条件"""
        invalid_conditions = [
            'invalid syntax ==',
            'status = "success"',
            'undefined_function()',
            '1 + ',  # 不完整的表达式
        ]
        
        for condition in invalid_conditions:
            is_valid, message = self.parser.validate_condition(condition)
            assert not is_valid, f"条件 '{condition}' 应该是无效的"
            assert len(message) > 0, "应该提供错误信息"
    
    def test_get_supported_conditions(self):
        """测试获取支持的条件类型"""
        supported = self.parser.get_supported_conditions()
        
        assert '预定义条件' in supported
        assert '变量' in supported
        assert '操作符' in supported
        assert '函数' in supported
        assert '示例' in supported
        
        # 检查预定义条件
        predefined = supported['预定义条件']
        assert 'success' in predefined
        assert 'failed' in predefined
        assert 'always' in predefined
        
        # 检查变量
        variables = supported['变量']
        assert 'status' in variables
        assert 'duration' in variables
        assert 'output' in variables
    
    def test_edge_cases(self):
        """测试边界情况"""
        test_cases = [
            # 空字符串条件
            ('', {}, False),
            # 只有空格的条件
            ('   ', {}, False),
            # 非常长的条件
            ('status == "success"' + ' and True' * 100, {'status': 'success'}, True),
            # 特殊字符
            ('status == "success with special chars: !@#$%"', 
             {'status': 'success with special chars: !@#$%'}, True),
            # 数值边界
            ('duration == 0', {'duration': 0}, True),
            ('duration == -1', {'duration': -1}, True),
            ('duration == 999999', {'duration': 999999}, True)
        ]
        
        for condition, context, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected, f"边界条件 '{condition}' 在上下文 {context} 中应该返回 {expected}"
    
    def test_security_restrictions(self):
        """测试安全限制"""
        # 这些条件应该被安全机制阻止或返回False
        dangerous_conditions = [
            '__import__("os").system("ls")',
            'eval("1+1")',
            'exec("print(1)")',
            'open("/etc/passwd")',
            'globals()',
            'locals()',
            'dir()',
            'vars()'
        ]
        
        for condition in dangerous_conditions:
            result = self.parser.parse_condition(condition, {})
            assert result is False, f"危险条件 '{condition}' 应该被阻止"
