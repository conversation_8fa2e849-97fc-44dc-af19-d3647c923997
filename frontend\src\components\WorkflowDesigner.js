import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Space, 
  message,
  Divider,
  Row,
  Col,
  Tag
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  PlayCircleOutlined,
  NodeIndexOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import api from '../services/api';

const { Option } = Select;

const WorkflowDesigner = ({ workflow, tasks, onSave, onClose }) => {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [nodeModalVisible, setNodeModalVisible] = useState(false);
  const [edgeModalVisible, setEdgeModalVisible] = useState(false);
  const [nodeForm] = Form.useForm();
  const [edgeForm] = Form.useForm();
  const canvasRef = useRef(null);

  useEffect(() => {
    if (workflow && workflow.workflow_definition) {
      const definition = workflow.workflow_definition;
      setNodes(definition.nodes || []);
      setEdges(definition.edges || []);
    }
  }, [workflow]);

  // 添加节点
  const handleAddNode = () => {
    nodeForm.resetFields();
    setSelectedNode(null);
    setNodeModalVisible(true);
  };

  // 保存节点
  const handleSaveNode = async (values) => {
    const newNode = {
      id: selectedNode ? selectedNode.id : `node_${Date.now()}`,
      type: 'task',
      ...values,
      position: selectedNode ? selectedNode.position : { x: 100, y: 100 }
    };

    if (selectedNode) {
      setNodes(nodes.map(node => node.id === selectedNode.id ? newNode : node));
    } else {
      setNodes([...nodes, newNode]);
    }

    setNodeModalVisible(false);
    nodeForm.resetFields();
    setSelectedNode(null);
  };

  // 删除节点
  const handleDeleteNode = (nodeId) => {
    setNodes(nodes.filter(node => node.id !== nodeId));
    setEdges(edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId));
  };

  // 编辑节点
  const handleEditNode = (node) => {
    setSelectedNode(node);
    nodeForm.setFieldsValue(node);
    setNodeModalVisible(true);
  };

  // 添加连接
  const handleAddEdge = () => {
    edgeForm.resetFields();
    setEdgeModalVisible(true);
  };

  // 保存连接
  const handleSaveEdge = async (values) => {
    const newEdge = {
      id: `edge_${Date.now()}`,
      source: values.source,
      target: values.target,
      condition: values.condition || null
    };

    // 检查是否会形成循环
    if (wouldCreateCycle(newEdge)) {
      message.error('不能创建循环依赖');
      return;
    }

    setEdges([...edges, newEdge]);
    setEdgeModalVisible(false);
    edgeForm.resetFields();
  };

  // 删除连接
  const handleDeleteEdge = (edgeId) => {
    setEdges(edges.filter(edge => edge.id !== edgeId));
  };

  // 检查是否会形成循环
  const wouldCreateCycle = (newEdge) => {
    const graph = {};
    
    // 构建图
    [...edges, newEdge].forEach(edge => {
      if (!graph[edge.source]) graph[edge.source] = [];
      graph[edge.source].push(edge.target);
    });

    // DFS检查循环
    const visited = new Set();
    const recStack = new Set();

    const hasCycle = (node) => {
      if (recStack.has(node)) return true;
      if (visited.has(node)) return false;

      visited.add(node);
      recStack.add(node);

      if (graph[node]) {
        for (const neighbor of graph[node]) {
          if (hasCycle(neighbor)) return true;
        }
      }

      recStack.delete(node);
      return false;
    };

    for (const node of Object.keys(graph)) {
      if (hasCycle(node)) return true;
    }

    return false;
  };

  // 验证工作流
  const validateWorkflow = async () => {
    try {
      const workflowDefinition = {
        nodes,
        edges,
        version: '1.0'
      };

      const response = await api.post('/task-workflows/workflows/validate', {
        workflow_definition: workflowDefinition
      });

      if (response.data.code === 200) {
        message.success('工作流验证通过');
        return true;
      } else {
        message.error(response.data.message || '工作流验证失败');
        return false;
      }
    } catch (error) {
      message.error('工作流验证失败');
      console.error('工作流验证失败:', error);
      return false;
    }
  };

  // 保存工作流
  const handleSaveWorkflow = async () => {
    // 先验证工作流
    const isValid = await validateWorkflow();
    if (!isValid) {
      return;
    }

    const workflowDefinition = {
      nodes,
      edges,
      version: '1.0',
      updatedAt: new Date().toISOString()
    };

    onSave(workflowDefinition);
  };

  // 执行工作流
  const handleExecuteWorkflow = () => {
    message.info('工作流执行功能正在开发中');
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddNode}
          >
            添加节点
          </Button>
          <Button
            icon={<ArrowRightOutlined />}
            onClick={handleAddEdge}
          >
            添加连接
          </Button>
          <Divider type="vertical" />
          <Button
            icon={<CheckCircleOutlined />}
            onClick={validateWorkflow}
          >
            验证工作流
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveWorkflow}
          >
            保存工作流
          </Button>
          <Button 
            icon={<PlayCircleOutlined />} 
            onClick={handleExecuteWorkflow}
          >
            执行工作流
          </Button>
        </Space>
      </Card>

      {/* 主要内容区域 */}
      <Row gutter={16} style={{ flex: 1 }}>
        {/* 节点列表 */}
        <Col span={8}>
          <Card title="节点列表" size="small" style={{ height: '100%' }}>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {nodes.map(node => (
                <Card 
                  key={node.id} 
                  size="small" 
                  style={{ marginBottom: 8 }}
                  title={
                    <Space>
                      <NodeIndexOutlined />
                      {node.name || node.id}
                    </Space>
                  }
                  extra={
                    <Space>
                      <Button 
                        type="link" 
                        size="small"
                        onClick={() => handleEditNode(node)}
                      >
                        编辑
                      </Button>
                      <Button 
                        type="link" 
                        size="small" 
                        danger
                        onClick={() => handleDeleteNode(node.id)}
                      >
                        删除
                      </Button>
                    </Space>
                  }
                >
                  <p><strong>任务:</strong> {node.task_name || '未选择'}</p>
                  <p><strong>类型:</strong> <Tag>{node.execution_type || 'serial'}</Tag></p>
                </Card>
              ))}
            </div>
          </Card>
        </Col>

        {/* 连接列表 */}
        <Col span={8}>
          <Card title="连接列表" size="small" style={{ height: '100%' }}>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {edges.map(edge => (
                <Card 
                  key={edge.id} 
                  size="small" 
                  style={{ marginBottom: 8 }}
                  title={
                    <Space>
                      <ArrowRightOutlined />
                      {`${edge.source} → ${edge.target}`}
                    </Space>
                  }
                  extra={
                    <Button 
                      type="link" 
                      size="small" 
                      danger
                      onClick={() => handleDeleteEdge(edge.id)}
                    >
                      删除
                    </Button>
                  }
                >
                  {edge.condition && (
                    <p><strong>条件:</strong> {edge.condition}</p>
                  )}
                </Card>
              ))}
            </div>
          </Card>
        </Col>

        {/* 预览区域 */}
        <Col span={8}>
          <Card title="工作流预览" size="small" style={{ height: '100%' }}>
            <div 
              ref={canvasRef}
              style={{ 
                height: '400px', 
                border: '1px dashed #d9d9d9',
                borderRadius: '4px',
                padding: '16px',
                background: '#fafafa'
              }}
            >
              <p style={{ textAlign: 'center', color: '#999' }}>
                可视化预览功能正在开发中
              </p>
              <p style={{ textAlign: 'center', color: '#999' }}>
                节点数: {nodes.length}, 连接数: {edges.length}
              </p>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 节点编辑模态框 */}
      <Modal
        title={selectedNode ? '编辑节点' : '添加节点'}
        open={nodeModalVisible}
        onOk={() => nodeForm.submit()}
        onCancel={() => {
          setNodeModalVisible(false);
          nodeForm.resetFields();
          setSelectedNode(null);
        }}
        destroyOnClose
      >
        <Form 
          form={nodeForm} 
          layout="vertical" 
          onFinish={handleSaveNode}
        >
          <Form.Item 
            name="name" 
            label="节点名称" 
            rules={[{ required: true, message: '请输入节点名称' }]}
          >
            <Input placeholder="请输入节点名称" />
          </Form.Item>
          <Form.Item 
            name="task_id" 
            label="关联任务"
            rules={[{ required: true, message: '请选择关联任务' }]}
          >
            <Select placeholder="请选择关联任务">
              {tasks.map(task => (
                <Option key={task.id} value={task.id}>
                  {task.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item 
            name="execution_type" 
            label="执行类型"
            initialValue="serial"
          >
            <Select>
              <Option value="serial">串行</Option>
              <Option value="parallel">并行</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="timeout"
            label="超时时间(秒)"
          >
            <Input type="number" placeholder="默认3600秒" />
          </Form.Item>
          <Form.Item
            name="input_parameters"
            label="输入参数"
            tooltip="JSON格式的输入参数，支持变量引用 ${variable_name}"
          >
            <Input.TextArea
              rows={3}
              placeholder='{"param1": "value1", "param2": "${previous_task_output}"}'
            />
          </Form.Item>
          <Form.Item
            name="output_extraction"
            label="输出参数提取规则"
            tooltip="从任务输出中提取参数的正则表达式规则"
          >
            <Input.TextArea
              rows={3}
              placeholder='[{"name": "result_count", "pattern": "处理了 (\\d+) 条记录", "type": "integer"}]'
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 连接编辑模态框 */}
      <Modal
        title="添加连接"
        open={edgeModalVisible}
        onOk={() => edgeForm.submit()}
        onCancel={() => {
          setEdgeModalVisible(false);
          edgeForm.resetFields();
        }}
        destroyOnClose
      >
        <Form 
          form={edgeForm} 
          layout="vertical" 
          onFinish={handleSaveEdge}
        >
          <Form.Item 
            name="source" 
            label="源节点" 
            rules={[{ required: true, message: '请选择源节点' }]}
          >
            <Select placeholder="请选择源节点">
              {nodes.map(node => (
                <Option key={node.id} value={node.id}>
                  {node.name || node.id}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item 
            name="target" 
            label="目标节点"
            rules={[{ required: true, message: '请选择目标节点' }]}
          >
            <Select placeholder="请选择目标节点">
              {nodes.map(node => (
                <Option key={node.id} value={node.id}>
                  {node.name || node.id}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="condition"
            label="执行条件"
          >
            <Select placeholder="请选择执行条件" allowClear>
              <Option value="success">成功时执行</Option>
              <Option value="failed">失败时执行</Option>
              <Option value="always">总是执行</Option>
              <Option value="completed">完成时执行（成功或失败）</Option>
              <Option value="custom">自定义条件</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="custom_condition"
            label="自定义条件表达式"
            tooltip="支持变量: status, output, error_message, duration。示例: status == 'success' and duration < 60"
          >
            <Input.TextArea
              rows={2}
              placeholder="例如: status == 'success' and duration < 60"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WorkflowDesigner;
