# 测试文档

## 概述

本项目采用全面的测试策略，包括单元测试、集成测试、API测试和性能测试，确保代码质量和系统稳定性。

## 测试架构

### 测试类型

1. **单元测试** (`@pytest.mark.unit`)
   - 测试单个函数或类的功能
   - 使用mock隔离外部依赖
   - 快速执行，覆盖核心业务逻辑

2. **集成测试** (`@pytest.mark.integration`)
   - 测试多个组件之间的交互
   - 使用真实数据库连接
   - 验证端到端功能

3. **API测试** (`@pytest.mark.api`)
   - 测试REST API端点
   - 验证请求/响应格式
   - 测试错误处理和边界条件

4. **性能测试** (`@pytest.mark.slow`)
   - 测试系统性能和并发能力
   - 验证响应时间要求
   - 压力测试和负载测试

### 测试结构

```
tests/
├── conftest.py              # 测试配置和fixtures
├── test_api_task_workflows.py  # 工作流API测试
├── test_dependency_manager.py  # 依赖管理器测试
├── test_condition_parser.py    # 条件解析器测试
├── test_parameter_manager.py   # 参数管理器测试
├── test_execution_manager.py   # 执行管理器测试
├── test_models.py              # 数据库模型测试
├── test_integration.py         # 集成测试
└── README.md                   # 测试文档
```

## 运行测试

### 环境准备

1. 安装测试依赖：
```bash
pip install -r requirements.txt
```

2. 设置环境变量：
```bash
export FLASK_ENV=testing
export TESTING=true
```

### 运行命令

#### 运行所有测试
```bash
python run_tests.py
```

#### 运行特定类型的测试
```bash
# 单元测试
pytest -m unit

# 集成测试
pytest -m integration

# API测试
pytest -m api

# 性能测试
pytest -m slow
```

#### 生成覆盖率报告
```bash
pytest --cov=app --cov-report=html --cov-report=term-missing
```

#### 运行特定测试文件
```bash
pytest tests/test_dependency_manager.py -v
```

#### 运行特定测试方法
```bash
pytest tests/test_dependency_manager.py::TestDependencyManager::test_add_dependency -v
```

### 测试选项

- `-v, --verbose`: 详细输出
- `--tb=short`: 简短的错误回溯
- `--tb=long`: 详细的错误回溯
- `--tb=no`: 不显示错误回溯
- `-x, --exitfirst`: 第一个失败后停止
- `--maxfail=num`: 失败num次后停止
- `-k EXPRESSION`: 只运行匹配表达式的测试
- `--collect-only`: 只收集测试，不执行

## 测试配置

### pytest.ini
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=90
```

### conftest.py
包含测试配置和共享fixtures：
- `app`: 测试应用实例
- `client`: 测试客户端
- `db_session`: 数据库会话
- `test_factory`: 测试数据工厂

## 编写测试

### 测试命名规范

1. 测试文件：`test_*.py`
2. 测试类：`Test*`
3. 测试方法：`test_*`
4. 测试方法应该描述测试的功能

### 测试结构

```python
class TestClassName:
    """测试类描述"""
    
    def setup_method(self):
        """每个测试方法前执行"""
        pass
    
    def teardown_method(self):
        """每个测试方法后执行"""
        pass
    
    def test_specific_functionality(self):
        """测试特定功能"""
        # Arrange - 准备测试数据
        # Act - 执行被测试的操作
        # Assert - 验证结果
        pass
```

### 使用Fixtures

```python
def test_with_database(db_session, test_factory):
    """使用数据库的测试"""
    # 创建测试数据
    task = test_factory.create_task(db_session, name="测试任务")
    
    # 执行测试
    assert task.name == "测试任务"
```

### Mock使用

```python
from unittest.mock import patch, MagicMock

@patch('app.some_module.external_service')
def test_with_mock(mock_service):
    """使用mock的测试"""
    mock_service.return_value = "mocked_result"
    
    # 执行测试
    result = function_that_uses_external_service()
    
    # 验证mock被调用
    mock_service.assert_called_once()
    assert result == "mocked_result"
```

## 测试数据管理

### 测试数据工厂

使用`TestDataFactory`创建测试数据：

```python
def test_with_test_data(db_session, test_factory):
    # 创建任务
    task = test_factory.create_task(
        db_session,
        name="测试任务",
        description="测试描述"
    )
    
    # 创建脚本
    script = test_factory.create_script(
        db_session,
        name="测试脚本",
        content="print('hello')"
    )
```

### 数据清理

测试数据会在每个测试后自动清理，通过`clean_database` fixture实现。

## 覆盖率要求

- 整体覆盖率：≥90%
- 分支覆盖率：≥80%
- 函数覆盖率：≥80%
- 行覆盖率：≥80%

### 查看覆盖率报告

1. 终端输出：运行测试时会显示覆盖率摘要
2. HTML报告：`htmlcov/index.html`
3. 详细报告：包含未覆盖的代码行

## 持续集成

### GitHub Actions配置

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Run tests
      run: python run_tests.py --no-coverage
    - name: Generate coverage report
      run: pytest --cov=app --cov-report=xml
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
```

## 最佳实践

### 1. 测试独立性
- 每个测试应该独立运行
- 不依赖其他测试的执行顺序
- 使用fixtures管理测试数据

### 2. 测试可读性
- 使用描述性的测试名称
- 遵循AAA模式（Arrange, Act, Assert）
- 添加必要的注释

### 3. 测试覆盖
- 测试正常路径和异常路径
- 测试边界条件
- 测试错误处理

### 4. Mock使用
- 只mock外部依赖
- 不要过度使用mock
- 验证mock的调用

### 5. 性能考虑
- 单元测试应该快速执行
- 使用内存数据库进行测试
- 避免不必要的网络请求

## 故障排除

### 常见问题

1. **数据库连接错误**
   - 检查测试数据库配置
   - 确保测试环境变量设置正确

2. **Import错误**
   - 检查Python路径设置
   - 确保所有依赖已安装

3. **测试超时**
   - 检查是否有死锁
   - 增加测试超时时间

4. **覆盖率不足**
   - 查看HTML覆盖率报告
   - 添加缺失的测试用例

### 调试技巧

1. 使用`pytest --pdb`在失败时进入调试器
2. 使用`pytest -s`显示print输出
3. 使用`pytest --lf`只运行上次失败的测试
4. 使用`pytest -k "test_name"`运行特定测试

## 贡献指南

1. 新功能必须包含测试
2. 修复bug必须包含回归测试
3. 测试覆盖率不能降低
4. 所有测试必须通过才能合并代码
