{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "41856e700bf2299104c7471f83dc0bb3", "files": {"z_a7b07432402c05f1_condition_parser_py": {"hash": "6534097ce1635bd52ffaef0f34e2443f", "index": {"url": "z_a7b07432402c05f1_condition_parser_py.html", "file": "app\\utils\\condition_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 138, "n_excluded": 0, "n_missing": 25, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_dependency_manager_py": {"hash": "ecb10545da2596d2f60fe74bb559c32e", "index": {"url": "z_a7b07432402c05f1_dependency_manager_py.html", "file": "app\\utils\\dependency_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 0, "n_missing": 10, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_execution_manager_py": {"hash": "bb6c880001792c8b650a46dc66e50104", "index": {"url": "z_a7b07432402c05f1_execution_manager_py.html", "file": "app\\utils\\execution_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 202, "n_excluded": 0, "n_missing": 48, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_parameter_manager_py": {"hash": "cb3095541430551ff6dd77727352efce", "index": {"url": "z_a7b07432402c05f1_parameter_manager_py.html", "file": "app\\utils\\parameter_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 190, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a7b07432402c05f1_security_py": {"hash": "5a3d7067d5f4ff67c4524a4999ff6425", "index": {"url": "z_a7b07432402c05f1_security_py.html", "file": "app\\utils\\security.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 63, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}