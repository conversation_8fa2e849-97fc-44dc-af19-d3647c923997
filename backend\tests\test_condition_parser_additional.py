"""
条件解析器补充测试
"""
import pytest
from app.utils.condition_parser import ConditionParser

class TestConditionParserAdditional:
    """条件解析器补充测试"""
    
    def setup_method(self):
        self.parser = ConditionParser()
    
    def test_complex_logical_expressions(self):
        """测试复杂逻辑表达式"""
        context = {
            'status': 'success',
            'duration': 45,
            'error_count': 0,
            'warning_count': 2
        }
        
        # 复杂AND条件
        result = self.parser.parse_condition(
            'status == "success" and duration < 60 and error_count == 0',
            context
        )
        assert result is True
        
        # 复杂OR条件
        result = self.parser.parse_condition(
            'status == "failed" or (duration > 30 and warning_count > 0)',
            context
        )
        assert result is True
    
    def test_nested_conditions(self):
        """测试嵌套条件"""
        context = {'a': 1, 'b': 2, 'c': 3}
        
        result = self.parser.parse_condition(
            '(a == 1 and b == 2) or (c == 3 and a != b)',
            context
        )
        assert result is True
    
    def test_mathematical_operations(self):
        """测试数学运算"""
        context = {'x': 10, 'y': 5}
        
        test_cases = [
            ('x + y == 15', True),
            ('x - y == 5', True),
            ('x * y == 50', True),
            ('x / y == 2', True),
            ('x % 3 == 1', True),
            ('x ** 2 == 100', True)
        ]
        
        for condition, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected
    
    def test_string_methods(self):
        """测试字符串方法"""
        context = {'message': 'Hello World', 'name': 'test'}
        
        test_cases = [
            ('message.lower() == "hello world"', True),
            ('message.upper() == "HELLO WORLD"', True),
            ('message.startswith("Hello")', True),
            ('message.endswith("World")', True),
            ('name.isalpha()', True),
            ('len(message) == 11', True)
        ]
        
        for condition, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected
    
    def test_list_operations(self):
        """测试列表操作"""
        context = {'items': [1, 2, 3, 4, 5], 'tags': ['urgent', 'important']}
        
        test_cases = [
            ('len(items) == 5', True),
            ('3 in items', True),
            ('6 not in items', True),
            ('"urgent" in tags', True),
            ('max(items) == 5', True),
            ('min(items) == 1', True),
            ('sum(items) == 15', True)
        ]
        
        for condition, expected in test_cases:
            result = self.parser.parse_condition(condition, context)
            assert result == expected
    
    def test_error_handling_edge_cases(self):
        """测试错误处理边界情况"""
        # 除零错误
        result = self.parser.parse_condition('1 / 0 == 1', {})
        assert result is False
        
        # 未定义变量
        result = self.parser.parse_condition('undefined_var == 1', {})
        assert result is False
        
        # 类型错误
        result = self.parser.parse_condition('"string" + 1', {})
        assert result is False
    
    def test_condition_validation_comprehensive(self):
        """测试条件验证的全面性"""
        valid_conditions = [
            'True',
            'False', 
            '1 == 1',
            'status in ["success", "failed"]',
            'duration > 0 and duration < 3600'
        ]
        
        for condition in valid_conditions:
            is_valid, message = self.parser.validate_condition(condition)
            assert is_valid, f"条件 '{condition}' 应该有效: {message}"
        
        invalid_conditions = [
            '',
            '   ',
            'invalid syntax',
            '1 +',
            'import os'
        ]
        
        for condition in invalid_conditions:
            is_valid, message = self.parser.validate_condition(condition)
            assert not is_valid, f"条件 '{condition}' 应该无效"
