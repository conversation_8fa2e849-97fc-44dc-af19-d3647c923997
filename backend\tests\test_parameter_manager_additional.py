"""
参数管理器补充测试
"""
import pytest
import json
from app.utils.parameter_manager import ParameterManager, ParameterType

class TestParameterManagerAdditional:
    """参数管理器补充测试"""
    
    def setup_method(self):
        self.manager = ParameterManager()
    
    def test_json_parameter_complex(self):
        """测试复杂JSON参数"""
        complex_json = {
            "config": {
                "database": {
                    "host": "localhost",
                    "port": 5432
                },
                "features": ["auth", "logging", "monitoring"]
            }
        }
        
        result = self.manager.convert_parameter(complex_json, ParameterType.JSON)
        assert result == complex_json
        
        # 测试JSON字符串
        json_str = json.dumps(complex_json)
        result = self.manager.convert_parameter(json_str, ParameterType.JSON)
        assert result == complex_json
    
    def test_list_parameter_various_formats(self):
        """测试各种格式的列表参数"""
        # CSV格式
        csv_str = "apple, banana, cherry, date"
        result = self.manager.convert_parameter(csv_str, ParameterType.LIST)
        assert result == ["apple", "banana", "cherry", "date"]
        
        # JSON数组格式
        json_array = '["item1", "item2", "item3"]'
        result = self.manager.convert_parameter(json_array, ParameterType.LIST)
        assert result == ["item1", "item2", "item3"]
        
        # 混合类型JSON数组
        mixed_array = '[1, "string", true, null]'
        result = self.manager.convert_parameter(mixed_array, ParameterType.LIST)
        assert result == [1, "string", True, None]
    
    def test_parameter_validation_comprehensive(self):
        """测试全面的参数验证"""
        # 字符串长度验证
        constraints = {'min_length': 5, 'max_length': 20}
        
        is_valid, _ = self.manager.validate_parameter("hello", ParameterType.STRING, constraints)
        assert is_valid
        
        is_valid, _ = self.manager.validate_parameter("hi", ParameterType.STRING, constraints)
        assert not is_valid
        
        is_valid, _ = self.manager.validate_parameter("a" * 25, ParameterType.STRING, constraints)
        assert not is_valid
        
        # 数值范围验证
        constraints = {'min': 0, 'max': 100}
        
        is_valid, _ = self.manager.validate_parameter(50, ParameterType.INTEGER, constraints)
        assert is_valid
        
        is_valid, _ = self.manager.validate_parameter(-10, ParameterType.INTEGER, constraints)
        assert not is_valid
        
        is_valid, _ = self.manager.validate_parameter(150, ParameterType.INTEGER, constraints)
        assert not is_valid
    
    def test_parameter_reference_resolution_complex(self):
        """测试复杂参数引用解析"""
        parameters = {
            "database_url": "postgresql://${db_user}:${db_password}@${db_host}:${db_port}/${db_name}",
            "log_file": "${log_dir}/${app_name}_${date}.log",
            "config": {
                "timeout": "${timeout_seconds}",
                "retries": "${max_retries}"
            },
            "features": ["${feature1}", "${feature2}", "static_feature"]
        }
        
        context = {
            "db_user": "admin",
            "db_password": "secret",
            "db_host": "localhost", 
            "db_port": "5432",
            "db_name": "myapp",
            "log_dir": "/var/log",
            "app_name": "taskmanager",
            "date": "2024-01-01",
            "timeout_seconds": "30",
            "max_retries": "3",
            "feature1": "authentication",
            "feature2": "monitoring"
        }
        
        resolved = self.manager.resolve_parameter_references(parameters, context)
        
        assert resolved["database_url"] == "postgresql://admin:secret@localhost:5432/myapp"
        assert resolved["log_file"] == "/var/log/taskmanager_2024-01-01.log"
        assert resolved["config"]["timeout"] == "30"
        assert resolved["config"]["retries"] == "3"
        assert resolved["features"] == ["authentication", "monitoring", "static_feature"]
    
    def test_output_parameter_extraction(self):
        """测试输出参数提取"""
        output = """
        任务执行完成
        处理记录数: 1500
        成功记录数: 1350
        失败记录数: 150
        执行时间: 45.6 秒
        输出文件: /tmp/results/data_20241220_processed.csv
        内存使用: 256 MB
        CPU使用率: 85%
        """
        
        extraction_rules = [
            {"name": "total_records", "pattern": r"处理记录数: (\d+)", "type": "integer"},
            {"name": "success_records", "pattern": r"成功记录数: (\d+)", "type": "integer"},
            {"name": "failed_records", "pattern": r"失败记录数: (\d+)", "type": "integer"},
            {"name": "execution_time", "pattern": r"执行时间: ([\d.]+) 秒", "type": "float"},
            {"name": "output_file", "pattern": r"输出文件: ([^\s]+)", "type": "string"},
            {"name": "memory_usage", "pattern": r"内存使用: (\d+) MB", "type": "integer"},
            {"name": "cpu_usage", "pattern": r"CPU使用率: (\d+)%", "type": "integer"}
        ]
        
        extracted = self.manager.extract_parameters_from_output(output, extraction_rules)
        
        assert extracted["total_records"] == 1500
        assert extracted["success_records"] == 1350
        assert extracted["failed_records"] == 150
        assert extracted["execution_time"] == 45.6
        assert extracted["output_file"] == "/tmp/results/data_20241220_processed.csv"
        assert extracted["memory_usage"] == 256
        assert extracted["cpu_usage"] == 85
    
    def test_parameter_schema_completeness(self):
        """测试参数模式的完整性"""
        schema = self.manager.get_parameter_schema()
        
        # 检查必要的键
        required_keys = ["types", "constraints", "transforms", "examples"]
        for key in required_keys:
            assert key in schema, f"参数模式应该包含 {key}"
        
        # 检查类型定义
        types = schema["types"]
        expected_types = ["string", "integer", "float", "boolean", "json", "list"]
        for param_type in expected_types:
            assert param_type in types, f"应该支持 {param_type} 类型"
        
        # 检查约束定义
        constraints = schema["constraints"]
        expected_constraints = ["required", "min", "max", "min_length", "max_length", "pattern", "enum"]
        for constraint in expected_constraints:
            assert constraint in constraints, f"应该支持 {constraint} 约束"
