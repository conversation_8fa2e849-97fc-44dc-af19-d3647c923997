{"name": "@vue/compiler-sfc", "version": "3.4.0", "description": "@vue/compiler-sfc", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "types": "dist/compiler-sfc.d.ts", "files": ["dist"], "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "dependencies": {"@babel/parser": "^7.23.6", "estree-walker": "^2.0.2", "magic-string": "^0.30.5", "postcss": "^8.4.32", "source-map-js": "^1.0.2", "@vue/compiler-core": "3.4.0", "@vue/compiler-dom": "3.4.0", "@vue/shared": "3.4.0", "@vue/compiler-ssr": "3.4.0"}, "devDependencies": {"@babel/types": "^7.23.6", "@vue/consolidate": "^0.17.3", "hash-sum": "^2.0.0", "lru-cache": "^10.1.0", "merge-source-map": "^1.1.0", "minimatch": "^9.0.3", "postcss-modules": "^6.0.0", "postcss-selector-parser": "^6.0.13", "pug": "^3.0.2", "sass": "^1.69.5"}}