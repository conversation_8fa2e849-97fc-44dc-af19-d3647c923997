#!/usr/bin/env python3
"""
简单测试验证脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试核心模块导入"""
    try:
        from app.utils.dependency_manager import DependencyManager
        from app.utils.condition_parser import ConditionParser
        from app.utils.parameter_manager import ParameterManager
        from app.utils.execution_manager import ExecutionManager
        print("✅ 核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_dependency_manager():
    """测试依赖管理器基本功能"""
    try:
        from app.utils.dependency_manager import DependencyManager
        
        dm = DependencyManager()
        dm.add_dependency('task2', 'task1')
        dm.add_dependency('task3', 'task2')
        
        # 测试无循环
        assert not dm.has_cycle(), "不应该有循环依赖"
        
        # 测试拓扑排序
        order = dm.topological_sort()
        assert len(order) == 3, "应该有3个层级"
        assert order[0] == ['task1'], "第一层应该是task1"
        
        print("✅ 依赖管理器测试通过")
        return True
    except Exception as e:
        print(f"❌ 依赖管理器测试失败: {e}")
        return False

def test_condition_parser():
    """测试条件解析器基本功能"""
    try:
        from app.utils.condition_parser import ConditionParser
        
        parser = ConditionParser()
        
        # 测试预定义条件
        assert parser.parse_condition('success', {'status': 'success'}) == True
        assert parser.parse_condition('failed', {'status': 'failed'}) == True
        assert parser.parse_condition('always', {}) == True
        
        # 测试自定义条件
        assert parser.parse_condition('status == "success"', {'status': 'success'}) == True
        assert parser.parse_condition('duration < 60', {'duration': 30}) == True
        
        print("✅ 条件解析器测试通过")
        return True
    except Exception as e:
        print(f"❌ 条件解析器测试失败: {e}")
        return False

def test_parameter_manager():
    """测试参数管理器基本功能"""
    try:
        from app.utils.parameter_manager import ParameterManager, ParameterType
        
        manager = ParameterManager()
        
        # 测试类型转换
        assert manager.convert_parameter("42", ParameterType.INTEGER) == 42
        assert manager.convert_parameter("3.14", ParameterType.FLOAT) == 3.14
        assert manager.convert_parameter("true", ParameterType.BOOLEAN) == True
        
        # 测试参数验证
        is_valid, _ = manager.validate_parameter("test", ParameterType.STRING, {'min_length': 3})
        assert is_valid == True
        
        print("✅ 参数管理器测试通过")
        return True
    except Exception as e:
        print(f"❌ 参数管理器测试失败: {e}")
        return False

def test_execution_manager():
    """测试执行管理器基本功能"""
    try:
        from app.utils.execution_manager import ExecutionManager, TaskNode
        
        manager = ExecutionManager(max_workers=2)
        
        # 测试基本初始化
        assert manager.max_workers == 2
        assert len(manager.task_results) == 0
        
        # 测试节点创建
        node = TaskNode(id="test", name="测试任务", task_id=1)
        assert node.id == "test"
        assert node.name == "测试任务"
        
        manager.cleanup()
        print("✅ 执行管理器测试通过")
        return True
    except Exception as e:
        print(f"❌ 执行管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单测试验证...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_dependency_manager,
        test_condition_parser,
        test_parameter_manager,
        test_execution_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基本功能测试通过！")
        return True
    else:
        print("💥 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
