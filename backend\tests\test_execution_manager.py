"""
执行管理器测试
"""
import pytest
import time
from unittest.mock import MagicMock, patch
from app.utils.execution_manager import (
    ExecutionManager, TaskNode, ExecutionResult, TaskStatus, ExecutionMode
)


class TestExecutionManager:
    """执行管理器测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.manager = ExecutionManager(max_workers=2)
    
    def teardown_method(self):
        """测试方法清理"""
        self.manager.cleanup()
    
    def test_initialization(self):
        """测试初始化"""
        assert self.manager.max_workers == 2
        assert len(self.manager.task_results) == 0
        assert len(self.manager.task_status) == 0
        assert len(self.manager.status_callbacks) == 0
    
    def test_add_status_callback(self):
        """测试添加状态回调"""
        callback = MagicMock()
        self.manager.add_status_callback(callback)
        
        assert callback in self.manager.status_callbacks
    
    def test_notify_status_change(self):
        """测试状态变化通知"""
        callback1 = MagicMock()
        callback2 = MagicMock()
        
        self.manager.add_status_callback(callback1)
        self.manager.add_status_callback(callback2)
        
        result = ExecutionResult(task_id="test", status=TaskStatus.SUCCESS)
        self.manager._notify_status_change("test", TaskStatus.SUCCESS, result)
        
        callback1.assert_called_once_with("test", TaskStatus.SUCCESS, result)
        callback2.assert_called_once_with("test", TaskStatus.SUCCESS, result)
    
    def test_should_execute_node_no_dependencies(self):
        """测试无依赖节点的执行检查"""
        node = TaskNode(id="task1", name="任务1", task_id=1)
        
        # 初始化任务结果
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.PENDING
        )
        
        should_execute = self.manager._should_execute_node(node)
        assert should_execute is True
    
    def test_should_execute_node_with_dependencies_success(self):
        """测试有依赖且依赖成功的节点执行检查"""
        node = TaskNode(
            id="task2", 
            name="任务2", 
            task_id=2, 
            dependencies=["task1"],
            condition="success"
        )
        
        # 设置依赖任务结果
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.SUCCESS
        )
        self.manager.task_results["task2"] = ExecutionResult(
            task_id="task2", status=TaskStatus.PENDING
        )
        
        should_execute = self.manager._should_execute_node(node)
        assert should_execute is True
    
    def test_should_execute_node_with_dependencies_failed(self):
        """测试有依赖且依赖失败的节点执行检查"""
        node = TaskNode(
            id="task2", 
            name="任务2", 
            task_id=2, 
            dependencies=["task1"],
            condition="success"
        )
        
        # 设置依赖任务结果
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.FAILED
        )
        self.manager.task_results["task2"] = ExecutionResult(
            task_id="task2", status=TaskStatus.PENDING
        )
        
        should_execute = self.manager._should_execute_node(node)
        assert should_execute is False
        assert self.manager.task_status["task2"] == TaskStatus.SKIPPED
    
    def test_should_execute_node_always_condition(self):
        """测试always条件的节点执行检查"""
        node = TaskNode(
            id="task2", 
            name="任务2", 
            task_id=2, 
            dependencies=["task1"],
            condition="always"
        )
        
        # 设置依赖任务结果为失败
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.FAILED
        )
        self.manager.task_results["task2"] = ExecutionResult(
            task_id="task2", status=TaskStatus.PENDING
        )
        
        should_execute = self.manager._should_execute_node(node)
        assert should_execute is True
    
    def test_execute_single_task_success(self):
        """测试单个任务成功执行"""
        node = TaskNode(id="task1", name="任务1", task_id=1)
        
        def mock_executor(task_node):
            return ExecutionResult(
                task_id=task_node.id,
                status=TaskStatus.SUCCESS,
                output="执行成功"
            )
        
        # 初始化任务结果
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.PENDING
        )
        
        self.manager._execute_single_task(node, mock_executor)
        
        result = self.manager.task_results["task1"]
        assert result.status == TaskStatus.SUCCESS
        assert result.output == "执行成功"
        assert result.start_time is not None
        assert result.end_time is not None
        assert result.duration is not None
    
    def test_execute_single_task_failure(self):
        """测试单个任务执行失败"""
        node = TaskNode(id="task1", name="任务1", task_id=1)
        
        def mock_executor(task_node):
            raise Exception("执行失败")
        
        # 初始化任务结果
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.PENDING
        )
        
        self.manager._execute_single_task(node, mock_executor)
        
        result = self.manager.task_results["task1"]
        assert result.status == TaskStatus.FAILED
        assert "执行失败" in result.error_message
    
    def test_execute_parallel_tasks(self):
        """测试并行任务执行"""
        nodes = [
            TaskNode(id="task1", name="任务1", task_id=1, execution_mode=ExecutionMode.PARALLEL),
            TaskNode(id="task2", name="任务2", task_id=2, execution_mode=ExecutionMode.PARALLEL)
        ]
        
        def mock_executor(task_node):
            time.sleep(0.1)  # 模拟执行时间
            return ExecutionResult(
                task_id=task_node.id,
                status=TaskStatus.SUCCESS,
                output=f"{task_node.name}执行完成"
            )
        
        # 初始化任务结果
        for node in nodes:
            self.manager.task_results[node.id] = ExecutionResult(
                task_id=node.id, status=TaskStatus.PENDING
            )
        
        node_ids = [node.id for node in nodes]
        results = self.manager._execute_parallel_tasks(node_ids, mock_executor)
        
        assert len(results) == 2
        for node_id in node_ids:
            assert results[node_id]['success'] is True
            assert self.manager.task_status[node_id] == TaskStatus.SUCCESS
    
    def test_execute_workflow_simple(self):
        """测试简单工作流执行"""
        nodes = [
            TaskNode(id="task1", name="任务1", task_id=1),
            TaskNode(id="task2", name="任务2", task_id=2, dependencies=["task1"])
        ]
        
        execution_order = [["task1"], ["task2"]]
        
        def mock_executor(task_node):
            return ExecutionResult(
                task_id=task_node.id,
                status=TaskStatus.SUCCESS,
                output=f"{task_node.name}执行完成"
            )
        
        results = self.manager.execute_workflow(nodes, execution_order, mock_executor)
        
        assert len(results) == 2
        assert results["task1"].status == TaskStatus.SUCCESS
        assert results["task2"].status == TaskStatus.SUCCESS
    
    def test_execute_workflow_with_failure(self):
        """测试工作流执行中有失败任务"""
        nodes = [
            TaskNode(id="task1", name="任务1", task_id=1),
            TaskNode(id="task2", name="任务2", task_id=2, dependencies=["task1"])
        ]
        
        execution_order = [["task1"], ["task2"]]
        
        def mock_executor(task_node):
            if task_node.id == "task1":
                return ExecutionResult(
                    task_id=task_node.id,
                    status=TaskStatus.FAILED,
                    error_message="任务1执行失败"
                )
            else:
                return ExecutionResult(
                    task_id=task_node.id,
                    status=TaskStatus.SUCCESS,
                    output=f"{task_node.name}执行完成"
                )
        
        results = self.manager.execute_workflow(nodes, execution_order, mock_executor)
        
        assert results["task1"].status == TaskStatus.FAILED
        assert results["task2"].status == TaskStatus.SKIPPED  # 因为依赖失败被跳过
    
    def test_cancel_task(self):
        """测试取消任务"""
        # 创建一个模拟的Future对象
        mock_future = MagicMock()
        mock_future.cancel.return_value = True
        
        self.manager.task_futures["task1"] = mock_future
        self.manager.task_status["task1"] = TaskStatus.RUNNING
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.RUNNING
        )
        
        result = self.manager.cancel_task("task1")
        
        assert result is True
        assert self.manager.task_status["task1"] == TaskStatus.CANCELLED
        mock_future.cancel.assert_called_once()
    
    def test_cancel_task_not_found(self):
        """测试取消不存在的任务"""
        result = self.manager.cancel_task("nonexistent")
        assert result is False
    
    def test_get_task_status(self):
        """测试获取任务状态"""
        self.manager.task_status["task1"] = TaskStatus.SUCCESS
        
        status = self.manager.get_task_status("task1")
        assert status == TaskStatus.SUCCESS
        
        status = self.manager.get_task_status("nonexistent")
        assert status is None
    
    def test_get_task_result(self):
        """测试获取任务结果"""
        result = ExecutionResult(task_id="task1", status=TaskStatus.SUCCESS)
        self.manager.task_results["task1"] = result
        
        retrieved_result = self.manager.get_task_result("task1")
        assert retrieved_result == result
        
        retrieved_result = self.manager.get_task_result("nonexistent")
        assert retrieved_result is None
    
    def test_cleanup(self):
        """测试资源清理"""
        # 添加一些测试数据
        self.manager.task_status["task1"] = TaskStatus.SUCCESS
        self.manager.task_results["task1"] = ExecutionResult(
            task_id="task1", status=TaskStatus.SUCCESS
        )
        
        self.manager.cleanup()
        
        assert len(self.manager.task_futures) == 0
        assert len(self.manager.task_results) == 0
        assert len(self.manager.task_status) == 0
    
    def test_complex_workflow_execution(self):
        """测试复杂工作流执行"""
        # 创建一个复杂的工作流：
        # task1 -> task2, task3
        # task2, task3 -> task4
        nodes = [
            TaskNode(id="task1", name="任务1", task_id=1),
            TaskNode(id="task2", name="任务2", task_id=2, dependencies=["task1"]),
            TaskNode(id="task3", name="任务3", task_id=3, dependencies=["task1"]),
            TaskNode(id="task4", name="任务4", task_id=4, dependencies=["task2", "task3"])
        ]
        
        execution_order = [["task1"], ["task2", "task3"], ["task4"]]
        
        execution_count = 0
        
        def mock_executor(task_node):
            nonlocal execution_count
            execution_count += 1
            return ExecutionResult(
                task_id=task_node.id,
                status=TaskStatus.SUCCESS,
                output=f"{task_node.name}执行完成"
            )
        
        results = self.manager.execute_workflow(nodes, execution_order, mock_executor)
        
        assert execution_count == 4
        assert len(results) == 4
        for task_id in ["task1", "task2", "task3", "task4"]:
            assert results[task_id].status == TaskStatus.SUCCESS
    
    def test_condition_evaluation_in_workflow(self):
        """测试工作流中的条件评估"""
        nodes = [
            TaskNode(id="task1", name="任务1", task_id=1),
            TaskNode(
                id="task2", 
                name="任务2", 
                task_id=2, 
                dependencies=["task1"],
                condition='status == "success" and duration < 60'
            )
        ]
        
        execution_order = [["task1"], ["task2"]]
        
        def mock_executor(task_node):
            return ExecutionResult(
                task_id=task_node.id,
                status=TaskStatus.SUCCESS,
                output=f"{task_node.name}执行完成",
                duration=30  # 小于60秒
            )
        
        results = self.manager.execute_workflow(nodes, execution_order, mock_executor)
        
        assert results["task1"].status == TaskStatus.SUCCESS
        assert results["task2"].status == TaskStatus.SUCCESS  # 条件满足，应该执行
