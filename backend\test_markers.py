#!/usr/bin/env python3
"""
测试pytest标记配置
"""
import pytest


@pytest.mark.api
def test_api_marker():
    """测试API标记"""
    assert True


@pytest.mark.workflow
def test_workflow_marker():
    """测试工作流标记"""
    assert True


@pytest.mark.performance
def test_performance_marker():
    """测试性能标记"""
    assert True


@pytest.mark.unit
def test_unit_marker():
    """测试单元测试标记"""
    assert True


@pytest.mark.integration
def test_integration_marker():
    """测试集成测试标记"""
    assert True


if __name__ == "__main__":
    print("测试pytest标记配置...")
    pytest.main([__file__, "-v"])
