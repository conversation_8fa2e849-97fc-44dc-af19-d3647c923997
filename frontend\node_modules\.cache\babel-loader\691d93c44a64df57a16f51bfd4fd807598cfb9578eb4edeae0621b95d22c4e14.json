{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { devUseWarning } from '../../../_util/warning';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../../util';\nimport FilterDropdown, { flattenKeys } from './FilterDropdown';\nconst collectFilterStates = (columns, init, pos) => {\n  let filterStates = [];\n  (columns || []).forEach((column, index) => {\n    var _a;\n    const columnPos = getColumnPos(index, pos);\n    const filterDropdownIsDefined = column.filterDropdown !== undefined;\n    if (column.filters || filterDropdownIsDefined || 'onFilter' in column) {\n      if ('filteredValue' in column) {\n        // Controlled\n        let filteredValues = column.filteredValue;\n        if (!filterDropdownIsDefined) {\n          filteredValues = (_a = filteredValues === null || filteredValues === void 0 ? void 0 : filteredValues.map(String)) !== null && _a !== void 0 ? _a : filteredValues;\n        }\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: filteredValues,\n          forceFiltered: column.filtered\n        });\n      } else {\n        // Uncontrolled\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: init && column.defaultFilteredValue ? column.defaultFilteredValue : undefined,\n          forceFiltered: column.filtered\n        });\n      }\n    }\n    if ('children' in column) {\n      filterStates = [].concat(_toConsumableArray(filterStates), _toConsumableArray(collectFilterStates(column.children, init, columnPos)));\n    }\n  });\n  return filterStates;\n};\nfunction injectFilter(prefixCls, dropdownPrefixCls, columns, filterStates, locale, triggerFilter, getPopupContainer, pos, rootClassName) {\n  return columns.map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    const {\n      filterOnClose = true,\n      filterMultiple = true,\n      filterMode,\n      filterSearch\n    } = column;\n    let newColumn = column;\n    if (newColumn.filters || newColumn.filterDropdown) {\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const filterState = filterStates.find(({\n        key\n      }) => columnKey === key);\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        title: renderProps => (/*#__PURE__*/React.createElement(FilterDropdown, {\n          tablePrefixCls: prefixCls,\n          prefixCls: `${prefixCls}-filter`,\n          dropdownPrefixCls: dropdownPrefixCls,\n          column: newColumn,\n          columnKey: columnKey,\n          filterState: filterState,\n          filterOnClose: filterOnClose,\n          filterMultiple: filterMultiple,\n          filterMode: filterMode,\n          filterSearch: filterSearch,\n          triggerFilter: triggerFilter,\n          locale: locale,\n          getPopupContainer: getPopupContainer,\n          rootClassName: rootClassName\n        }, renderColumnTitle(column.title, renderProps)))\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectFilter(prefixCls, dropdownPrefixCls, newColumn.children, filterStates, locale, triggerFilter, getPopupContainer, columnPos, rootClassName)\n      });\n    }\n    return newColumn;\n  });\n}\nconst generateFilterInfo = filterStates => {\n  const currentFilters = {};\n  filterStates.forEach(({\n    key,\n    filteredKeys,\n    column\n  }) => {\n    const keyAsString = key;\n    const {\n      filters,\n      filterDropdown\n    } = column;\n    if (filterDropdown) {\n      currentFilters[keyAsString] = filteredKeys || null;\n    } else if (Array.isArray(filteredKeys)) {\n      const keys = flattenKeys(filters);\n      currentFilters[keyAsString] = keys.filter(originKey => filteredKeys.includes(String(originKey)));\n    } else {\n      currentFilters[keyAsString] = null;\n    }\n  });\n  return currentFilters;\n};\nexport const getFilterData = (data, filterStates, childrenColumnName) => {\n  const filterDatas = filterStates.reduce((currentData, filterState) => {\n    const {\n      column: {\n        onFilter,\n        filters\n      },\n      filteredKeys\n    } = filterState;\n    if (onFilter && filteredKeys && filteredKeys.length) {\n      return currentData\n      // shallow copy\n      .map(record => Object.assign({}, record)).filter(record => filteredKeys.some(key => {\n        const keys = flattenKeys(filters);\n        const keyIndex = keys.findIndex(k => String(k) === String(key));\n        const realKey = keyIndex !== -1 ? keys[keyIndex] : key;\n        // filter children\n        if (record[childrenColumnName]) {\n          record[childrenColumnName] = getFilterData(record[childrenColumnName], filterStates, childrenColumnName);\n        }\n        return onFilter(realKey, record);\n      }));\n    }\n    return currentData;\n  }, data);\n  return filterDatas;\n};\nconst getMergedColumns = rawMergedColumns => rawMergedColumns.flatMap(column => {\n  if ('children' in column) {\n    return [column].concat(_toConsumableArray(getMergedColumns(column.children || [])));\n  }\n  return [column];\n});\nconst useFilter = props => {\n  const {\n    prefixCls,\n    dropdownPrefixCls,\n    mergedColumns: rawMergedColumns,\n    onFilterChange,\n    getPopupContainer,\n    locale: tableLocale,\n    rootClassName\n  } = props;\n  const warning = devUseWarning('Table');\n  const mergedColumns = React.useMemo(() => getMergedColumns(rawMergedColumns || []), [rawMergedColumns]);\n  const [filterStates, setFilterStates] = React.useState(() => collectFilterStates(mergedColumns, true));\n  const mergedFilterStates = React.useMemo(() => {\n    const collectedStates = collectFilterStates(mergedColumns, false);\n    if (collectedStates.length === 0) {\n      return collectedStates;\n    }\n    let filteredKeysIsAllNotControlled = true;\n    let filteredKeysIsAllControlled = true;\n    collectedStates.forEach(({\n      filteredKeys\n    }) => {\n      if (filteredKeys !== undefined) {\n        filteredKeysIsAllNotControlled = false;\n      } else {\n        filteredKeysIsAllControlled = false;\n      }\n    });\n    // Return if not controlled\n    if (filteredKeysIsAllNotControlled) {\n      // Filter column may have been removed\n      const keyList = (mergedColumns || []).map((column, index) => getColumnKey(column, getColumnPos(index)));\n      return filterStates.filter(({\n        key\n      }) => keyList.includes(key)).map(item => {\n        const col = mergedColumns[keyList.findIndex(key => key === item.key)];\n        return Object.assign(Object.assign({}, item), {\n          column: Object.assign(Object.assign({}, item.column), col),\n          forceFiltered: col.filtered\n        });\n      });\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(filteredKeysIsAllControlled, 'usage', 'Columns should all contain `filteredValue` or not contain `filteredValue`.') : void 0;\n    return collectedStates;\n  }, [mergedColumns, filterStates]);\n  const filters = React.useMemo(() => generateFilterInfo(mergedFilterStates), [mergedFilterStates]);\n  const triggerFilter = filterState => {\n    const newFilterStates = mergedFilterStates.filter(({\n      key\n    }) => key !== filterState.key);\n    newFilterStates.push(filterState);\n    setFilterStates(newFilterStates);\n    onFilterChange(generateFilterInfo(newFilterStates), newFilterStates);\n  };\n  const transformColumns = innerColumns => injectFilter(prefixCls, dropdownPrefixCls, innerColumns, mergedFilterStates, tableLocale, triggerFilter, getPopupContainer, undefined, rootClassName);\n  return [transformColumns, mergedFilterStates, filters];\n};\nexport { flattenKeys };\nexport default useFilter;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "devUseW<PERSON>ning", "getColumnKey", "getColumnPos", "renderColumnTitle", "FilterDropdown", "flatten<PERSON>eys", "collectFilterStates", "columns", "init", "pos", "filterStates", "for<PERSON>ach", "column", "index", "_a", "columnPos", "filterDropdownIsDefined", "filterDropdown", "undefined", "filters", "filteredValues", "filteredValue", "map", "String", "push", "key", "filtered<PERSON>eys", "forceFiltered", "filtered", "defaultFilteredValue", "concat", "children", "injectFilter", "prefixCls", "dropdownPrefixCls", "locale", "triggerFilter", "getPopupContainer", "rootClassName", "filterOnClose", "filterMultiple", "filterMode", "filterSearch", "newColumn", "column<PERSON>ey", "filterState", "find", "Object", "assign", "title", "renderProps", "createElement", "tablePrefixCls", "generateFilterInfo", "currentFilters", "keyAsString", "Array", "isArray", "keys", "filter", "<PERSON><PERSON><PERSON>", "includes", "getFilterData", "data", "childrenColumnName", "filterDatas", "reduce", "currentData", "onFilter", "length", "record", "some", "keyIndex", "findIndex", "k", "realKey", "getMergedColumns", "rawMergedColumns", "flatMap", "useFilter", "props", "mergedColumns", "onFilterChange", "tableLocale", "warning", "useMemo", "setFilterStates", "useState", "mergedFilterStates", "collectedStates", "filteredKeysIsAllNotControlled", "filteredKeysIsAllControlled", "keyList", "item", "col", "process", "env", "NODE_ENV", "newFilterStates", "transformColumns", "innerColumns"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/table/hooks/useFilter/index.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { devUseWarning } from '../../../_util/warning';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../../util';\nimport FilterDropdown, { flattenKeys } from './FilterDropdown';\nconst collectFilterStates = (columns, init, pos) => {\n  let filterStates = [];\n  (columns || []).forEach((column, index) => {\n    var _a;\n    const columnPos = getColumnPos(index, pos);\n    const filterDropdownIsDefined = column.filterDropdown !== undefined;\n    if (column.filters || filterDropdownIsDefined || 'onFilter' in column) {\n      if ('filteredValue' in column) {\n        // Controlled\n        let filteredValues = column.filteredValue;\n        if (!filterDropdownIsDefined) {\n          filteredValues = (_a = filteredValues === null || filteredValues === void 0 ? void 0 : filteredValues.map(String)) !== null && _a !== void 0 ? _a : filteredValues;\n        }\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: filteredValues,\n          forceFiltered: column.filtered\n        });\n      } else {\n        // Uncontrolled\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: init && column.defaultFilteredValue ? column.defaultFilteredValue : undefined,\n          forceFiltered: column.filtered\n        });\n      }\n    }\n    if ('children' in column) {\n      filterStates = [].concat(_toConsumableArray(filterStates), _toConsumableArray(collectFilterStates(column.children, init, columnPos)));\n    }\n  });\n  return filterStates;\n};\nfunction injectFilter(prefixCls, dropdownPrefixCls, columns, filterStates, locale, triggerFilter, getPopupContainer, pos, rootClassName) {\n  return columns.map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    const {\n      filterOnClose = true,\n      filterMultiple = true,\n      filterMode,\n      filterSearch\n    } = column;\n    let newColumn = column;\n    if (newColumn.filters || newColumn.filterDropdown) {\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const filterState = filterStates.find(({\n        key\n      }) => columnKey === key);\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        title: renderProps => (/*#__PURE__*/React.createElement(FilterDropdown, {\n          tablePrefixCls: prefixCls,\n          prefixCls: `${prefixCls}-filter`,\n          dropdownPrefixCls: dropdownPrefixCls,\n          column: newColumn,\n          columnKey: columnKey,\n          filterState: filterState,\n          filterOnClose: filterOnClose,\n          filterMultiple: filterMultiple,\n          filterMode: filterMode,\n          filterSearch: filterSearch,\n          triggerFilter: triggerFilter,\n          locale: locale,\n          getPopupContainer: getPopupContainer,\n          rootClassName: rootClassName\n        }, renderColumnTitle(column.title, renderProps)))\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectFilter(prefixCls, dropdownPrefixCls, newColumn.children, filterStates, locale, triggerFilter, getPopupContainer, columnPos, rootClassName)\n      });\n    }\n    return newColumn;\n  });\n}\nconst generateFilterInfo = filterStates => {\n  const currentFilters = {};\n  filterStates.forEach(({\n    key,\n    filteredKeys,\n    column\n  }) => {\n    const keyAsString = key;\n    const {\n      filters,\n      filterDropdown\n    } = column;\n    if (filterDropdown) {\n      currentFilters[keyAsString] = filteredKeys || null;\n    } else if (Array.isArray(filteredKeys)) {\n      const keys = flattenKeys(filters);\n      currentFilters[keyAsString] = keys.filter(originKey => filteredKeys.includes(String(originKey)));\n    } else {\n      currentFilters[keyAsString] = null;\n    }\n  });\n  return currentFilters;\n};\nexport const getFilterData = (data, filterStates, childrenColumnName) => {\n  const filterDatas = filterStates.reduce((currentData, filterState) => {\n    const {\n      column: {\n        onFilter,\n        filters\n      },\n      filteredKeys\n    } = filterState;\n    if (onFilter && filteredKeys && filteredKeys.length) {\n      return currentData\n      // shallow copy\n      .map(record => Object.assign({}, record)).filter(record => filteredKeys.some(key => {\n        const keys = flattenKeys(filters);\n        const keyIndex = keys.findIndex(k => String(k) === String(key));\n        const realKey = keyIndex !== -1 ? keys[keyIndex] : key;\n        // filter children\n        if (record[childrenColumnName]) {\n          record[childrenColumnName] = getFilterData(record[childrenColumnName], filterStates, childrenColumnName);\n        }\n        return onFilter(realKey, record);\n      }));\n    }\n    return currentData;\n  }, data);\n  return filterDatas;\n};\nconst getMergedColumns = rawMergedColumns => rawMergedColumns.flatMap(column => {\n  if ('children' in column) {\n    return [column].concat(_toConsumableArray(getMergedColumns(column.children || [])));\n  }\n  return [column];\n});\nconst useFilter = props => {\n  const {\n    prefixCls,\n    dropdownPrefixCls,\n    mergedColumns: rawMergedColumns,\n    onFilterChange,\n    getPopupContainer,\n    locale: tableLocale,\n    rootClassName\n  } = props;\n  const warning = devUseWarning('Table');\n  const mergedColumns = React.useMemo(() => getMergedColumns(rawMergedColumns || []), [rawMergedColumns]);\n  const [filterStates, setFilterStates] = React.useState(() => collectFilterStates(mergedColumns, true));\n  const mergedFilterStates = React.useMemo(() => {\n    const collectedStates = collectFilterStates(mergedColumns, false);\n    if (collectedStates.length === 0) {\n      return collectedStates;\n    }\n    let filteredKeysIsAllNotControlled = true;\n    let filteredKeysIsAllControlled = true;\n    collectedStates.forEach(({\n      filteredKeys\n    }) => {\n      if (filteredKeys !== undefined) {\n        filteredKeysIsAllNotControlled = false;\n      } else {\n        filteredKeysIsAllControlled = false;\n      }\n    });\n    // Return if not controlled\n    if (filteredKeysIsAllNotControlled) {\n      // Filter column may have been removed\n      const keyList = (mergedColumns || []).map((column, index) => getColumnKey(column, getColumnPos(index)));\n      return filterStates.filter(({\n        key\n      }) => keyList.includes(key)).map(item => {\n        const col = mergedColumns[keyList.findIndex(key => key === item.key)];\n        return Object.assign(Object.assign({}, item), {\n          column: Object.assign(Object.assign({}, item.column), col),\n          forceFiltered: col.filtered\n        });\n      });\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(filteredKeysIsAllControlled, 'usage', 'Columns should all contain `filteredValue` or not contain `filteredValue`.') : void 0;\n    return collectedStates;\n  }, [mergedColumns, filterStates]);\n  const filters = React.useMemo(() => generateFilterInfo(mergedFilterStates), [mergedFilterStates]);\n  const triggerFilter = filterState => {\n    const newFilterStates = mergedFilterStates.filter(({\n      key\n    }) => key !== filterState.key);\n    newFilterStates.push(filterState);\n    setFilterStates(newFilterStates);\n    onFilterChange(generateFilterInfo(newFilterStates), newFilterStates);\n  };\n  const transformColumns = innerColumns => injectFilter(prefixCls, dropdownPrefixCls, innerColumns, mergedFilterStates, tableLocale, triggerFilter, getPopupContainer, undefined, rootClassName);\n  return [transformColumns, mergedFilterStates, filters];\n};\nexport { flattenKeys };\nexport default useFilter;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,EAAEC,YAAY,EAAEC,iBAAiB,QAAQ,YAAY;AAC1E,OAAOC,cAAc,IAAIC,WAAW,QAAQ,kBAAkB;AAC9D,MAAMC,mBAAmB,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEC,GAAG,KAAK;EAClD,IAAIC,YAAY,GAAG,EAAE;EACrB,CAACH,OAAO,IAAI,EAAE,EAAEI,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;IACzC,IAAIC,EAAE;IACN,MAAMC,SAAS,GAAGb,YAAY,CAACW,KAAK,EAAEJ,GAAG,CAAC;IAC1C,MAAMO,uBAAuB,GAAGJ,MAAM,CAACK,cAAc,KAAKC,SAAS;IACnE,IAAIN,MAAM,CAACO,OAAO,IAAIH,uBAAuB,IAAI,UAAU,IAAIJ,MAAM,EAAE;MACrE,IAAI,eAAe,IAAIA,MAAM,EAAE;QAC7B;QACA,IAAIQ,cAAc,GAAGR,MAAM,CAACS,aAAa;QACzC,IAAI,CAACL,uBAAuB,EAAE;UAC5BI,cAAc,GAAG,CAACN,EAAE,GAAGM,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACE,GAAG,CAACC,MAAM,CAAC,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGM,cAAc;QACpK;QACAV,YAAY,CAACc,IAAI,CAAC;UAChBZ,MAAM;UACNa,GAAG,EAAExB,YAAY,CAACW,MAAM,EAAEG,SAAS,CAAC;UACpCW,YAAY,EAAEN,cAAc;UAC5BO,aAAa,EAAEf,MAAM,CAACgB;QACxB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAlB,YAAY,CAACc,IAAI,CAAC;UAChBZ,MAAM;UACNa,GAAG,EAAExB,YAAY,CAACW,MAAM,EAAEG,SAAS,CAAC;UACpCW,YAAY,EAAElB,IAAI,IAAII,MAAM,CAACiB,oBAAoB,GAAGjB,MAAM,CAACiB,oBAAoB,GAAGX,SAAS;UAC3FS,aAAa,EAAEf,MAAM,CAACgB;QACxB,CAAC,CAAC;MACJ;IACF;IACA,IAAI,UAAU,IAAIhB,MAAM,EAAE;MACxBF,YAAY,GAAG,EAAE,CAACoB,MAAM,CAAChC,kBAAkB,CAACY,YAAY,CAAC,EAAEZ,kBAAkB,CAACQ,mBAAmB,CAACM,MAAM,CAACmB,QAAQ,EAAEvB,IAAI,EAAEO,SAAS,CAAC,CAAC,CAAC;IACvI;EACF,CAAC,CAAC;EACF,OAAOL,YAAY;AACrB,CAAC;AACD,SAASsB,YAAYA,CAACC,SAAS,EAAEC,iBAAiB,EAAE3B,OAAO,EAAEG,YAAY,EAAEyB,MAAM,EAAEC,aAAa,EAAEC,iBAAiB,EAAE5B,GAAG,EAAE6B,aAAa,EAAE;EACvI,OAAO/B,OAAO,CAACe,GAAG,CAAC,CAACV,MAAM,EAAEC,KAAK,KAAK;IACpC,MAAME,SAAS,GAAGb,YAAY,CAACW,KAAK,EAAEJ,GAAG,CAAC;IAC1C,MAAM;MACJ8B,aAAa,GAAG,IAAI;MACpBC,cAAc,GAAG,IAAI;MACrBC,UAAU;MACVC;IACF,CAAC,GAAG9B,MAAM;IACV,IAAI+B,SAAS,GAAG/B,MAAM;IACtB,IAAI+B,SAAS,CAACxB,OAAO,IAAIwB,SAAS,CAAC1B,cAAc,EAAE;MACjD,MAAM2B,SAAS,GAAG3C,YAAY,CAAC0C,SAAS,EAAE5B,SAAS,CAAC;MACpD,MAAM8B,WAAW,GAAGnC,YAAY,CAACoC,IAAI,CAAC,CAAC;QACrCrB;MACF,CAAC,KAAKmB,SAAS,KAAKnB,GAAG,CAAC;MACxBkB,SAAS,GAAGI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,SAAS,CAAC,EAAE;QACtDM,KAAK,EAAEC,WAAW,KAAK,aAAanD,KAAK,CAACoD,aAAa,CAAC/C,cAAc,EAAE;UACtEgD,cAAc,EAAEnB,SAAS;UACzBA,SAAS,EAAE,GAAGA,SAAS,SAAS;UAChCC,iBAAiB,EAAEA,iBAAiB;UACpCtB,MAAM,EAAE+B,SAAS;UACjBC,SAAS,EAAEA,SAAS;UACpBC,WAAW,EAAEA,WAAW;UACxBN,aAAa,EAAEA,aAAa;UAC5BC,cAAc,EAAEA,cAAc;UAC9BC,UAAU,EAAEA,UAAU;UACtBC,YAAY,EAAEA,YAAY;UAC1BN,aAAa,EAAEA,aAAa;UAC5BD,MAAM,EAAEA,MAAM;UACdE,iBAAiB,EAAEA,iBAAiB;UACpCC,aAAa,EAAEA;QACjB,CAAC,EAAEnC,iBAAiB,CAACS,MAAM,CAACqC,KAAK,EAAEC,WAAW,CAAC,CAAC;MAClD,CAAC,CAAC;IACJ;IACA,IAAI,UAAU,IAAIP,SAAS,EAAE;MAC3BA,SAAS,GAAGI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,SAAS,CAAC,EAAE;QACtDZ,QAAQ,EAAEC,YAAY,CAACC,SAAS,EAAEC,iBAAiB,EAAES,SAAS,CAACZ,QAAQ,EAAErB,YAAY,EAAEyB,MAAM,EAAEC,aAAa,EAAEC,iBAAiB,EAAEtB,SAAS,EAAEuB,aAAa;MAC3J,CAAC,CAAC;IACJ;IACA,OAAOK,SAAS;EAClB,CAAC,CAAC;AACJ;AACA,MAAMU,kBAAkB,GAAG3C,YAAY,IAAI;EACzC,MAAM4C,cAAc,GAAG,CAAC,CAAC;EACzB5C,YAAY,CAACC,OAAO,CAAC,CAAC;IACpBc,GAAG;IACHC,YAAY;IACZd;EACF,CAAC,KAAK;IACJ,MAAM2C,WAAW,GAAG9B,GAAG;IACvB,MAAM;MACJN,OAAO;MACPF;IACF,CAAC,GAAGL,MAAM;IACV,IAAIK,cAAc,EAAE;MAClBqC,cAAc,CAACC,WAAW,CAAC,GAAG7B,YAAY,IAAI,IAAI;IACpD,CAAC,MAAM,IAAI8B,KAAK,CAACC,OAAO,CAAC/B,YAAY,CAAC,EAAE;MACtC,MAAMgC,IAAI,GAAGrD,WAAW,CAACc,OAAO,CAAC;MACjCmC,cAAc,CAACC,WAAW,CAAC,GAAGG,IAAI,CAACC,MAAM,CAACC,SAAS,IAAIlC,YAAY,CAACmC,QAAQ,CAACtC,MAAM,CAACqC,SAAS,CAAC,CAAC,CAAC;IAClG,CAAC,MAAM;MACLN,cAAc,CAACC,WAAW,CAAC,GAAG,IAAI;IACpC;EACF,CAAC,CAAC;EACF,OAAOD,cAAc;AACvB,CAAC;AACD,OAAO,MAAMQ,aAAa,GAAGA,CAACC,IAAI,EAAErD,YAAY,EAAEsD,kBAAkB,KAAK;EACvE,MAAMC,WAAW,GAAGvD,YAAY,CAACwD,MAAM,CAAC,CAACC,WAAW,EAAEtB,WAAW,KAAK;IACpE,MAAM;MACJjC,MAAM,EAAE;QACNwD,QAAQ;QACRjD;MACF,CAAC;MACDO;IACF,CAAC,GAAGmB,WAAW;IACf,IAAIuB,QAAQ,IAAI1C,YAAY,IAAIA,YAAY,CAAC2C,MAAM,EAAE;MACnD,OAAOF;MACP;MAAA,CACC7C,GAAG,CAACgD,MAAM,IAAIvB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAAC,CAAC,CAACX,MAAM,CAACW,MAAM,IAAI5C,YAAY,CAAC6C,IAAI,CAAC9C,GAAG,IAAI;QAClF,MAAMiC,IAAI,GAAGrD,WAAW,CAACc,OAAO,CAAC;QACjC,MAAMqD,QAAQ,GAAGd,IAAI,CAACe,SAAS,CAACC,CAAC,IAAInD,MAAM,CAACmD,CAAC,CAAC,KAAKnD,MAAM,CAACE,GAAG,CAAC,CAAC;QAC/D,MAAMkD,OAAO,GAAGH,QAAQ,KAAK,CAAC,CAAC,GAAGd,IAAI,CAACc,QAAQ,CAAC,GAAG/C,GAAG;QACtD;QACA,IAAI6C,MAAM,CAACN,kBAAkB,CAAC,EAAE;UAC9BM,MAAM,CAACN,kBAAkB,CAAC,GAAGF,aAAa,CAACQ,MAAM,CAACN,kBAAkB,CAAC,EAAEtD,YAAY,EAAEsD,kBAAkB,CAAC;QAC1G;QACA,OAAOI,QAAQ,CAACO,OAAO,EAAEL,MAAM,CAAC;MAClC,CAAC,CAAC,CAAC;IACL;IACA,OAAOH,WAAW;EACpB,CAAC,EAAEJ,IAAI,CAAC;EACR,OAAOE,WAAW;AACpB,CAAC;AACD,MAAMW,gBAAgB,GAAGC,gBAAgB,IAAIA,gBAAgB,CAACC,OAAO,CAAClE,MAAM,IAAI;EAC9E,IAAI,UAAU,IAAIA,MAAM,EAAE;IACxB,OAAO,CAACA,MAAM,CAAC,CAACkB,MAAM,CAAChC,kBAAkB,CAAC8E,gBAAgB,CAAChE,MAAM,CAACmB,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;EACrF;EACA,OAAO,CAACnB,MAAM,CAAC;AACjB,CAAC,CAAC;AACF,MAAMmE,SAAS,GAAGC,KAAK,IAAI;EACzB,MAAM;IACJ/C,SAAS;IACTC,iBAAiB;IACjB+C,aAAa,EAAEJ,gBAAgB;IAC/BK,cAAc;IACd7C,iBAAiB;IACjBF,MAAM,EAAEgD,WAAW;IACnB7C;EACF,CAAC,GAAG0C,KAAK;EACT,MAAMI,OAAO,GAAGpF,aAAa,CAAC,OAAO,CAAC;EACtC,MAAMiF,aAAa,GAAGlF,KAAK,CAACsF,OAAO,CAAC,MAAMT,gBAAgB,CAACC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EACvG,MAAM,CAACnE,YAAY,EAAE4E,eAAe,CAAC,GAAGvF,KAAK,CAACwF,QAAQ,CAAC,MAAMjF,mBAAmB,CAAC2E,aAAa,EAAE,IAAI,CAAC,CAAC;EACtG,MAAMO,kBAAkB,GAAGzF,KAAK,CAACsF,OAAO,CAAC,MAAM;IAC7C,MAAMI,eAAe,GAAGnF,mBAAmB,CAAC2E,aAAa,EAAE,KAAK,CAAC;IACjE,IAAIQ,eAAe,CAACpB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAOoB,eAAe;IACxB;IACA,IAAIC,8BAA8B,GAAG,IAAI;IACzC,IAAIC,2BAA2B,GAAG,IAAI;IACtCF,eAAe,CAAC9E,OAAO,CAAC,CAAC;MACvBe;IACF,CAAC,KAAK;MACJ,IAAIA,YAAY,KAAKR,SAAS,EAAE;QAC9BwE,8BAA8B,GAAG,KAAK;MACxC,CAAC,MAAM;QACLC,2BAA2B,GAAG,KAAK;MACrC;IACF,CAAC,CAAC;IACF;IACA,IAAID,8BAA8B,EAAE;MAClC;MACA,MAAME,OAAO,GAAG,CAACX,aAAa,IAAI,EAAE,EAAE3D,GAAG,CAAC,CAACV,MAAM,EAAEC,KAAK,KAAKZ,YAAY,CAACW,MAAM,EAAEV,YAAY,CAACW,KAAK,CAAC,CAAC,CAAC;MACvG,OAAOH,YAAY,CAACiD,MAAM,CAAC,CAAC;QAC1BlC;MACF,CAAC,KAAKmE,OAAO,CAAC/B,QAAQ,CAACpC,GAAG,CAAC,CAAC,CAACH,GAAG,CAACuE,IAAI,IAAI;QACvC,MAAMC,GAAG,GAAGb,aAAa,CAACW,OAAO,CAACnB,SAAS,CAAChD,GAAG,IAAIA,GAAG,KAAKoE,IAAI,CAACpE,GAAG,CAAC,CAAC;QACrE,OAAOsB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE6C,IAAI,CAAC,EAAE;UAC5CjF,MAAM,EAAEmC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE6C,IAAI,CAACjF,MAAM,CAAC,EAAEkF,GAAG,CAAC;UAC1DnE,aAAa,EAAEmE,GAAG,CAAClE;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAmE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGb,OAAO,CAACO,2BAA2B,EAAE,OAAO,EAAE,4EAA4E,CAAC,GAAG,KAAK,CAAC;IAC5K,OAAOF,eAAe;EACxB,CAAC,EAAE,CAACR,aAAa,EAAEvE,YAAY,CAAC,CAAC;EACjC,MAAMS,OAAO,GAAGpB,KAAK,CAACsF,OAAO,CAAC,MAAMhC,kBAAkB,CAACmC,kBAAkB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EACjG,MAAMpD,aAAa,GAAGS,WAAW,IAAI;IACnC,MAAMqD,eAAe,GAAGV,kBAAkB,CAAC7B,MAAM,CAAC,CAAC;MACjDlC;IACF,CAAC,KAAKA,GAAG,KAAKoB,WAAW,CAACpB,GAAG,CAAC;IAC9ByE,eAAe,CAAC1E,IAAI,CAACqB,WAAW,CAAC;IACjCyC,eAAe,CAACY,eAAe,CAAC;IAChChB,cAAc,CAAC7B,kBAAkB,CAAC6C,eAAe,CAAC,EAAEA,eAAe,CAAC;EACtE,CAAC;EACD,MAAMC,gBAAgB,GAAGC,YAAY,IAAIpE,YAAY,CAACC,SAAS,EAAEC,iBAAiB,EAAEkE,YAAY,EAAEZ,kBAAkB,EAAEL,WAAW,EAAE/C,aAAa,EAAEC,iBAAiB,EAAEnB,SAAS,EAAEoB,aAAa,CAAC;EAC9L,OAAO,CAAC6D,gBAAgB,EAAEX,kBAAkB,EAAErE,OAAO,CAAC;AACxD,CAAC;AACD,SAASd,WAAW;AACpB,eAAe0E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}