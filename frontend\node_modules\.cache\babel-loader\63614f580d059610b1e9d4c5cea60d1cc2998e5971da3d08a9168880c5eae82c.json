{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DropboxOutlinedSvg from \"@ant-design/icons-svg/es/asn/DropboxOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DropboxOutlined = function DropboxOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DropboxOutlinedSvg\n  }));\n};\n\n/**![dropbox](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY0IDU1Ni45bDI2NC4yIDE3My41TDUxMi41IDU3NyAyNDYuOCA0MTIuN3ptODk2LTI5MC4zem0wIDBMNjk2LjggOTUgNTEyLjUgMjQ4LjVsMjY1LjIgMTY0LjJMNTEyLjUgNTc3bDE4NC4zIDE1My40TDk2MCA1NTguOCA3NzcuNyA0MTIuN3pNNTEzIDYwOS44TDMyOC4yIDc2My4zbC03OS40LTUxLjV2NTcuOEw1MTMgOTI4bDI2My43LTE1OC40di01Ny44bC03OC45IDUxLjV6TTMyOC4yIDk1TDY0IDI2NS4xbDE4Mi44IDE0Ny42IDI2NS43LTE2NC4yek02NCA1NTYuOXoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DropboxOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DropboxOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "DropboxOutlinedSvg", "AntdIcon", "DropboxOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["E:/code1/task3/frontend/node_modules/@ant-design/icons/es/icons/DropboxOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DropboxOutlinedSvg from \"@ant-design/icons-svg/es/asn/DropboxOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DropboxOutlined = function DropboxOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DropboxOutlinedSvg\n  }));\n};\n\n/**![dropbox](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY0IDU1Ni45bDI2NC4yIDE3My41TDUxMi41IDU3NyAyNDYuOCA0MTIuN3ptODk2LTI5MC4zem0wIDBMNjk2LjggOTUgNTEyLjUgMjQ4LjVsMjY1LjIgMTY0LjJMNTEyLjUgNTc3bDE4NC4zIDE1My40TDk2MCA1NTguOCA3NzcuNyA0MTIuN3pNNTEzIDYwOS44TDMyOC4yIDc2My4zbC03OS40LTUxLjV2NTcuOEw1MTMgOTI4bDI2My43LTE1OC40di01Ny44bC03OC45IDUxLjV6TTMyOC4yIDk1TDY0IDI2NS4xbDE4Mi44IDE0Ny42IDI2NS43LTE2NC4yek02NCA1NTYuOXoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DropboxOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DropboxOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}