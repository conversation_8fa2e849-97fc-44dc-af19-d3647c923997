"""
安全模块补充测试
"""
import pytest
import os
import tempfile
import platform
from app.utils.security import validate_file_path, limit_execution_time, limit_memory_usage

class TestSecurityAdditional:
    """安全模块补充测试"""
    
    def test_validate_file_path_absolute(self):
        """测试绝对路径验证"""
        # 测试绝对路径
        if platform.system() == "Windows":
            assert validate_file_path("C:\\temp\\test.txt") == "C:\\temp\\test.txt"
        else:
            assert validate_file_path("/tmp/test.txt") == "/tmp/test.txt"
    
    def test_validate_file_path_relative(self):
        """测试相对路径验证"""
        assert validate_file_path("test.txt") == "test.txt"
        assert validate_file_path("./test.txt") == "./test.txt"
    
    def test_validate_file_path_dangerous(self):
        """测试危险路径验证"""
        with pytest.raises(ValueError):
            validate_file_path("../../../etc/passwd")
        
        with pytest.raises(ValueError):
            validate_file_path("..\\..\\windows\\system32")
    
    def test_validate_file_path_none(self):
        """测试空路径验证"""
        with pytest.raises(ValueError):
            validate_file_path(None)
        
        with pytest.raises(ValueError):
            validate_file_path("")
    
    def test_limit_execution_time_decorator(self):
        """测试执行时间限制装饰器"""
        @limit_execution_time(1)
        def quick_function():
            return "completed"
        
        # 在支持的系统上测试
        result = quick_function()
        assert result == "completed"
    
    def test_limit_execution_time_long_running(self):
        """测试长时间运行的函数"""
        import time
        
        @limit_execution_time(1)
        def slow_function():
            time.sleep(0.1)  # 短暂延迟，不会触发超时
            return "completed"
        
        result = slow_function()
        assert result == "completed"
    
    def test_limit_memory_usage_function(self):
        """测试内存限制函数"""
        # 这个函数在Windows上会显示警告，但不会失败
        try:
            limit_memory_usage(100)  # 100MB限制
            # 如果没有异常，说明函数正常工作
            assert True
        except Exception:
            # 在不支持的系统上可能会有异常
            assert True
    
    def test_validate_file_path_edge_cases(self):
        """测试文件路径边界情况"""
        # 测试长路径
        long_path = "a" * 255 + ".txt"
        result = validate_file_path(long_path)
        assert result == long_path
        
        # 测试特殊字符
        special_path = "test_file-123.txt"
        result = validate_file_path(special_path)
        assert result == special_path
    
    def test_validate_file_path_with_spaces(self):
        """测试包含空格的路径"""
        path_with_spaces = "test file.txt"
        result = validate_file_path(path_with_spaces)
        assert result == path_with_spaces
    
    def test_validate_file_path_unicode(self):
        """测试Unicode路径"""
        unicode_path = "测试文件.txt"
        result = validate_file_path(unicode_path)
        assert result == unicode_path
