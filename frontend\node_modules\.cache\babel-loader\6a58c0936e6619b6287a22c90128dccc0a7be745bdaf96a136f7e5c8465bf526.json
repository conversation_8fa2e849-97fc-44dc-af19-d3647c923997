{"ast": null, "code": "export function getMotion(prefixCls, transitionName) {\n  return {\n    motionName: transitionName !== null && transitionName !== void 0 ? transitionName : `${prefixCls}-move-up`\n  };\n}\n/** Wrap message open with promise like function */\nexport function wrapPromiseFn(openFn) {\n  let closeFn;\n  const closePromise = new Promise(resolve => {\n    closeFn = openFn(() => {\n      resolve(true);\n    });\n  });\n  const result = () => {\n    closeFn === null || closeFn === void 0 ? void 0 : closeFn();\n  };\n  result.then = (filled, rejected) => closePromise.then(filled, rejected);\n  result.promise = closePromise;\n  return result;\n}", "map": {"version": 3, "names": ["getMotion", "prefixCls", "transitionName", "motionName", "wrapPromiseFn", "openFn", "closeFn", "closePromise", "Promise", "resolve", "result", "then", "filled", "rejected", "promise"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/message/util.js"], "sourcesContent": ["export function getMotion(prefixCls, transitionName) {\n  return {\n    motionName: transitionName !== null && transitionName !== void 0 ? transitionName : `${prefixCls}-move-up`\n  };\n}\n/** Wrap message open with promise like function */\nexport function wrapPromiseFn(openFn) {\n  let closeFn;\n  const closePromise = new Promise(resolve => {\n    closeFn = openFn(() => {\n      resolve(true);\n    });\n  });\n  const result = () => {\n    closeFn === null || closeFn === void 0 ? void 0 : closeFn();\n  };\n  result.then = (filled, rejected) => closePromise.then(filled, rejected);\n  result.promise = closePromise;\n  return result;\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,SAAS,EAAEC,cAAc,EAAE;EACnD,OAAO;IACLC,UAAU,EAAED,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,GAAGD,SAAS;EAClG,CAAC;AACH;AACA;AACA,OAAO,SAASG,aAAaA,CAACC,MAAM,EAAE;EACpC,IAAIC,OAAO;EACX,MAAMC,YAAY,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC1CH,OAAO,GAAGD,MAAM,CAAC,MAAM;MACrBI,OAAO,CAAC,IAAI,CAAC;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;EAC7D,CAAC;EACDI,MAAM,CAACC,IAAI,GAAG,CAACC,MAAM,EAAEC,QAAQ,KAAKN,YAAY,CAACI,IAAI,CAACC,MAAM,EAAEC,QAAQ,CAAC;EACvEH,MAAM,CAACI,OAAO,GAAGP,YAAY;EAC7B,OAAOG,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}