"""
简单的API测试 - 无标记版本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_import_mock_app():
    """测试导入Mock应用"""
    try:
        from tests.mock_app import create_mock_app
        app = create_mock_app()
        assert app is not None
        print("✅ Mock应用导入成功")
    except Exception as e:
        print(f"❌ Mock应用导入失败: {e}")
        assert False, f"Mock应用导入失败: {e}"

def test_mock_app_endpoints():
    """测试Mock应用端点"""
    try:
        from tests.mock_app import create_mock_app
        app = create_mock_app()
        client = app.test_client()
        
        # 测试基本端点
        response = client.get('/api/tasks')
        assert response.status_code == 200
        print("✅ GET /api/tasks 正常")
        
        response = client.get('/api/scripts')
        assert response.status_code == 200
        print("✅ GET /api/scripts 正常")
        
        response = client.get('/api/workflows')
        assert response.status_code == 200
        print("✅ GET /api/workflows 正常")
        
    except Exception as e:
        print(f"❌ 端点测试失败: {e}")
        assert False, f"端点测试失败: {e}"

def test_api_post_requests():
    """测试API POST请求"""
    try:
        from tests.mock_app import create_mock_app
        import json
        
        app = create_mock_app()
        client = app.test_client()
        
        auth_headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'
        }
        
        # 测试创建任务
        task_data = {
            'name': '测试任务',
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        print("✅ POST /api/tasks 正常")
        
    except Exception as e:
        print(f"❌ POST请求测试失败: {e}")
        assert False, f"POST请求测试失败: {e}"

if __name__ == "__main__":
    print("运行简单API测试...")
    test_import_mock_app()
    test_mock_app_endpoints()
    test_api_post_requests()
    print("✅ 所有测试通过")
