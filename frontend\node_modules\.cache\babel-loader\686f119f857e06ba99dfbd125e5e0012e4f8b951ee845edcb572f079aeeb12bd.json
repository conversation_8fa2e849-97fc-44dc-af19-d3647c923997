{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genInputStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    fontSizeSM,\n    lineHeightSM,\n    colorPickerAlphaInputWidth,\n    marginXXS,\n    paddingXXS,\n    controlHeightSM,\n    marginXS,\n    fontSizeIcon,\n    paddingXS,\n    colorTextPlaceholder,\n    colorPickerInputNumberHandleWidth,\n    lineWidth\n  } = token;\n  return {\n    [`${componentCls}-input-container`]: {\n      display: 'flex',\n      [`${componentCls}-steppers${antCls}-input-number`]: {\n        fontSize: fontSizeSM,\n        lineHeight: lineHeightSM,\n        [`${antCls}-input-number-input`]: {\n          paddingInlineStart: paddingXXS,\n          paddingInlineEnd: 0\n        },\n        [`${antCls}-input-number-handler-wrap`]: {\n          width: colorPickerInputNumberHandleWidth\n        }\n      },\n      [`${componentCls}-steppers${componentCls}-alpha-input`]: {\n        flex: `0 0 ${unit(colorPickerAlphaInputWidth)}`,\n        marginInlineStart: marginXXS\n      },\n      [`${componentCls}-format-select${antCls}-select`]: {\n        marginInlineEnd: marginXS,\n        width: 'auto',\n        '&-single': {\n          [`${antCls}-select-selector`]: {\n            padding: 0,\n            border: 0\n          },\n          [`${antCls}-select-arrow`]: {\n            insetInlineEnd: 0\n          },\n          [`${antCls}-select-selection-item`]: {\n            paddingInlineEnd: token.calc(fontSizeIcon).add(marginXXS).equal(),\n            fontSize: fontSizeSM,\n            lineHeight: unit(controlHeightSM)\n          },\n          [`${antCls}-select-item-option-content`]: {\n            fontSize: fontSizeSM,\n            lineHeight: lineHeightSM\n          },\n          [`${antCls}-select-dropdown`]: {\n            [`${antCls}-select-item`]: {\n              minHeight: 'auto'\n            }\n          }\n        }\n      },\n      [`${componentCls}-input`]: {\n        gap: marginXXS,\n        alignItems: 'center',\n        flex: 1,\n        width: 0,\n        [`${componentCls}-hsb-input,${componentCls}-rgb-input`]: {\n          display: 'flex',\n          gap: marginXXS,\n          alignItems: 'center'\n        },\n        [`${componentCls}-steppers`]: {\n          flex: 1\n        },\n        [`${componentCls}-hex-input${antCls}-input-affix-wrapper`]: {\n          flex: 1,\n          padding: `0 ${unit(paddingXS)}`,\n          [`${antCls}-input`]: {\n            fontSize: fontSizeSM,\n            textTransform: 'uppercase',\n            lineHeight: unit(token.calc(controlHeightSM).sub(token.calc(lineWidth).mul(2)).equal())\n          },\n          [`${antCls}-input-prefix`]: {\n            color: colorTextPlaceholder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genInputStyle;", "map": {"version": 3, "names": ["unit", "genInputStyle", "token", "componentCls", "antCls", "fontSizeSM", "lineHeightSM", "colorPickerAlphaInputWidth", "marginXXS", "paddingXXS", "controlHeightSM", "marginXS", "fontSizeIcon", "paddingXS", "colorTextPlaceholder", "colorPickerInputNumberHandleWidth", "lineWidth", "display", "fontSize", "lineHeight", "paddingInlineStart", "paddingInlineEnd", "width", "flex", "marginInlineStart", "marginInlineEnd", "padding", "border", "insetInlineEnd", "calc", "add", "equal", "minHeight", "gap", "alignItems", "textTransform", "sub", "mul", "color"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/color-picker/style/input.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genInputStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    fontSizeSM,\n    lineHeightSM,\n    colorPickerAlphaInputWidth,\n    marginXXS,\n    paddingXXS,\n    controlHeightSM,\n    marginXS,\n    fontSizeIcon,\n    paddingXS,\n    colorTextPlaceholder,\n    colorPickerInputNumberHandleWidth,\n    lineWidth\n  } = token;\n  return {\n    [`${componentCls}-input-container`]: {\n      display: 'flex',\n      [`${componentCls}-steppers${antCls}-input-number`]: {\n        fontSize: fontSizeSM,\n        lineHeight: lineHeightSM,\n        [`${antCls}-input-number-input`]: {\n          paddingInlineStart: paddingXXS,\n          paddingInlineEnd: 0\n        },\n        [`${antCls}-input-number-handler-wrap`]: {\n          width: colorPickerInputNumberHandleWidth\n        }\n      },\n      [`${componentCls}-steppers${componentCls}-alpha-input`]: {\n        flex: `0 0 ${unit(colorPickerAlphaInputWidth)}`,\n        marginInlineStart: marginXXS\n      },\n      [`${componentCls}-format-select${antCls}-select`]: {\n        marginInlineEnd: marginXS,\n        width: 'auto',\n        '&-single': {\n          [`${antCls}-select-selector`]: {\n            padding: 0,\n            border: 0\n          },\n          [`${antCls}-select-arrow`]: {\n            insetInlineEnd: 0\n          },\n          [`${antCls}-select-selection-item`]: {\n            paddingInlineEnd: token.calc(fontSizeIcon).add(marginXXS).equal(),\n            fontSize: fontSizeSM,\n            lineHeight: unit(controlHeightSM)\n          },\n          [`${antCls}-select-item-option-content`]: {\n            fontSize: fontSizeSM,\n            lineHeight: lineHeightSM\n          },\n          [`${antCls}-select-dropdown`]: {\n            [`${antCls}-select-item`]: {\n              minHeight: 'auto'\n            }\n          }\n        }\n      },\n      [`${componentCls}-input`]: {\n        gap: marginXXS,\n        alignItems: 'center',\n        flex: 1,\n        width: 0,\n        [`${componentCls}-hsb-input,${componentCls}-rgb-input`]: {\n          display: 'flex',\n          gap: marginXXS,\n          alignItems: 'center'\n        },\n        [`${componentCls}-steppers`]: {\n          flex: 1\n        },\n        [`${componentCls}-hex-input${antCls}-input-affix-wrapper`]: {\n          flex: 1,\n          padding: `0 ${unit(paddingXS)}`,\n          [`${antCls}-input`]: {\n            fontSize: fontSizeSM,\n            textTransform: 'uppercase',\n            lineHeight: unit(token.calc(controlHeightSM).sub(token.calc(lineWidth).mul(2)).equal())\n          },\n          [`${antCls}-input-prefix`]: {\n            color: colorTextPlaceholder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genInputStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,UAAU;IACVC,YAAY;IACZC,0BAA0B;IAC1BC,SAAS;IACTC,UAAU;IACVC,eAAe;IACfC,QAAQ;IACRC,YAAY;IACZC,SAAS;IACTC,oBAAoB;IACpBC,iCAAiC;IACjCC;EACF,CAAC,GAAGd,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,kBAAkB,GAAG;MACnCc,OAAO,EAAE,MAAM;MACf,CAAC,GAAGd,YAAY,YAAYC,MAAM,eAAe,GAAG;QAClDc,QAAQ,EAAEb,UAAU;QACpBc,UAAU,EAAEb,YAAY;QACxB,CAAC,GAAGF,MAAM,qBAAqB,GAAG;UAChCgB,kBAAkB,EAAEX,UAAU;UAC9BY,gBAAgB,EAAE;QACpB,CAAC;QACD,CAAC,GAAGjB,MAAM,4BAA4B,GAAG;UACvCkB,KAAK,EAAEP;QACT;MACF,CAAC;MACD,CAAC,GAAGZ,YAAY,YAAYA,YAAY,cAAc,GAAG;QACvDoB,IAAI,EAAE,OAAOvB,IAAI,CAACO,0BAA0B,CAAC,EAAE;QAC/CiB,iBAAiB,EAAEhB;MACrB,CAAC;MACD,CAAC,GAAGL,YAAY,iBAAiBC,MAAM,SAAS,GAAG;QACjDqB,eAAe,EAAEd,QAAQ;QACzBW,KAAK,EAAE,MAAM;QACb,UAAU,EAAE;UACV,CAAC,GAAGlB,MAAM,kBAAkB,GAAG;YAC7BsB,OAAO,EAAE,CAAC;YACVC,MAAM,EAAE;UACV,CAAC;UACD,CAAC,GAAGvB,MAAM,eAAe,GAAG;YAC1BwB,cAAc,EAAE;UAClB,CAAC;UACD,CAAC,GAAGxB,MAAM,wBAAwB,GAAG;YACnCiB,gBAAgB,EAAEnB,KAAK,CAAC2B,IAAI,CAACjB,YAAY,CAAC,CAACkB,GAAG,CAACtB,SAAS,CAAC,CAACuB,KAAK,CAAC,CAAC;YACjEb,QAAQ,EAAEb,UAAU;YACpBc,UAAU,EAAEnB,IAAI,CAACU,eAAe;UAClC,CAAC;UACD,CAAC,GAAGN,MAAM,6BAA6B,GAAG;YACxCc,QAAQ,EAAEb,UAAU;YACpBc,UAAU,EAAEb;UACd,CAAC;UACD,CAAC,GAAGF,MAAM,kBAAkB,GAAG;YAC7B,CAAC,GAAGA,MAAM,cAAc,GAAG;cACzB4B,SAAS,EAAE;YACb;UACF;QACF;MACF,CAAC;MACD,CAAC,GAAG7B,YAAY,QAAQ,GAAG;QACzB8B,GAAG,EAAEzB,SAAS;QACd0B,UAAU,EAAE,QAAQ;QACpBX,IAAI,EAAE,CAAC;QACPD,KAAK,EAAE,CAAC;QACR,CAAC,GAAGnB,YAAY,cAAcA,YAAY,YAAY,GAAG;UACvDc,OAAO,EAAE,MAAM;UACfgB,GAAG,EAAEzB,SAAS;UACd0B,UAAU,EAAE;QACd,CAAC;QACD,CAAC,GAAG/B,YAAY,WAAW,GAAG;UAC5BoB,IAAI,EAAE;QACR,CAAC;QACD,CAAC,GAAGpB,YAAY,aAAaC,MAAM,sBAAsB,GAAG;UAC1DmB,IAAI,EAAE,CAAC;UACPG,OAAO,EAAE,KAAK1B,IAAI,CAACa,SAAS,CAAC,EAAE;UAC/B,CAAC,GAAGT,MAAM,QAAQ,GAAG;YACnBc,QAAQ,EAAEb,UAAU;YACpB8B,aAAa,EAAE,WAAW;YAC1BhB,UAAU,EAAEnB,IAAI,CAACE,KAAK,CAAC2B,IAAI,CAACnB,eAAe,CAAC,CAAC0B,GAAG,CAAClC,KAAK,CAAC2B,IAAI,CAACb,SAAS,CAAC,CAACqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC;UACxF,CAAC;UACD,CAAC,GAAG3B,MAAM,eAAe,GAAG;YAC1BkC,KAAK,EAAExB;UACT;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}