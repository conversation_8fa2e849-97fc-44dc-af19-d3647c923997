{"ast": null, "code": "var locale = {\n  // Options\n  items_per_page: '/ page',\n  jump_to: 'Go to',\n  jump_to_confirm: 'confirm',\n  page: 'Page',\n  // Pagination\n  prev_page: 'Previous Page',\n  next_page: 'Next Page',\n  prev_5: 'Previous 5 Pages',\n  next_5: 'Next 5 Pages',\n  prev_3: 'Previous 3 Pages',\n  next_3: 'Next 3 Pages',\n  page_size: 'Page Size'\n};\nexport default locale;", "map": {"version": 3, "names": ["locale", "items_per_page", "jump_to", "jump_to_confirm", "page", "prev_page", "next_page", "prev_5", "next_5", "prev_3", "next_3", "page_size"], "sources": ["E:/code1/task3/frontend/node_modules/rc-pagination/es/locale/en_US.js"], "sourcesContent": ["var locale = {\n  // Options\n  items_per_page: '/ page',\n  jump_to: 'Go to',\n  jump_to_confirm: 'confirm',\n  page: 'Page',\n  // Pagination\n  prev_page: 'Previous Page',\n  next_page: 'Next Page',\n  prev_5: 'Previous 5 Pages',\n  next_5: 'Next 5 Pages',\n  prev_3: 'Previous 3 Pages',\n  next_3: 'Next 3 Pages',\n  page_size: 'Page Size'\n};\nexport default locale;"], "mappings": "AAAA,IAAIA,MAAM,GAAG;EACX;EACAC,cAAc,EAAE,QAAQ;EACxBC,OAAO,EAAE,OAAO;EAChBC,eAAe,EAAE,SAAS;EAC1BC,IAAI,EAAE,MAAM;EACZ;EACAC,SAAS,EAAE,eAAe;EAC1BC,SAAS,EAAE,WAAW;EACtBC,MAAM,EAAE,kBAAkB;EAC1BC,MAAM,EAAE,cAAc;EACtBC,MAAM,EAAE,kBAAkB;EAC1BC,MAAM,EAAE,cAAc;EACtBC,SAAS,EAAE;AACb,CAAC;AACD,eAAeX,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}