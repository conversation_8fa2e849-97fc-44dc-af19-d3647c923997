{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedAnchorStyle = token => {\n  const {\n    componentCls,\n    holderOffsetBlock,\n    motionDurationSlow,\n    lineWidthBold,\n    colorPrimary,\n    lineType,\n    colorSplit,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      marginBlockStart: calc(holderOffsetBlock).mul(-1).equal(),\n      paddingBlockStart: holderOffsetBlock,\n      // delete overflow: auto\n      // overflow: 'auto',\n      [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n        position: 'relative',\n        paddingInlineStart: lineWidthBold,\n        [`${componentCls}-link`]: {\n          paddingBlock: token.linkPaddingBlock,\n          paddingInline: `${unit(token.linkPaddingInlineStart)} 0`,\n          '&-title': Object.assign(Object.assign({}, textEllipsis), {\n            position: 'relative',\n            display: 'block',\n            marginBlockEnd: token.anchorTitleBlock,\n            color: token.colorText,\n            transition: `all ${token.motionDurationSlow}`,\n            '&:only-child': {\n              marginBlockEnd: 0\n            }\n          }),\n          [`&-active > ${componentCls}-link-title`]: {\n            color: token.colorPrimary\n          },\n          // link link\n          [`${componentCls}-link`]: {\n            paddingBlock: token.anchorPaddingBlockSecondary\n          }\n        }\n      }),\n      [`&:not(${componentCls}-wrapper-horizontal)`]: {\n        [componentCls]: {\n          '&::before': {\n            position: 'absolute',\n            insetInlineStart: 0,\n            top: 0,\n            height: '100%',\n            borderInlineStart: `${unit(lineWidthBold)} ${lineType} ${colorSplit}`,\n            content: '\" \"'\n          },\n          [`${componentCls}-ink`]: {\n            position: 'absolute',\n            insetInlineStart: 0,\n            display: 'none',\n            transform: 'translateY(-50%)',\n            transition: `top ${motionDurationSlow} ease-in-out`,\n            width: lineWidthBold,\n            backgroundColor: colorPrimary,\n            [`&${componentCls}-ink-visible`]: {\n              display: 'inline-block'\n            }\n          }\n        }\n      },\n      [`${componentCls}-fixed ${componentCls}-ink ${componentCls}-ink`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nconst genSharedAnchorHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    lineWidthBold,\n    colorPrimary\n  } = token;\n  return {\n    [`${componentCls}-wrapper-horizontal`]: {\n      position: 'relative',\n      '&::before': {\n        position: 'absolute',\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: 0,\n        borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n        content: '\" \"'\n      },\n      [componentCls]: {\n        overflowX: 'scroll',\n        position: 'relative',\n        display: 'flex',\n        scrollbarWidth: 'none' /* Firefox */,\n        '&::-webkit-scrollbar': {\n          display: 'none' /* Safari and Chrome */\n        },\n        [`${componentCls}-link:first-of-type`]: {\n          paddingInline: 0\n        },\n        [`${componentCls}-ink`]: {\n          position: 'absolute',\n          bottom: 0,\n          transition: `left ${motionDurationSlow} ease-in-out, width ${motionDurationSlow} ease-in-out`,\n          height: lineWidthBold,\n          backgroundColor: colorPrimary\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  linkPaddingBlock: token.paddingXXS,\n  linkPaddingInlineStart: token.padding\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Anchor', token => {\n  const {\n    fontSize,\n    fontSizeLG,\n    paddingXXS,\n    calc\n  } = token;\n  const anchorToken = mergeToken(token, {\n    holderOffsetBlock: paddingXXS,\n    anchorPaddingBlockSecondary: calc(paddingXXS).div(2).equal(),\n    anchorTitleBlock: calc(fontSize).div(14).mul(3).equal(),\n    anchorBallSize: calc(fontSizeLG).div(2).equal()\n  });\n  return [genSharedAnchorStyle(anchorToken), genSharedAnchorHorizontalStyle(anchorToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "resetComponent", "textEllipsis", "genStyleHooks", "mergeToken", "genSharedAnchorStyle", "token", "componentCls", "holderOffsetBlock", "motionDurationSlow", "lineWidthBold", "colorPrimary", "lineType", "colorSplit", "calc", "marginBlockStart", "mul", "equal", "paddingBlockStart", "Object", "assign", "position", "paddingInlineStart", "paddingBlock", "linkPaddingBlock", "paddingInline", "linkPaddingInlineStart", "display", "marginBlockEnd", "anchorTitleBlock", "color", "colorText", "transition", "anchorPaddingBlockSecondary", "insetInlineStart", "top", "height", "borderInlineStart", "content", "transform", "width", "backgroundColor", "genSharedAnchorHorizontalStyle", "left", "_skip_check_", "value", "right", "bottom", "borderBottom", "lineWidth", "overflowX", "scrollbarWidth", "prepareComponentToken", "paddingXXS", "padding", "fontSize", "fontSizeLG", "anchorToken", "div", "anchorBallSize"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/anchor/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedAnchorStyle = token => {\n  const {\n    componentCls,\n    holderOffsetBlock,\n    motionDurationSlow,\n    lineWidthBold,\n    colorPrimary,\n    lineType,\n    colorSplit,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      marginBlockStart: calc(holderOffsetBlock).mul(-1).equal(),\n      paddingBlockStart: holderOffsetBlock,\n      // delete overflow: auto\n      // overflow: 'auto',\n      [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n        position: 'relative',\n        paddingInlineStart: lineWidthBold,\n        [`${componentCls}-link`]: {\n          paddingBlock: token.linkPaddingBlock,\n          paddingInline: `${unit(token.linkPaddingInlineStart)} 0`,\n          '&-title': Object.assign(Object.assign({}, textEllipsis), {\n            position: 'relative',\n            display: 'block',\n            marginBlockEnd: token.anchorTitleBlock,\n            color: token.colorText,\n            transition: `all ${token.motionDurationSlow}`,\n            '&:only-child': {\n              marginBlockEnd: 0\n            }\n          }),\n          [`&-active > ${componentCls}-link-title`]: {\n            color: token.colorPrimary\n          },\n          // link link\n          [`${componentCls}-link`]: {\n            paddingBlock: token.anchorPaddingBlockSecondary\n          }\n        }\n      }),\n      [`&:not(${componentCls}-wrapper-horizontal)`]: {\n        [componentCls]: {\n          '&::before': {\n            position: 'absolute',\n            insetInlineStart: 0,\n            top: 0,\n            height: '100%',\n            borderInlineStart: `${unit(lineWidthBold)} ${lineType} ${colorSplit}`,\n            content: '\" \"'\n          },\n          [`${componentCls}-ink`]: {\n            position: 'absolute',\n            insetInlineStart: 0,\n            display: 'none',\n            transform: 'translateY(-50%)',\n            transition: `top ${motionDurationSlow} ease-in-out`,\n            width: lineWidthBold,\n            backgroundColor: colorPrimary,\n            [`&${componentCls}-ink-visible`]: {\n              display: 'inline-block'\n            }\n          }\n        }\n      },\n      [`${componentCls}-fixed ${componentCls}-ink ${componentCls}-ink`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nconst genSharedAnchorHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    lineWidthBold,\n    colorPrimary\n  } = token;\n  return {\n    [`${componentCls}-wrapper-horizontal`]: {\n      position: 'relative',\n      '&::before': {\n        position: 'absolute',\n        left: {\n          _skip_check_: true,\n          value: 0\n        },\n        right: {\n          _skip_check_: true,\n          value: 0\n        },\n        bottom: 0,\n        borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n        content: '\" \"'\n      },\n      [componentCls]: {\n        overflowX: 'scroll',\n        position: 'relative',\n        display: 'flex',\n        scrollbarWidth: 'none' /* Firefox */,\n        '&::-webkit-scrollbar': {\n          display: 'none' /* Safari and Chrome */\n        },\n        [`${componentCls}-link:first-of-type`]: {\n          paddingInline: 0\n        },\n        [`${componentCls}-ink`]: {\n          position: 'absolute',\n          bottom: 0,\n          transition: `left ${motionDurationSlow} ease-in-out, width ${motionDurationSlow} ease-in-out`,\n          height: lineWidthBold,\n          backgroundColor: colorPrimary\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  linkPaddingBlock: token.paddingXXS,\n  linkPaddingInlineStart: token.padding\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Anchor', token => {\n  const {\n    fontSize,\n    fontSizeLG,\n    paddingXXS,\n    calc\n  } = token;\n  const anchorToken = mergeToken(token, {\n    holderOffsetBlock: paddingXXS,\n    anchorPaddingBlockSecondary: calc(paddingXXS).div(2).equal(),\n    anchorTitleBlock: calc(fontSize).div(14).mul(3).equal(),\n    anchorBallSize: calc(fontSizeLG).div(2).equal()\n  });\n  return [genSharedAnchorStyle(anchorToken), genSharedAnchorHorizontalStyle(anchorToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,EAAEC,YAAY,QAAQ,aAAa;AAC1D,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE;AACA,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC,YAAY;IACZC,iBAAiB;IACjBC,kBAAkB;IAClBC,aAAa;IACbC,YAAY;IACZC,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,GAAGR,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3BQ,gBAAgB,EAAED,IAAI,CAACN,iBAAiB,CAAC,CAACQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACzDC,iBAAiB,EAAEV,iBAAiB;MACpC;MACA;MACA,CAACD,YAAY,GAAGY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,cAAc,CAACK,KAAK,CAAC,CAAC,EAAE;QACtEe,QAAQ,EAAE,UAAU;QACpBC,kBAAkB,EAAEZ,aAAa;QACjC,CAAC,GAAGH,YAAY,OAAO,GAAG;UACxBgB,YAAY,EAAEjB,KAAK,CAACkB,gBAAgB;UACpCC,aAAa,EAAE,GAAGzB,IAAI,CAACM,KAAK,CAACoB,sBAAsB,CAAC,IAAI;UACxD,SAAS,EAAEP,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,YAAY,CAAC,EAAE;YACxDmB,QAAQ,EAAE,UAAU;YACpBM,OAAO,EAAE,OAAO;YAChBC,cAAc,EAAEtB,KAAK,CAACuB,gBAAgB;YACtCC,KAAK,EAAExB,KAAK,CAACyB,SAAS;YACtBC,UAAU,EAAE,OAAO1B,KAAK,CAACG,kBAAkB,EAAE;YAC7C,cAAc,EAAE;cACdmB,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UACF,CAAC,cAAcrB,YAAY,aAAa,GAAG;YACzCuB,KAAK,EAAExB,KAAK,CAACK;UACf,CAAC;UACD;UACA,CAAC,GAAGJ,YAAY,OAAO,GAAG;YACxBgB,YAAY,EAAEjB,KAAK,CAAC2B;UACtB;QACF;MACF,CAAC,CAAC;MACF,CAAC,SAAS1B,YAAY,sBAAsB,GAAG;QAC7C,CAACA,YAAY,GAAG;UACd,WAAW,EAAE;YACXc,QAAQ,EAAE,UAAU;YACpBa,gBAAgB,EAAE,CAAC;YACnBC,GAAG,EAAE,CAAC;YACNC,MAAM,EAAE,MAAM;YACdC,iBAAiB,EAAE,GAAGrC,IAAI,CAACU,aAAa,CAAC,IAAIE,QAAQ,IAAIC,UAAU,EAAE;YACrEyB,OAAO,EAAE;UACX,CAAC;UACD,CAAC,GAAG/B,YAAY,MAAM,GAAG;YACvBc,QAAQ,EAAE,UAAU;YACpBa,gBAAgB,EAAE,CAAC;YACnBP,OAAO,EAAE,MAAM;YACfY,SAAS,EAAE,kBAAkB;YAC7BP,UAAU,EAAE,OAAOvB,kBAAkB,cAAc;YACnD+B,KAAK,EAAE9B,aAAa;YACpB+B,eAAe,EAAE9B,YAAY;YAC7B,CAAC,IAAIJ,YAAY,cAAc,GAAG;cAChCoB,OAAO,EAAE;YACX;UACF;QACF;MACF,CAAC;MACD,CAAC,GAAGpB,YAAY,UAAUA,YAAY,QAAQA,YAAY,MAAM,GAAG;QACjEoB,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMe,8BAA8B,GAAGpC,KAAK,IAAI;EAC9C,MAAM;IACJC,YAAY;IACZE,kBAAkB;IAClBC,aAAa;IACbC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,qBAAqB,GAAG;MACtCc,QAAQ,EAAE,UAAU;MACpB,WAAW,EAAE;QACXA,QAAQ,EAAE,UAAU;QACpBsB,IAAI,EAAE;UACJC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDC,KAAK,EAAE;UACLF,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDE,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,GAAGhD,IAAI,CAACM,KAAK,CAAC2C,SAAS,CAAC,IAAI3C,KAAK,CAACM,QAAQ,IAAIN,KAAK,CAACO,UAAU,EAAE;QAC9EyB,OAAO,EAAE;MACX,CAAC;MACD,CAAC/B,YAAY,GAAG;QACd2C,SAAS,EAAE,QAAQ;QACnB7B,QAAQ,EAAE,UAAU;QACpBM,OAAO,EAAE,MAAM;QACfwB,cAAc,EAAE,MAAM,CAAC;QACvB,sBAAsB,EAAE;UACtBxB,OAAO,EAAE,MAAM,CAAC;QAClB,CAAC;QACD,CAAC,GAAGpB,YAAY,qBAAqB,GAAG;UACtCkB,aAAa,EAAE;QACjB,CAAC;QACD,CAAC,GAAGlB,YAAY,MAAM,GAAG;UACvBc,QAAQ,EAAE,UAAU;UACpB0B,MAAM,EAAE,CAAC;UACTf,UAAU,EAAE,QAAQvB,kBAAkB,uBAAuBA,kBAAkB,cAAc;UAC7F2B,MAAM,EAAE1B,aAAa;UACrB+B,eAAe,EAAE9B;QACnB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMyC,qBAAqB,GAAG9C,KAAK,KAAK;EAC7CkB,gBAAgB,EAAElB,KAAK,CAAC+C,UAAU;EAClC3B,sBAAsB,EAAEpB,KAAK,CAACgD;AAChC,CAAC,CAAC;AACF;AACA,eAAenD,aAAa,CAAC,QAAQ,EAAEG,KAAK,IAAI;EAC9C,MAAM;IACJiD,QAAQ;IACRC,UAAU;IACVH,UAAU;IACVvC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMmD,WAAW,GAAGrD,UAAU,CAACE,KAAK,EAAE;IACpCE,iBAAiB,EAAE6C,UAAU;IAC7BpB,2BAA2B,EAAEnB,IAAI,CAACuC,UAAU,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACzC,KAAK,CAAC,CAAC;IAC5DY,gBAAgB,EAAEf,IAAI,CAACyC,QAAQ,CAAC,CAACG,GAAG,CAAC,EAAE,CAAC,CAAC1C,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACvD0C,cAAc,EAAE7C,IAAI,CAAC0C,UAAU,CAAC,CAACE,GAAG,CAAC,CAAC,CAAC,CAACzC,KAAK,CAAC;EAChD,CAAC,CAAC;EACF,OAAO,CAACZ,oBAAoB,CAACoD,WAAW,CAAC,EAAEf,8BAA8B,CAACe,WAAW,CAAC,CAAC;AACzF,CAAC,EAAEL,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}