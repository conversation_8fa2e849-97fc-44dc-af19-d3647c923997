"""
简化的API测试
"""
import pytest
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.mock_app import create_mock_app


@pytest.fixture
def app():
    """创建测试应用"""
    return create_mock_app()


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture
def auth_headers():
    """认证头部"""
    return {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
    }


@pytest.mark.api
class TestTaskAPI:
    """任务API测试"""
    
    def test_get_tasks(self, client):
        """测试获取任务列表"""
        response = client.get('/api/tasks')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_task(self, client, auth_headers):
        """测试创建任务"""
        task_data = {
            'name': '测试任务',
            'description': '这是一个测试任务',
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert 'id' in data['data']
    
    def test_create_task_missing_name(self, client, auth_headers):
        """测试创建任务缺少名称"""
        task_data = {
            'description': '缺少名称的任务',
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400


@pytest.mark.api
class TestScriptAPI:
    """脚本API测试"""
    
    def test_get_scripts(self, client):
        """测试获取脚本列表"""
        response = client.get('/api/scripts')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_script(self, client, auth_headers):
        """测试创建脚本"""
        script_data = {
            'name': '测试脚本',
            'type': 'python',
            'content': 'print("Hello, World!")',
            'version': '1.0.0'
        }
        
        response = client.post('/api/scripts',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
    
    def test_validate_script(self, client, auth_headers):
        """测试脚本验证"""
        script_data = {
            'type': 'python',
            'content': 'print("Valid code")'
        }
        
        response = client.post('/api/scripts/validate',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True


@pytest.mark.api
class TestWorkflowAPI:
    """工作流API测试"""
    
    def test_get_workflows(self, client):
        """测试获取工作流列表"""
        response = client.get('/api/workflows')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_workflow(self, client, auth_headers):
        """测试创建工作流"""
        workflow_data = {
            'name': '测试工作流',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'task1',
                        'name': '任务1',
                        'task_id': 1,
                        'execution_type': 'serial'
                    }
                ],
                'edges': []
            }
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
    
    def test_validate_workflow(self, client, auth_headers):
        """测试工作流验证"""
        workflow_def = {
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1', 'task_id': 1}
                ],
                'edges': []
            }
        }
        
        response = client.post('/api/workflows/validate',
                             data=json.dumps(workflow_def),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True


@pytest.mark.performance
class TestAPIPerformance:
    """API性能测试"""
    
    def test_response_time(self, client):
        """测试响应时间"""
        import time
        
        start_time = time.time()
        response = client.get('/api/tasks')
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 1.0  # 响应时间小于1秒
