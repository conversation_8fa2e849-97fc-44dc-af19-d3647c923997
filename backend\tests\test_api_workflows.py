"""
工作流API专项测试
"""
import pytest
import json
import time
from unittest.mock import patch, MagicMock
from flask import Flask


@pytest.fixture
def sample_workflow_definition():
    """示例工作流定义"""
    return {
        'nodes': [
            {
                'id': 'start_task',
                'name': '开始任务',
                'task_id': 1,
                'execution_type': 'serial',
                'parameters': {
                    'input_file': '${workflow.input_file}',
                    'timeout': 300
                }
            },
            {
                'id': 'process_task',
                'name': '处理任务',
                'task_id': 2,
                'execution_type': 'parallel',
                'parameters': {
                    'input_data': '${start_task.output}',
                    'batch_size': 1000
                },
                'condition': 'start_task.status == "success"'
            },
            {
                'id': 'end_task',
                'name': '结束任务',
                'task_id': 3,
                'execution_type': 'serial',
                'parameters': {
                    'result_file': '${process_task.output_file}'
                },
                'condition': 'process_task.status == "success" and process_task.processed_count > 0'
            }
        ],
        'edges': [
            {
                'source': 'start_task',
                'target': 'process_task',
                'condition': 'success'
            },
            {
                'source': 'process_task',
                'target': 'end_task',
                'condition': 'success'
            }
        ],
        'global_parameters': {
            'max_retry_count': 3,
            'notification_email': '<EMAIL>'
        }
    }


@pytest.fixture
def complex_workflow_definition():
    """复杂工作流定义"""
    return {
        'nodes': [
            {
                'id': 'data_validation',
                'name': '数据验证',
                'task_id': 1,
                'execution_type': 'serial'
            },
            {
                'id': 'parallel_process_1',
                'name': '并行处理1',
                'task_id': 2,
                'execution_type': 'parallel'
            },
            {
                'id': 'parallel_process_2',
                'name': '并行处理2',
                'task_id': 3,
                'execution_type': 'parallel'
            },
            {
                'id': 'merge_results',
                'name': '合并结果',
                'task_id': 4,
                'execution_type': 'serial'
            },
            {
                'id': 'final_report',
                'name': '生成报告',
                'task_id': 5,
                'execution_type': 'serial'
            }
        ],
        'edges': [
            {'source': 'data_validation', 'target': 'parallel_process_1'},
            {'source': 'data_validation', 'target': 'parallel_process_2'},
            {'source': 'parallel_process_1', 'target': 'merge_results'},
            {'source': 'parallel_process_2', 'target': 'merge_results'},
            {'source': 'merge_results', 'target': 'final_report'}
        ]
    }


class TestWorkflowCRUD:
    """工作流CRUD操作测试"""
    
    def test_create_workflow_basic(self, client, auth_headers, sample_workflow_definition):
        """测试创建基本工作流"""
        workflow_data = {
            'name': '基本工作流',
            'description': '这是一个基本的工作流测试',
            'workflow_definition': sample_workflow_definition,
            'is_active': True
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert data['data']['name'] == workflow_data['name']
        assert 'id' in data['data']
        assert data['data']['is_active'] is True
    
    def test_create_workflow_complex(self, client, auth_headers, complex_workflow_definition):
        """测试创建复杂工作流"""
        workflow_data = {
            'name': '复杂工作流',
            'description': '包含并行处理的复杂工作流',
            'workflow_definition': complex_workflow_definition
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert len(data['data']['workflow_definition']['nodes']) == 5
    
    def test_create_workflow_missing_name(self, client, auth_headers, sample_workflow_definition):
        """测试创建工作流缺少名称"""
        workflow_data = {
            'description': '缺少名称的工作流',
            'workflow_definition': sample_workflow_definition
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert 'name' in data['message'].lower()
    
    def test_create_workflow_invalid_definition(self, client, auth_headers):
        """测试创建工作流无效定义"""
        workflow_data = {
            'name': '无效工作流',
            'workflow_definition': {
                'nodes': [],  # 空节点列表
                'edges': []
            }
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
    
    def test_get_workflow_by_id(self, client, auth_headers, sample_workflow_definition):
        """测试根据ID获取工作流"""
        # 先创建工作流
        workflow_data = {
            'name': '获取测试工作流',
            'workflow_definition': sample_workflow_definition
        }
        
        create_response = client.post('/api/workflows',
                                    data=json.dumps(workflow_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            workflow_id = json.loads(create_response.data)['data']['id']
            
            response = client.get(f'/api/workflows/{workflow_id}')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['data']['name'] == workflow_data['name']
    
    def test_get_workflow_not_found(self, client):
        """测试获取不存在的工作流"""
        response = client.get('/api/workflows/99999')
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['code'] == 404
    
    def test_update_workflow(self, client, auth_headers, sample_workflow_definition):
        """测试更新工作流"""
        # 先创建工作流
        workflow_data = {
            'name': '更新前工作流',
            'workflow_definition': sample_workflow_definition
        }
        
        create_response = client.post('/api/workflows',
                                    data=json.dumps(workflow_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            workflow_id = json.loads(create_response.data)['data']['id']
            
            update_data = {
                'name': '更新后工作流',
                'description': '更新后的描述',
                'is_active': False
            }
            
            response = client.put(f'/api/workflows/{workflow_id}',
                                data=json.dumps(update_data),
                                headers=auth_headers)
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['data']['name'] == update_data['name']
            assert data['data']['is_active'] is False
    
    def test_delete_workflow(self, client, auth_headers, sample_workflow_definition):
        """测试删除工作流"""
        # 先创建工作流
        workflow_data = {
            'name': '待删除工作流',
            'workflow_definition': sample_workflow_definition
        }
        
        create_response = client.post('/api/workflows',
                                    data=json.dumps(workflow_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            workflow_id = json.loads(create_response.data)['data']['id']
            
            response = client.delete(f'/api/workflows/{workflow_id}',
                                   headers=auth_headers)
            
            assert response.status_code == 200
            
            # 验证工作流已被删除
            get_response = client.get(f'/api/workflows/{workflow_id}')
            assert get_response.status_code == 404


class TestWorkflowValidation:
    """工作流验证测试"""
    
    def test_validate_workflow_valid(self, client, auth_headers, sample_workflow_definition):
        """测试验证有效工作流"""
        validation_data = {
            'workflow_definition': sample_workflow_definition
        }
        
        response = client.post('/api/workflows/validate',
                             data=json.dumps(validation_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['valid'] is True
        assert 'execution_plan' in data['data']
    
    def test_validate_workflow_circular_dependency(self, client, auth_headers):
        """测试验证循环依赖工作流"""
        circular_workflow = {
            'nodes': [
                {'id': 'task1', 'name': '任务1', 'task_id': 1},
                {'id': 'task2', 'name': '任务2', 'task_id': 2},
                {'id': 'task3', 'name': '任务3', 'task_id': 3}
            ],
            'edges': [
                {'source': 'task1', 'target': 'task2'},
                {'source': 'task2', 'target': 'task3'},
                {'source': 'task3', 'target': 'task1'}  # 循环依赖
            ]
        }
        
        validation_data = {
            'workflow_definition': circular_workflow
        }
        
        response = client.post('/api/workflows/validate',
                             data=json.dumps(validation_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is False
        assert 'cycle' in data['data']['errors'][0].lower()
    
    def test_validate_workflow_missing_node(self, client, auth_headers):
        """测试验证缺少节点的工作流"""
        invalid_workflow = {
            'nodes': [
                {'id': 'task1', 'name': '任务1', 'task_id': 1}
            ],
            'edges': [
                {'source': 'task1', 'target': 'task2'}  # task2不存在
            ]
        }
        
        validation_data = {
            'workflow_definition': invalid_workflow
        }
        
        response = client.post('/api/workflows/validate',
                             data=json.dumps(validation_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is False
        assert 'task2' in str(data['data']['errors'])
    
    def test_get_execution_plan(self, client, auth_headers, sample_workflow_definition):
        """测试获取执行计划"""
        plan_data = {
            'workflow_definition': sample_workflow_definition
        }
        
        response = client.post('/api/workflows/execution-plan',
                             data=json.dumps(plan_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'execution_order' in data['data']
        assert isinstance(data['data']['execution_order'], list)


class TestWorkflowExecution:
    """工作流执行测试"""
    
    @patch('app.scheduler.execute_workflow')
    def test_execute_workflow_success(self, mock_execute, client, auth_headers, sample_workflow_definition):
        """测试成功执行工作流"""
        mock_execute.return_value = {
            'status': 'started',
            'execution_id': 'workflow-exec-123',
            'estimated_duration': 300
        }
        
        # 先创建工作流
        workflow_data = {
            'name': '执行测试工作流',
            'workflow_definition': sample_workflow_definition
        }
        
        create_response = client.post('/api/workflows',
                                    data=json.dumps(workflow_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            workflow_id = json.loads(create_response.data)['data']['id']
            
            execution_data = {
                'parameters': {
                    'input_file': '/tmp/test_input.csv',
                    'output_dir': '/tmp/output'
                }
            }
            
            response = client.post(f'/api/workflows/{workflow_id}/execute',
                                 data=json.dumps(execution_data),
                                 headers=auth_headers)
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['code'] == 200
            assert data['data']['status'] == 'started'
            mock_execute.assert_called_once()
    
    def test_execute_workflow_not_found(self, client, auth_headers):
        """测试执行不存在的工作流"""
        response = client.post('/api/workflows/99999/execute',
                             data=json.dumps({}),
                             headers=auth_headers)
        
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['code'] == 404
    
    @patch('app.scheduler.get_workflow_execution_status')
    def test_get_execution_status(self, mock_get_status, client, auth_headers):
        """测试获取执行状态"""
        mock_get_status.return_value = {
            'execution_id': 'workflow-exec-123',
            'status': 'running',
            'progress': 0.6,
            'current_task': 'process_task',
            'start_time': '2024-01-01T10:00:00Z',
            'estimated_completion': '2024-01-01T10:15:00Z'
        }
        
        response = client.get('/api/workflows/executions/workflow-exec-123/status')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['status'] == 'running'
        assert data['data']['progress'] == 0.6
    
    @patch('app.scheduler.cancel_workflow_execution')
    def test_cancel_execution(self, mock_cancel, client, auth_headers):
        """测试取消执行"""
        mock_cancel.return_value = {
            'status': 'cancelled',
            'message': 'Workflow execution cancelled successfully'
        }
        
        response = client.post('/api/workflows/executions/workflow-exec-123/cancel',
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['status'] == 'cancelled'


class TestConditionValidation:
    """条件验证API测试"""
    
    def test_validate_condition_valid(self, client, auth_headers):
        """测试验证有效条件"""
        condition_data = {
            'condition': 'status == "success" and duration < 300',
            'context': {
                'status': 'success',
                'duration': 250,
                'error_count': 0
            }
        }
        
        response = client.post('/api/workflows/validate-condition',
                             data=json.dumps(condition_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['valid'] is True
        assert data['data']['result'] is True
    
    def test_validate_condition_invalid(self, client, auth_headers):
        """测试验证无效条件"""
        condition_data = {
            'condition': 'invalid syntax condition',
            'context': {}
        }
        
        response = client.post('/api/workflows/validate-condition',
                             data=json.dumps(condition_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is False
        assert 'error' in data['data']
    
    def test_get_condition_help(self, client):
        """测试获取条件帮助"""
        response = client.get('/api/workflows/condition-help')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'predefined_conditions' in data['data']
        assert 'operators' in data['data']
        assert 'functions' in data['data']
        assert 'examples' in data['data']


class TestParameterValidation:
    """参数验证API测试"""
    
    def test_validate_parameters(self, client, auth_headers):
        """测试参数验证"""
        parameter_data = {
            'parameters': {
                'input_file': '/tmp/input.csv',
                'batch_size': 1000,
                'timeout': 300,
                'enable_logging': True
            },
            'schema': {
                'input_file': {
                    'type': 'string',
                    'required': True,
                    'pattern': r'^/tmp/.*\.csv$'
                },
                'batch_size': {
                    'type': 'integer',
                    'min': 1,
                    'max': 10000
                },
                'timeout': {
                    'type': 'integer',
                    'min': 60,
                    'max': 3600
                },
                'enable_logging': {
                    'type': 'boolean'
                }
            }
        }
        
        response = client.post('/api/workflows/validate-parameters',
                             data=json.dumps(parameter_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['valid'] is True
    
    def test_validate_parameters_invalid(self, client, auth_headers):
        """测试无效参数验证"""
        parameter_data = {
            'parameters': {
                'batch_size': 50000,  # 超出最大值
                'timeout': 30  # 低于最小值
            },
            'schema': {
                'batch_size': {
                    'type': 'integer',
                    'max': 10000
                },
                'timeout': {
                    'type': 'integer',
                    'min': 60
                }
            }
        }
        
        response = client.post('/api/workflows/validate-parameters',
                             data=json.dumps(parameter_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is False
        assert len(data['data']['errors']) >= 2
    
    def test_get_parameter_schema(self, client):
        """测试获取参数模式"""
        response = client.get('/api/workflows/parameter-schema')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'types' in data['data']
        assert 'constraints' in data['data']
        assert 'examples' in data['data']


@pytest.mark.api
@pytest.mark.workflow
class TestWorkflowAPIIntegration:
    """工作流API集成测试"""
    
    def test_complete_workflow_lifecycle(self, client, auth_headers, sample_workflow_definition):
        """测试完整的工作流生命周期"""
        # 1. 创建工作流
        workflow_data = {
            'name': '生命周期测试工作流',
            'workflow_definition': sample_workflow_definition
        }
        
        create_response = client.post('/api/workflows',
                                    data=json.dumps(workflow_data),
                                    headers=auth_headers)
        
        assert create_response.status_code == 201
        workflow_id = json.loads(create_response.data)['data']['id']
        
        # 2. 验证工作流
        validation_data = {
            'workflow_definition': sample_workflow_definition
        }
        
        validate_response = client.post('/api/workflows/validate',
                                      data=json.dumps(validation_data),
                                      headers=auth_headers)
        
        assert validate_response.status_code == 200
        assert json.loads(validate_response.data)['data']['valid'] is True
        
        # 3. 获取执行计划
        plan_response = client.post('/api/workflows/execution-plan',
                                  data=json.dumps(validation_data),
                                  headers=auth_headers)
        
        assert plan_response.status_code == 200
        
        # 4. 执行工作流
        with patch('app.scheduler.execute_workflow') as mock_execute:
            mock_execute.return_value = {'status': 'started', 'execution_id': 'test-exec-123'}
            
            execute_response = client.post(f'/api/workflows/{workflow_id}/execute',
                                         data=json.dumps({}),
                                         headers=auth_headers)
            
            assert execute_response.status_code == 200
        
        # 5. 更新工作流
        update_data = {'description': '更新后的描述'}
        update_response = client.put(f'/api/workflows/{workflow_id}',
                                   data=json.dumps(update_data),
                                   headers=auth_headers)
        
        assert update_response.status_code == 200
        
        # 6. 删除工作流
        delete_response = client.delete(f'/api/workflows/{workflow_id}',
                                      headers=auth_headers)
        
        assert delete_response.status_code == 200
