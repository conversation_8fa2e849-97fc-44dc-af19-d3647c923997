from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Float, Boolean, JSON
from sqlalchemy.orm import relationship
from app.database import Base
from datetime import datetime

class TaskExecution(Base):
    __tablename__ = 'task_executions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False)
    status = Column(String(20), nullable=False)  # running, success, failed
    start_time = Column(DateTime, default=datetime.utcnow)
    end_time = Column(DateTime)
    duration = Column(Float)  # 执行时长（秒）
    output = Column(Text)  # 执行输出
    error_message = Column(Text)  # 错误信息
    input_parameters = Column(JSON)  # 输入参数
    output_parameters = Column(JSON)  # 输出参数
    return_code = Column(Integer, default=0)  # 返回码

    # 关联任务
    task = relationship('Task', back_populates='executions')