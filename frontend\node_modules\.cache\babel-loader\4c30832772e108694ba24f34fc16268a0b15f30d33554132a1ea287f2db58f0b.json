{"ast": null, "code": "// This icon file is generated automatically.\nvar ExclamationCircleTwoTone = {\n  \"icon\": function render(primaryColor, secondaryColor) {\n    return {\n      \"tag\": \"svg\",\n      \"attrs\": {\n        \"viewBox\": \"64 64 896 896\",\n        \"focusable\": \"false\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\",\n          \"fill\": primaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M512 140c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm-32 156c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z\",\n          \"fill\": secondaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M488 576h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8zm-24 112a48 48 0 1096 0 48 48 0 10-96 0z\",\n          \"fill\": primaryColor\n        }\n      }]\n    };\n  },\n  \"name\": \"exclamation-circle\",\n  \"theme\": \"twotone\"\n};\nexport default ExclamationCircleTwoTone;", "map": {"version": 3, "names": ["ExclamationCircleTwoTone", "render", "primaryColor", "secondaryColor"], "sources": ["E:/code1/task3/frontend/node_modules/@ant-design/icons-svg/es/asn/ExclamationCircleTwoTone.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar ExclamationCircleTwoTone = { \"icon\": function render(primaryColor, secondaryColor) { return { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z\", \"fill\": primaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M512 140c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm-32 156c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z\", \"fill\": secondaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M488 576h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8zm-24 112a48 48 0 1096 0 48 48 0 10-96 0z\", \"fill\": primaryColor } }] }; }, \"name\": \"exclamation-circle\", \"theme\": \"twotone\" };\nexport default ExclamationCircleTwoTone;\n"], "mappings": "AAAA;AACA,IAAIA,wBAAwB,GAAG;EAAE,MAAM,EAAE,SAASC,MAAMA,CAACC,YAAY,EAAEC,cAAc,EAAE;IAAE,OAAO;MAAE,KAAK,EAAE,KAAK;MAAE,OAAO,EAAE;QAAE,SAAS,EAAE,eAAe;QAAE,WAAW,EAAE;MAAQ,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,+KAA+K;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,uOAAuO;UAAE,MAAM,EAAEC;QAAe;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,mIAAmI;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,oBAAoB;EAAE,OAAO,EAAE;AAAU,CAAC;AACt8B,eAAeF,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}