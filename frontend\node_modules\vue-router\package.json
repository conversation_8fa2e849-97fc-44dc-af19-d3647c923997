{"name": "vue-router", "version": "4.2.5", "main": "index.js", "unpkg": "dist/vue-router.global.js", "jsdelivr": "dist/vue-router.global.js", "module": "dist/vue-router.mjs", "types": "dist/vue-router.d.ts", "exports": {".": {"types": "./dist/vue-router.d.ts", "node": {"import": {"production": "./dist/vue-router.node.mjs", "development": "./dist/vue-router.node.mjs", "default": "./dist/vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}, "./dist/*": "./dist/*", "./vetur/*": "./vetur/*", "./package.json": "./package.json"}, "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/posva", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/router.git"}, "bugs": {"url": "https://github.com/vuejs/router/issues"}, "homepage": "https://github.com/vuejs/router#readme", "files": ["index.js", "dist/*.{js,cjs,mjs}", "dist/vue-router.d.ts", "vetur/tags.json", "vetur/attributes.json", "README.md"], "peerDependencies": {"vue": "^3.2.0"}, "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}, "dependencies": {"@vue/devtools-api": "^6.5.0"}, "devDependencies": {"@microsoft/api-extractor": "^7.36.4", "@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.3", "@sucrase/jest-plugin": "^3.0.0", "@types/jest": "^29.5.3", "@types/jsdom": "^21.1.1", "@types/nightwatch": "^2.3.25", "@vitejs/plugin-vue": "^4.2.3", "@vue/compiler-sfc": "^3.3.4", "@vue/server-renderer": "^3.3.4", "@vue/test-utils": "^2.4.1", "browserstack-local": "^1.5.4", "chromedriver": "^115.0.1", "connect-history-api-fallback": "^1.6.0", "conventional-changelog-cli": "^2.1.1", "dotenv": "^16.3.1", "faked-promise": "^2.2.2", "geckodriver": "^3.2.0", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "jest-mock-warn": "^1.1.0", "nightwatch": "^2.6.21", "nightwatch-helpers": "^1.2.0", "rimraf": "^5.0.1", "rollup": "^3.28.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-typescript2": "^0.35.0", "sucrase": "^3.34.0", "typescript": "~5.1.6", "vite": "^4.4.9", "vue": "^3.3.4"}, "scripts": {"dev": "jest --watch", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "build": "rimraf dist && rollup -c rollup.config.mjs", "build:dts": "api-extractor run --local --verbose && tail -n +10 src/globalExtensions.ts >> dist/vue-router.d.ts", "build:playground": "vue-tsc --noEmit && vite build --config playground/vite.config.ts", "build:e2e": "vue-tsc --noEmit && vite build --config e2e/vite.config.mjs", "build:size": "pnpm run build && rollup -c size-checks/rollup.config.mjs", "dev:e2e": "vite --config e2e/vite.config.mjs", "test:types": "tsc --build tsconfig.json", "test:dts": "tsc -p ./test-dts/tsconfig.json", "test:unit": "jest --coverage", "test": "pnpm run test:types && pnpm run test:unit && pnpm run build && pnpm run build:dts && pnpm run test:e2e", "test:e2e": "pnpm run test:e2e:headless", "test:e2e:headless": "node e2e/runner.mjs --env chrome-headless", "test:e2e:native": "node e2e/runner.mjs --env chrome", "test:e2e:ci": "node e2e/runner.mjs --env chrome-headless --retries 2", "test:e2e:bs": "node e2e/runner.mjs --local -e android5 --tag browserstack", "test:e2e:bs-test": "node e2e/runner.mjs --local --env browserstack.local_chrome --tag browserstack"}}