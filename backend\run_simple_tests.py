#!/usr/bin/env python3
"""
简化的测试运行脚本
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_core_tests():
    """运行核心工具类测试"""
    print("🧪 运行核心工具类测试...")
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = str(Path(__file__).parent)
    
    # 运行特定的测试文件
    test_files = [
        'tests/test_dependency_manager.py',
        'tests/test_condition_parser.py', 
        'tests/test_parameter_manager.py',
        'tests/test_execution_manager.py'
    ]
    
    success_count = 0
    total_count = len(test_files)
    
    for test_file in test_files:
        print(f"\n📋 运行 {test_file}...")
        
        cmd = [
            sys.executable, '-m', 'pytest', 
            test_file,
            '-v',
            '--tb=short',
            '--confcutdir=tests',
            f'--confcutdir={Path(__file__).parent / "tests"}'
        ]
        
        try:
            result = subprocess.run(cmd, cwd=Path(__file__).parent, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ {test_file} 测试通过")
                success_count += 1
            else:
                print(f"❌ {test_file} 测试失败")
                if result.stdout:
                    print("STDOUT:", result.stdout[-500:])  # 显示最后500字符
                if result.stderr:
                    print("STDERR:", result.stderr[-500:])
                    
        except Exception as e:
            print(f"❌ 运行 {test_file} 时出错: {e}")
    
    return success_count, total_count

def run_unit_tests_directly():
    """直接运行单元测试（不使用pytest）"""
    print("🔧 直接运行单元测试...")
    
    # 添加项目路径
    sys.path.insert(0, str(Path(__file__).parent))
    
    try:
        # 测试依赖管理器
        print("\n📋 测试依赖管理器...")
        from app.utils.dependency_manager import DependencyManager
        
        dm = DependencyManager()
        dm.add_dependency('task2', 'task1')
        dm.add_dependency('task3', 'task2')
        
        assert not dm.has_cycle(), "不应该有循环依赖"
        order = dm.topological_sort()
        assert len(order) == 3, "应该有3个层级"
        print("✅ 依赖管理器测试通过")
        
        # 测试条件解析器
        print("\n📋 测试条件解析器...")
        from app.utils.condition_parser import ConditionParser
        
        parser = ConditionParser()
        assert parser.parse_condition('success', {'status': 'success'}) == True
        assert parser.parse_condition('status == "success"', {'status': 'success'}) == True
        print("✅ 条件解析器测试通过")
        
        # 测试参数管理器
        print("\n📋 测试参数管理器...")
        from app.utils.parameter_manager import ParameterManager, ParameterType
        
        manager = ParameterManager()
        assert manager.convert_parameter("42", ParameterType.INTEGER) == 42
        assert manager.convert_parameter("true", ParameterType.BOOLEAN) == True
        print("✅ 参数管理器测试通过")
        
        # 测试执行管理器
        print("\n📋 测试执行管理器...")
        from app.utils.execution_manager import ExecutionManager, TaskNode
        
        exec_manager = ExecutionManager(max_workers=2)
        assert exec_manager.max_workers == 2
        
        node = TaskNode(id="test", name="测试任务", task_id=1)
        assert node.id == "test"
        
        exec_manager.cleanup()
        print("✅ 执行管理器测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化的测试运行器")
    parser.add_argument("--method", choices=["pytest", "direct", "both"], default="both", help="测试方法")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    print("🚀 简化测试运行器")
    print("=" * 50)
    
    success = True
    
    if args.method in ["direct", "both"]:
        print("\n🔧 方法1: 直接运行测试")
        direct_success = run_unit_tests_directly()
        success = success and direct_success
    
    if args.method in ["pytest", "both"]:
        print("\n🧪 方法2: 使用pytest运行")
        try:
            success_count, total_count = run_core_tests()
            pytest_success = success_count == total_count
            success = success and pytest_success
            
            print(f"\n📊 pytest结果: {success_count}/{total_count} 测试文件通过")
        except Exception as e:
            print(f"❌ pytest运行失败: {e}")
            success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！")
        print("\n✅ 核心功能验证:")
        print("  - 依赖管理器: 循环检测、拓扑排序")
        print("  - 条件解析器: 表达式评估、安全验证")
        print("  - 参数管理器: 类型转换、参数验证")
        print("  - 执行管理器: 并发控制、状态管理")
    else:
        print("❌ 部分测试失败")
        print("\n💡 建议:")
        print("  1. 检查Python环境和依赖")
        print("  2. 确保在backend目录下运行")
        print("  3. 尝试运行 python test_simple.py")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
