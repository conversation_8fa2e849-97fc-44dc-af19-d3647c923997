#!/usr/bin/env node
/**
 * 前端测试运行脚本
 */
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function installDependencies() {
  log('📦 安装测试依赖...', 'blue');
  
  try {
    await runCommand('npm', ['install']);
    log('✅ 依赖安装成功', 'green');
    return true;
  } catch (error) {
    log('❌ 依赖安装失败', 'red');
    console.error(error.message);
    return false;
  }
}

async function runUnitTests(coverage = true, watch = false) {
  log('🧪 运行单元测试...', 'blue');
  
  const args = [];
  
  if (coverage) {
    args.push('--coverage');
  }
  
  if (watch) {
    args.push('--watch');
  } else {
    args.push('--watchAll=false');
  }
  
  try {
    await runCommand('npm', ['run', 'test', '--', ...args]);
    log('✅ 单元测试通过', 'green');
    return true;
  } catch (error) {
    log('❌ 单元测试失败', 'red');
    return false;
  }
}

async function runCoverageReport() {
  log('📊 生成覆盖率报告...', 'blue');
  
  try {
    await runCommand('npm', ['run', 'test:coverage']);
    
    const coverageDir = path.join(__dirname, 'coverage');
    const htmlReport = path.join(coverageDir, 'lcov-report', 'index.html');
    
    if (fs.existsSync(htmlReport)) {
      log(`📈 覆盖率报告: ${htmlReport}`, 'cyan');
    }
    
    return true;
  } catch (error) {
    log('❌ 覆盖率报告生成失败', 'red');
    return false;
  }
}

async function lintCode() {
  log('🔍 代码检查...', 'blue');
  
  try {
    // 检查是否有ESLint配置
    const eslintConfig = path.join(__dirname, '.eslintrc.js');
    if (!fs.existsSync(eslintConfig)) {
      log('⚠️  未找到ESLint配置，跳过代码检查', 'yellow');
      return true;
    }
    
    await runCommand('npx', ['eslint', 'src/**/*.{js,jsx}']);
    log('✅ 代码检查通过', 'green');
    return true;
  } catch (error) {
    log('❌ 代码检查失败', 'red');
    return false;
  }
}

async function buildProject() {
  log('🏗️  构建项目...', 'blue');
  
  try {
    await runCommand('npm', ['run', 'build']);
    log('✅ 项目构建成功', 'green');
    return true;
  } catch (error) {
    log('❌ 项目构建失败', 'red');
    return false;
  }
}

function showHelp() {
  console.log(`
使用方法: node run_tests.js [选项]

选项:
  --unit              只运行单元测试
  --coverage          生成覆盖率报告
  --watch             监视模式
  --lint              代码检查
  --build             构建项目
  --install           安装依赖
  --all               运行所有检查（默认）
  --help              显示帮助信息

示例:
  node run_tests.js --unit --coverage
  node run_tests.js --watch
  node run_tests.js --all
`);
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    showHelp();
    return;
  }
  
  const options = {
    unit: args.includes('--unit'),
    coverage: args.includes('--coverage'),
    watch: args.includes('--watch'),
    lint: args.includes('--lint'),
    build: args.includes('--build'),
    install: args.includes('--install'),
    all: args.includes('--all') || args.length === 0
  };
  
  let success = true;
  
  try {
    // 安装依赖
    if (options.install || options.all) {
      success = await installDependencies() && success;
    }
    
    // 代码检查
    if (options.lint || options.all) {
      success = await lintCode() && success;
    }
    
    // 运行单元测试
    if (options.unit || options.all) {
      success = await runUnitTests(options.coverage || options.all, options.watch) && success;
    }
    
    // 生成覆盖率报告
    if (options.coverage && !options.unit && !options.all) {
      success = await runCoverageReport() && success;
    }
    
    // 构建项目
    if (options.build || options.all) {
      success = await buildProject() && success;
    }
    
    if (success) {
      log('\n🎉 所有检查通过！', 'green');
    } else {
      log('\n💥 部分检查失败', 'red');
      process.exit(1);
    }
    
  } catch (error) {
    log(`\n💥 执行失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  log(`未处理的Promise拒绝: ${reason}`, 'red');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`未捕获的异常: ${error.message}`, 'red');
  process.exit(1);
});

if (require.main === module) {
  main();
}
