"""
无标记API测试
"""
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class TestAPINoMarkers:
    """无标记的API测试类"""
    
    def setup_method(self):
        """设置测试"""
        from tests.mock_app import create_mock_app
        self.app = create_mock_app()
        self.client = self.app.test_client()
        self.auth_headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'
        }
    
    def test_get_tasks(self):
        """测试获取任务列表"""
        response = self.client.get('/api/tasks')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_task(self):
        """测试创建任务"""
        task_data = {
            'name': '测试任务',
            'description': '这是一个测试任务',
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = self.client.post('/api/tasks',
                                  data=json.dumps(task_data),
                                  headers=self.auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert 'id' in data['data']
    
    def test_get_scripts(self):
        """测试获取脚本列表"""
        response = self.client.get('/api/scripts')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_script(self):
        """测试创建脚本"""
        script_data = {
            'name': '测试脚本',
            'type': 'python',
            'content': 'print("Hello, World!")',
            'version': '1.0.0'
        }
        
        response = self.client.post('/api/scripts',
                                  data=json.dumps(script_data),
                                  headers=self.auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
    
    def test_validate_script(self):
        """测试脚本验证"""
        script_data = {
            'type': 'python',
            'content': 'print("Valid code")'
        }
        
        response = self.client.post('/api/scripts/validate',
                                  data=json.dumps(script_data),
                                  headers=self.auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True
    
    def test_get_workflows(self):
        """测试获取工作流列表"""
        response = self.client.get('/api/workflows')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_workflow(self):
        """测试创建工作流"""
        workflow_data = {
            'name': '测试工作流',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'task1',
                        'name': '任务1',
                        'task_id': 1,
                        'execution_type': 'serial'
                    }
                ],
                'edges': []
            }
        }
        
        response = self.client.post('/api/workflows',
                                  data=json.dumps(workflow_data),
                                  headers=self.auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
    
    def test_validate_workflow(self):
        """测试工作流验证"""
        workflow_def = {
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1', 'task_id': 1}
                ],
                'edges': []
            }
        }
        
        response = self.client.post('/api/workflows/validate',
                                  data=json.dumps(workflow_def),
                                  headers=self.auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True
    
    def test_api_performance(self):
        """测试API性能"""
        import time
        
        start_time = time.time()
        response = self.client.get('/api/tasks')
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 1.0  # 响应时间小于1秒
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效JSON
        response = self.client.post('/api/tasks',
                                  data='invalid json',
                                  headers=self.auth_headers)
        assert response.status_code == 400
        
        # 测试缺少必需字段
        response = self.client.post('/api/tasks',
                                  data=json.dumps({}),
                                  headers=self.auth_headers)
        assert response.status_code == 400
        
        # 测试不存在的资源
        response = self.client.get('/api/tasks/99999')
        assert response.status_code == 404
