<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-30 00:04 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_condition_parser_py.html#t13">app\utils\condition_parser.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_condition_parser_py.html#t13"><data value='ConditionOperator'>ConditionOperator</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_condition_parser_py.html#t27">app\utils\condition_parser.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_condition_parser_py.html#t27"><data value='ConditionParser'>ConditionParser</data></a></td>
                <td>110</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="85 110">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_condition_parser_py.html">app\utils\condition_parser.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_condition_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_dependency_manager_py.html#t10">app\utils\dependency_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_dependency_manager_py.html#t10"><data value='DependencyManager'>DependencyManager</data></a></td>
                <td>133</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="123 133">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_dependency_manager_py.html">app\utils\dependency_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_dependency_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t17">app\utils\execution_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t17"><data value='ExecutionMode'>ExecutionMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t23">app\utils\execution_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t23"><data value='TaskStatus'>TaskStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t33">app\utils\execution_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t33"><data value='ExecutionResult'>ExecutionResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t47">app\utils\execution_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t47"><data value='TaskNode'>TaskNode</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t72">app\utils\execution_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html#t72"><data value='ExecutionManager'>ExecutionManager</data></a></td>
                <td>132</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="84 132">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html">app\utils\execution_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_execution_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="62 62">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_parameter_manager_py.html#t12">app\utils\parameter_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_parameter_manager_py.html#t12"><data value='ParameterType'>ParameterType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_parameter_manager_py.html#t22">app\utils\parameter_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_parameter_manager_py.html#t22"><data value='ParameterManager'>ParameterManager</data></a></td>
                <td>162</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="136 162">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_parameter_manager_py.html">app\utils\parameter_manager.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_parameter_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="28 28">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html">app\utils\security.py</a></td>
                <td class="name left"><a href="z_a7b07432402c05f1_security_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>63</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="30 63">48%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>744</td>
                <td>142</td>
                <td>0</td>
                <td class="right" data-ratio="602 744">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-07-30 00:04 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
