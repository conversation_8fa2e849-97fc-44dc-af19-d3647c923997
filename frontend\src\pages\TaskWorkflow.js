import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Table, 
  Space, 
  message, 
  Drawer,
  Divider,
  Tag,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  PlayCircleOutlined,
  EyeOutlined,
  NodeIndexOutlined
} from '@ant-design/icons';
import api from '../services/api';
import WorkflowDesigner from '../components/WorkflowDesigner';

const { TextArea } = Input;
const { Option } = Select;

const TaskWorkflow = () => {
  const [workflows, setWorkflows] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [designerVisible, setDesignerVisible] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState(null);
  const [designingWorkflow, setDesigningWorkflow] = useState(null);
  const [form] = Form.useForm();

  // 获取工作流列表
  const fetchWorkflows = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get('/task-workflows/workflows');
      if (response.data.code === 200) {
        setWorkflows(response.data.data || []);
      } else {
        message.error(response.data.message || '获取工作流列表失败');
      }
    } catch (error) {
      message.error('获取工作流列表失败');
      console.error('获取工作流列表失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取任务列表
  const fetchTasks = useCallback(async () => {
    try {
      const response = await api.get('/tasks/');
      setTasks(response.data || []);
    } catch (error) {
      console.error('获取任务列表失败:', error);
    }
  }, []);

  useEffect(() => {
    fetchWorkflows();
    fetchTasks();
  }, [fetchWorkflows, fetchTasks]);

  // 创建或更新工作流
  const handleSubmit = async (values) => {
    try {
      const workflowData = {
        ...values,
        workflow_definition: {
          nodes: [],
          edges: [],
          version: '1.0'
        }
      };

      if (editingWorkflow) {
        const response = await api.put(`/task-workflows/workflows/${editingWorkflow.id}`, workflowData);
        if (response.data.code === 200) {
          message.success('更新工作流成功');
          fetchWorkflows();
        } else {
          message.error(response.data.message || '更新工作流失败');
        }
      } else {
        const response = await api.post('/task-workflows/workflows', workflowData);
        if (response.data.code === 200) {
          message.success('创建工作流成功');
          fetchWorkflows();
        } else {
          message.error(response.data.message || '创建工作流失败');
        }
      }
      
      setModalVisible(false);
      form.resetFields();
      setEditingWorkflow(null);
    } catch (error) {
      message.error(editingWorkflow ? '更新工作流失败' : '创建工作流失败');
      console.error('操作工作流失败:', error);
    }
  };

  // 删除工作流
  const handleDelete = async (id) => {
    try {
      const response = await api.delete(`/task-workflows/workflows/${id}`);
      if (response.data.code === 200) {
        message.success('删除工作流成功');
        fetchWorkflows();
      } else {
        message.error(response.data.message || '删除工作流失败');
      }
    } catch (error) {
      message.error('删除工作流失败');
      console.error('删除工作流失败:', error);
    }
  };

  // 执行工作流
  const handleExecute = async (id) => {
    try {
      const response = await api.post(`/task-workflows/workflows/${id}/execute`);
      if (response.data.code === 200) {
        message.success('工作流执行成功');
        // 可以添加执行状态监控
      } else {
        message.error(response.data.message || '工作流执行失败');
      }
    } catch (error) {
      message.error('工作流执行失败');
      console.error('工作流执行失败:', error);
    }
  };

  // 打开编辑模态框
  const handleEdit = (record) => {
    setEditingWorkflow(record);
    form.setFieldsValue({
      name: record.name,
      description: record.description
    });
    setModalVisible(true);
  };

  // 查看工作流详情
  const handleView = (record) => {
    setSelectedWorkflow(record);
    setDrawerVisible(true);
  };

  // 打开工作流设计器
  const handleDesign = (record) => {
    setDesigningWorkflow(record);
    setDesignerVisible(true);
  };

  // 保存工作流设计
  const handleSaveDesign = async (workflowDefinition) => {
    try {
      const response = await api.put(`/task-workflows/workflows/${designingWorkflow.id}`, {
        workflow_definition: workflowDefinition
      });
      if (response.data.code === 200) {
        message.success('保存工作流设计成功');
        setDesignerVisible(false);
        setDesigningWorkflow(null);
        fetchWorkflows();
      } else {
        message.error(response.data.message || '保存工作流设计失败');
      }
    } catch (error) {
      message.error('保存工作流设计失败');
      console.error('保存工作流设计失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '工作流名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            icon={<NodeIndexOutlined />} 
            onClick={() => handleDesign(record)}
          >
            设计
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            icon={<PlayCircleOutlined />} 
            onClick={() => handleExecute(record.id)}
          >
            执行
          </Button>
          <Popconfirm
            title="确定要删除这个工作流吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card title="任务编排管理" style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={() => {
              setEditingWorkflow(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            创建工作流
          </Button>
        </div>
        
        <Table 
          columns={columns} 
          dataSource={workflows} 
          loading={loading} 
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑工作流模态框 */}
      <Modal
        title={editingWorkflow ? '编辑工作流' : '创建工作流'}
        open={modalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingWorkflow(null);
        }}
        destroyOnClose
      >
        <Form 
          form={form} 
          layout="vertical" 
          onFinish={handleSubmit}
        >
          <Form.Item 
            name="name" 
            label="工作流名称" 
            rules={[{ required: true, message: '请输入工作流名称' }]}
          >
            <Input placeholder="请输入工作流名称" />
          </Form.Item>
          <Form.Item 
            name="description" 
            label="描述"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入工作流描述" 
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 工作流详情抽屉 */}
      <Drawer
        title="工作流详情"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        width={600}
      >
        {selectedWorkflow && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <h3>基本信息</h3>
              <p><strong>名称：</strong>{selectedWorkflow.name}</p>
              <p><strong>描述：</strong>{selectedWorkflow.description || '无'}</p>
              <p><strong>创建时间：</strong>{selectedWorkflow.created_at ? new Date(selectedWorkflow.created_at).toLocaleString() : '-'}</p>
              <p><strong>更新时间：</strong>{selectedWorkflow.updated_at ? new Date(selectedWorkflow.updated_at).toLocaleString() : '-'}</p>
            </div>
            
            <Divider />
            
            <div>
              <h3>工作流定义</h3>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                overflow: 'auto'
              }}>
                {JSON.stringify(selectedWorkflow.workflow_definition || {}, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </Drawer>

      {/* 工作流设计器模态框 */}
      <Modal
        title={`设计工作流: ${designingWorkflow?.name || ''}`}
        open={designerVisible}
        onCancel={() => {
          setDesignerVisible(false);
          setDesigningWorkflow(null);
        }}
        footer={null}
        width="90%"
        style={{ top: 20 }}
        destroyOnClose
      >
        {designingWorkflow && (
          <WorkflowDesigner
            workflow={designingWorkflow}
            tasks={tasks}
            onSave={handleSaveDesign}
            onClose={() => {
              setDesignerVisible(false);
              setDesigningWorkflow(null);
            }}
          />
        )}
      </Modal>
    </div>
  );
};

export default TaskWorkflow;
