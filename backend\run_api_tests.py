#!/usr/bin/env python3
"""
API测试运行器
"""
import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def run_api_tests(coverage=False, verbose=False, specific_test=None, performance=False):
    """运行API测试"""
    print("🚀 运行API接口测试")
    print("=" * 60)
    
    # 构建pytest命令
    cmd = [sys.executable, "-m", "pytest"]
    
    # 添加覆盖率选项
    if coverage:
        cmd.extend([
            "--cov=app.api",
            "--cov=app.models", 
            "--cov=app.schemas",
            "--cov-report=html:htmlcov_api",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
    
    # 添加详细输出
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # 添加测试文件
    if specific_test:
        cmd.append(specific_test)
    else:
        # 运行所有API测试
        api_test_files = [
            "tests/test_api_comprehensive.py",
            "tests/test_api_workflows.py",
            "tests/test_api_performance.py"
        ]
        
        # 检查文件是否存在
        existing_files = []
        for test_file in api_test_files:
            if Path(test_file).exists():
                existing_files.append(test_file)
            else:
                print(f"⚠️  测试文件不存在: {test_file}")
        
        if existing_files:
            cmd.extend(existing_files)
        else:
            print("❌ 没有找到API测试文件")
            return False
    
    # 添加标记过滤
    if performance:
        cmd.extend(["-m", "performance"])
    else:
        cmd.extend(["-m", "not performance"])  # 默认跳过性能测试
    
    # 添加其他选项
    cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--disable-warnings"
    ])
    
    print("🔧 运行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['FLASK_ENV'] = 'testing'
        env['TESTING'] = 'true'
        
        result = subprocess.run(cmd, cwd=Path(__file__).parent, env=env, timeout=300)
        
        if result.returncode == 0:
            print("\n✅ API测试成功完成！")
            if coverage:
                print("📊 覆盖率报告已生成: htmlcov_api/index.html")
            return True
        else:
            print("\n❌ API测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ API测试超时")
        return False
    except Exception as e:
        print(f"💥 运行API测试时出错: {e}")
        return False


def run_specific_api_tests():
    """运行特定的API测试"""
    print("🎯 运行特定API测试")
    print("-" * 40)
    
    test_categories = {
        "basic": "tests/test_api_comprehensive.py::TestTaskAPI",
        "workflow": "tests/test_api_workflows.py::TestWorkflowCRUD",
        "performance": "tests/test_api_performance.py::TestAPIPerformance",
        "error": "tests/test_api_performance.py::TestAPIErrorHandling"
    }
    
    for category, test_path in test_categories.items():
        print(f"\n📋 运行 {category} 测试...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            test_path,
            "-v", "--tb=short"
        ]
        
        try:
            result = subprocess.run(cmd, cwd=Path(__file__).parent, timeout=120)
            if result.returncode == 0:
                print(f"  ✅ {category} 测试通过")
            else:
                print(f"  ❌ {category} 测试失败")
        except Exception as e:
            print(f"  💥 {category} 测试出错: {e}")


def generate_api_test_report():
    """生成API测试报告"""
    print("📊 生成API测试报告")
    print("-" * 40)
    
    # 运行测试并生成详细报告
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_api_comprehensive.py",
        "tests/test_api_workflows.py",
        "--cov=app.api",
        "--cov=app.models",
        "--cov-report=html:htmlcov_api",
        "--cov-report=term-missing",
        "--html=reports/api_test_report.html",
        "--self-contained-html",
        "-v"
    ]
    
    try:
        # 确保报告目录存在
        reports_dir = Path(__file__).parent / "reports"
        reports_dir.mkdir(exist_ok=True)
        
        result = subprocess.run(cmd, cwd=Path(__file__).parent, timeout=300)
        
        if result.returncode == 0:
            print("✅ API测试报告生成成功")
            print("📁 HTML报告: reports/api_test_report.html")
            print("📁 覆盖率报告: htmlcov_api/index.html")
            return True
        else:
            print("❌ API测试报告生成失败")
            return False
            
    except Exception as e:
        print(f"💥 生成报告时出错: {e}")
        return False


def validate_api_coverage():
    """验证API覆盖率"""
    print("🎯 验证API覆盖率")
    print("-" * 40)
    
    # 运行覆盖率测试
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_api_comprehensive.py",
        "tests/test_api_workflows.py",
        "--cov=app.api",
        "--cov-report=term-missing",
        "--cov-fail-under=80",
        "-q"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent, 
                              capture_output=True, text=True, timeout=180)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ API覆盖率达到80%以上")
            return True
        else:
            print("❌ API覆盖率未达到80%")
            return False
            
    except Exception as e:
        print(f"💥 验证覆盖率时出错: {e}")
        return False


def create_api_test_summary():
    """创建API测试总结"""
    print("📋 API测试总结")
    print("=" * 60)
    
    summary = {
        "测试文件": [
            "test_api_comprehensive.py - 全面API接口测试",
            "test_api_workflows.py - 工作流API专项测试", 
            "test_api_performance.py - 性能和错误处理测试"
        ],
        "测试覆盖": [
            "✅ 任务API (CRUD操作)",
            "✅ 脚本API (创建、验证)",
            "✅ 工作流API (完整生命周期)",
            "✅ 条件验证API",
            "✅ 参数验证API",
            "✅ 性能测试",
            "✅ 错误处理测试"
        ],
        "覆盖率目标": [
            "API模块覆盖率: 80%+",
            "模型模块覆盖率: 70%+",
            "总体API覆盖率: 75%+"
        ],
        "测试特性": [
            "Flask测试客户端",
            "Mock数据和服务",
            "并发测试",
            "性能监控",
            "错误注入测试"
        ]
    }
    
    for category, items in summary.items():
        print(f"\n📌 {category}:")
        for item in items:
            print(f"  {item}")
    
    print(f"\n🎯 运行方法:")
    print("# 运行所有API测试")
    print("python run_api_tests.py --coverage")
    print("")
    print("# 运行特定测试")
    print("python run_api_tests.py --test tests/test_api_workflows.py")
    print("")
    print("# 运行性能测试")
    print("python run_api_tests.py --performance")
    print("")
    print("# 生成详细报告")
    print("python run_api_tests.py --report")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API测试运行器")
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--test", help="运行特定测试文件")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--report", action="store_true", help="生成详细报告")
    parser.add_argument("--validate", action="store_true", help="验证覆盖率")
    parser.add_argument("--summary", action="store_true", help="显示测试总结")
    
    args = parser.parse_args()
    
    if args.summary:
        create_api_test_summary()
        return
    
    if args.report:
        success = generate_api_test_report()
        sys.exit(0 if success else 1)
    
    if args.validate:
        success = validate_api_coverage()
        sys.exit(0 if success else 1)
    
    # 运行API测试
    success = run_api_tests(
        coverage=args.coverage,
        verbose=args.verbose,
        specific_test=args.test,
        performance=args.performance
    )
    
    if success:
        print("\n🎉 API测试完成！")
        print("\n💡 后续步骤:")
        print("1. 查看覆盖率报告: htmlcov_api/index.html")
        print("2. 运行性能测试: python run_api_tests.py --performance")
        print("3. 生成详细报告: python run_api_tests.py --report")
    else:
        print("\n⚠️  API测试未完全通过")
        print("\n💡 建议:")
        print("1. 检查测试环境配置")
        print("2. 确认Flask应用正常启动")
        print("3. 查看详细错误信息")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
