"""
全面的API接口测试
"""
import pytest
import json
import time
from unittest.mock import patch, MagicMock
from flask import Flask
from tests.mock_app import create_mock_app


@pytest.fixture(scope='session')
def test_app():
    """创建测试应用"""
    app = create_mock_app()
    app.config['TESTING'] = True

    with app.app_context():
        yield app


@pytest.fixture
def client(test_app):
    """创建测试客户端"""
    return test_app.test_client()


@pytest.fixture
def auth_headers():
    """认证头部"""
    return {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
    }


class TestTaskAPI:
    """任务API测试"""
    
    def test_get_tasks_empty(self, client):
        """测试获取空任务列表"""
        response = client.get('/api/tasks')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_task_success(self, client, auth_headers):
        """测试成功创建任务"""
        task_data = {
            'name': '测试任务',
            'description': '这是一个测试任务',
            'cron_expression': '0 0 * * *',
            'script_id': 1,
            'is_active': True
        }
        
        response = client.post('/api/tasks', 
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert data['data']['name'] == task_data['name']
        assert 'id' in data['data']
    
    def test_create_task_missing_name(self, client, auth_headers):
        """测试创建任务缺少名称"""
        task_data = {
            'description': '缺少名称的任务',
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert 'name' in data['message'].lower()
    
    def test_create_task_invalid_cron(self, client, auth_headers):
        """测试创建任务无效cron表达式"""
        task_data = {
            'name': '无效cron任务',
            'cron_expression': 'invalid-cron',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
    
    def test_get_task_by_id(self, client):
        """测试根据ID获取任务"""
        # 先创建一个任务
        task_data = {
            'name': '获取测试任务',
            'description': '用于测试获取的任务',
            'cron_expression': '0 0 * * *'
        }
        
        create_response = client.post('/api/tasks',
                                    data=json.dumps(task_data),
                                    headers={'Content-Type': 'application/json'})
        
        if create_response.status_code == 201:
            task_id = json.loads(create_response.data)['data']['id']
            
            response = client.get(f'/api/tasks/{task_id}')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['data']['name'] == task_data['name']
    
    def test_get_task_not_found(self, client):
        """测试获取不存在的任务"""
        response = client.get('/api/tasks/99999')
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['code'] == 404
    
    def test_update_task(self, client, auth_headers):
        """测试更新任务"""
        # 先创建一个任务
        task_data = {
            'name': '更新前任务',
            'description': '更新前描述',
            'cron_expression': '0 0 * * *'
        }
        
        create_response = client.post('/api/tasks',
                                    data=json.dumps(task_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            task_id = json.loads(create_response.data)['data']['id']
            
            update_data = {
                'name': '更新后任务',
                'description': '更新后描述',
                'is_active': False
            }
            
            response = client.put(f'/api/tasks/{task_id}',
                                data=json.dumps(update_data),
                                headers=auth_headers)
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['data']['name'] == update_data['name']
    
    def test_delete_task(self, client, auth_headers):
        """测试删除任务"""
        # 先创建一个任务
        task_data = {
            'name': '待删除任务',
            'cron_expression': '0 0 * * *'
        }
        
        create_response = client.post('/api/tasks',
                                    data=json.dumps(task_data),
                                    headers=auth_headers)
        
        if create_response.status_code == 201:
            task_id = json.loads(create_response.data)['data']['id']
            
            response = client.delete(f'/api/tasks/{task_id}',
                                   headers=auth_headers)
            
            assert response.status_code == 200
            
            # 验证任务已被删除
            get_response = client.get(f'/api/tasks/{task_id}')
            assert get_response.status_code == 404
    
    def test_execute_task(self, client, auth_headers):
        """测试执行任务"""
        with patch('app.scheduler.execute_task') as mock_execute:
            mock_execute.return_value = {'status': 'started', 'execution_id': 'test-123'}
            
            response = client.post('/api/tasks/1/execute',
                                 headers=auth_headers)
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['code'] == 200
            mock_execute.assert_called_once()


class TestScriptAPI:
    """脚本API测试"""
    
    def test_get_scripts(self, client):
        """测试获取脚本列表"""
        response = client.get('/api/scripts')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_script_success(self, client, auth_headers):
        """测试成功创建脚本"""
        script_data = {
            'name': '测试脚本',
            'description': '这是一个测试脚本',
            'type': 'python',
            'content': 'print("Hello, World!")',
            'version': '1.0.0'
        }
        
        response = client.post('/api/scripts',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert data['data']['name'] == script_data['name']
    
    def test_create_script_missing_content(self, client, auth_headers):
        """测试创建脚本缺少内容"""
        script_data = {
            'name': '无内容脚本',
            'type': 'python'
        }
        
        response = client.post('/api/scripts',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
    
    def test_validate_script_syntax(self, client, auth_headers):
        """测试脚本语法验证"""
        script_data = {
            'type': 'python',
            'content': 'print("Valid Python code")'
        }
        
        response = client.post('/api/scripts/validate',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True
    
    def test_validate_script_invalid_syntax(self, client, auth_headers):
        """测试无效脚本语法验证"""
        script_data = {
            'type': 'python',
            'content': 'print("Invalid syntax'  # 缺少引号
        }
        
        response = client.post('/api/scripts/validate',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is False
        assert 'error' in data['data']


class TestWorkflowAPI:
    """工作流API测试"""
    
    def test_get_workflows(self, client):
        """测试获取工作流列表"""
        response = client.get('/api/workflows')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_workflow_success(self, client, auth_headers):
        """测试成功创建工作流"""
        workflow_data = {
            'name': '测试工作流',
            'description': '这是一个测试工作流',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'task1',
                        'name': '任务1',
                        'task_id': 1,
                        'execution_type': 'serial'
                    }
                ],
                'edges': []
            }
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert data['data']['name'] == workflow_data['name']
    
    def test_validate_workflow(self, client, auth_headers):
        """测试工作流验证"""
        workflow_def = {
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1', 'task_id': 1},
                    {'id': 'task2', 'name': '任务2', 'task_id': 2}
                ],
                'edges': [
                    {'source': 'task1', 'target': 'task2', 'condition': 'success'}
                ]
            }
        }
        
        response = client.post('/api/workflows/validate',
                             data=json.dumps(workflow_def),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'valid' in data['data']
    
    def test_execute_workflow(self, client, auth_headers):
        """测试执行工作流"""
        with patch('app.scheduler.execute_workflow') as mock_execute:
            mock_execute.return_value = {'status': 'started', 'execution_id': 'workflow-123'}
            
            response = client.post('/api/workflows/1/execute',
                                 headers=auth_headers)
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['code'] == 200
            mock_execute.assert_called_once()


class TestAPIPerformance:
    """API性能测试"""
    
    def test_api_response_time(self, client):
        """测试API响应时间"""
        start_time = time.time()
        response = client.get('/api/tasks')
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 1.0  # 响应时间应小于1秒
        assert response.status_code == 200
    
    def test_concurrent_requests(self, client):
        """测试并发请求"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                response = client.get('/api/tasks')
                results.put(response.status_code)
            except Exception as e:
                results.put(str(e))
        
        # 创建10个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        success_count = 0
        while not results.empty():
            result = results.get()
            if result == 200:
                success_count += 1
        
        assert success_count >= 8  # 至少80%的请求成功


class TestAPIErrorHandling:
    """API错误处理测试"""
    
    def test_invalid_json_request(self, client, auth_headers):
        """测试无效JSON请求"""
        response = client.post('/api/tasks',
                             data='invalid json',
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
    
    def test_missing_content_type(self, client):
        """测试缺少Content-Type头部"""
        response = client.post('/api/tasks',
                             data=json.dumps({'name': 'test'}))
        
        assert response.status_code == 400
    
    def test_unauthorized_request(self, client):
        """测试未授权请求"""
        response = client.post('/api/tasks',
                             data=json.dumps({'name': 'test'}),
                             headers={'Content-Type': 'application/json'})
        
        # 根据实际的认证机制调整期望的状态码
        assert response.status_code in [401, 403, 400]
    
    def test_method_not_allowed(self, client):
        """测试不允许的HTTP方法"""
        response = client.patch('/api/tasks')
        assert response.status_code == 405
    
    def test_large_request_body(self, client, auth_headers):
        """测试大请求体"""
        large_data = {
            'name': 'test',
            'description': 'x' * 10000  # 10KB描述
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(large_data),
                             headers=auth_headers)
        
        # 应该能处理合理大小的请求
        assert response.status_code in [201, 400, 413]


class TestAPIIntegration:
    """API集成测试"""
    
    def test_task_workflow_integration(self, client, auth_headers):
        """测试任务和工作流集成"""
        # 1. 创建脚本
        script_data = {
            'name': '集成测试脚本',
            'type': 'python',
            'content': 'print("Integration test")'
        }
        
        script_response = client.post('/api/scripts',
                                    data=json.dumps(script_data),
                                    headers=auth_headers)
        
        if script_response.status_code == 201:
            script_id = json.loads(script_response.data)['data']['id']
            
            # 2. 创建任务
            task_data = {
                'name': '集成测试任务',
                'script_id': script_id,
                'cron_expression': '0 0 * * *'
            }
            
            task_response = client.post('/api/tasks',
                                      data=json.dumps(task_data),
                                      headers=auth_headers)
            
            if task_response.status_code == 201:
                task_id = json.loads(task_response.data)['data']['id']
                
                # 3. 创建工作流
                workflow_data = {
                    'name': '集成测试工作流',
                    'workflow_definition': {
                        'nodes': [
                            {
                                'id': 'integration_task',
                                'name': '集成任务',
                                'task_id': task_id
                            }
                        ],
                        'edges': []
                    }
                }
                
                workflow_response = client.post('/api/workflows',
                                              data=json.dumps(workflow_data),
                                              headers=auth_headers)
                
                assert workflow_response.status_code == 201
                workflow_data = json.loads(workflow_response.data)
                assert workflow_data['code'] == 201
