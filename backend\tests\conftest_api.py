"""
API测试配置和fixtures
"""
import pytest
import os
import tempfile
import json
from unittest.mock import patch, MagicMock
from flask import Flask
from app import create_app
from app.database import db
from app.models import Task, Script, TaskWorkflow


@pytest.fixture(scope='session')
def test_app():
    """创建测试应用实例"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp()
    
    config = {
        'TESTING': True,
        'DATABASE_URL': f'sqlite:///{db_path}',
        'SECRET_KEY': 'test-secret-key-for-api-testing',
        'WTF_CSRF_ENABLED': False,
        'SQLALCHEMY_TRACK_MODIFICATIONS': False,
        'SQLALCHEMY_ECHO': False,
        'JWT_SECRET_KEY': 'test-jwt-secret',
        'JWT_ACCESS_TOKEN_EXPIRES': False,
    }
    
    app = create_app(config)
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 插入测试数据
        _insert_test_data()
        
        yield app
        
        # 清理
        db.drop_all()
    
    os.close(db_fd)
    os.unlink(db_path)


def _insert_test_data():
    """插入测试数据"""
    try:
        # 创建测试脚本
        test_scripts = [
            Script(
                name='测试脚本1',
                type='python',
                content='print("Hello from script 1")',
                version='1.0.0'
            ),
            Script(
                name='测试脚本2',
                type='bash',
                content='echo "Hello from script 2"',
                version='1.0.0'
            ),
            Script(
                name='测试脚本3',
                type='python',
                content='import time; time.sleep(1); print("Completed")',
                version='1.0.0'
            )
        ]
        
        for script in test_scripts:
            db.session.add(script)
        
        db.session.commit()
        
        # 创建测试任务
        test_tasks = [
            Task(
                name='测试任务1',
                description='第一个测试任务',
                cron_expression='0 0 * * *',
                script_id=1,
                is_active=True
            ),
            Task(
                name='测试任务2',
                description='第二个测试任务',
                cron_expression='0 12 * * *',
                script_id=2,
                is_active=True
            ),
            Task(
                name='测试任务3',
                description='第三个测试任务',
                cron_expression='*/5 * * * *',
                script_id=3,
                is_active=False
            )
        ]
        
        for task in test_tasks:
            db.session.add(task)
        
        db.session.commit()
        
        # 创建测试工作流
        sample_workflow_def = {
            'nodes': [
                {
                    'id': 'task1',
                    'name': '任务1',
                    'task_id': 1,
                    'execution_type': 'serial'
                },
                {
                    'id': 'task2',
                    'name': '任务2',
                    'task_id': 2,
                    'execution_type': 'parallel'
                }
            ],
            'edges': [
                {
                    'source': 'task1',
                    'target': 'task2',
                    'condition': 'success'
                }
            ]
        }
        
        test_workflow = TaskWorkflow(
            name='测试工作流',
            description='用于API测试的工作流',
            workflow_definition=json.dumps(sample_workflow_def),
            is_active=True
        )
        
        db.session.add(test_workflow)
        db.session.commit()
        
    except Exception as e:
        print(f"插入测试数据失败: {e}")
        db.session.rollback()


@pytest.fixture
def client(test_app):
    """创建测试客户端"""
    return test_app.test_client()


@pytest.fixture
def auth_headers():
    """认证头部"""
    return {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token',
        'Accept': 'application/json'
    }


@pytest.fixture
def json_headers():
    """JSON头部"""
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }


@pytest.fixture
def mock_scheduler():
    """模拟调度器"""
    with patch('app.scheduler') as mock:
        mock.execute_task.return_value = {
            'status': 'started',
            'execution_id': 'task-exec-123',
            'estimated_duration': 60
        }
        
        mock.execute_workflow.return_value = {
            'status': 'started',
            'execution_id': 'workflow-exec-123',
            'estimated_duration': 300
        }
        
        mock.get_task_execution_status.return_value = {
            'execution_id': 'task-exec-123',
            'status': 'running',
            'progress': 0.5,
            'start_time': '2024-01-01T10:00:00Z'
        }
        
        mock.get_workflow_execution_status.return_value = {
            'execution_id': 'workflow-exec-123',
            'status': 'running',
            'progress': 0.3,
            'current_task': 'task1',
            'start_time': '2024-01-01T10:00:00Z'
        }
        
        mock.cancel_task_execution.return_value = {
            'status': 'cancelled',
            'message': 'Task execution cancelled'
        }
        
        mock.cancel_workflow_execution.return_value = {
            'status': 'cancelled',
            'message': 'Workflow execution cancelled'
        }
        
        yield mock


@pytest.fixture
def mock_dependency_manager():
    """模拟依赖管理器"""
    with patch('app.utils.dependency_manager.DependencyManager') as mock:
        instance = mock.return_value
        instance.validate_workflow.return_value = (True, "工作流验证通过")
        instance.has_cycle.return_value = False
        instance.topological_sort.return_value = [['task1'], ['task2'], ['task3']]
        instance.get_execution_plan.return_value = {
            'execution_order': [['task1'], ['task2'], ['task3']],
            'estimated_duration': 300,
            'parallel_groups': [['task2', 'task3']]
        }
        yield instance


@pytest.fixture
def mock_condition_parser():
    """模拟条件解析器"""
    with patch('app.utils.condition_parser.ConditionParser') as mock:
        instance = mock.return_value
        instance.validate_condition.return_value = (True, "条件验证通过")
        instance.parse_condition.return_value = True
        instance.get_supported_conditions.return_value = {
            'predefined_conditions': ['success', 'failed', 'always'],
            'operators': ['==', '!=', '>', '<', '>=', '<=', 'and', 'or'],
            'functions': ['len', 'str', 'int', 'float'],
            'variables': ['status', 'duration', 'output', 'error_message'],
            'examples': [
                'success',
                'status == "success"',
                'duration < 300',
                'status == "success" and duration < 300'
            ]
        }
        yield instance


@pytest.fixture
def mock_parameter_manager():
    """模拟参数管理器"""
    with patch('app.utils.parameter_manager.ParameterManager') as mock:
        instance = mock.return_value
        instance.validate_parameter.return_value = (True, "参数验证通过")
        instance.validate_parameters.return_value = (True, [])
        instance.get_parameter_schema.return_value = {
            'types': ['string', 'integer', 'float', 'boolean', 'json', 'list'],
            'constraints': ['required', 'min', 'max', 'min_length', 'max_length', 'pattern'],
            'examples': {
                'string': 'hello world',
                'integer': 42,
                'float': 3.14,
                'boolean': True,
                'json': {'key': 'value'},
                'list': ['item1', 'item2']
            }
        }
        yield instance


@pytest.fixture
def sample_task_data():
    """示例任务数据"""
    return {
        'name': '示例任务',
        'description': '这是一个示例任务',
        'cron_expression': '0 0 * * *',
        'script_id': 1,
        'is_active': True,
        'parameters': {
            'input_file': '/tmp/input.txt',
            'output_file': '/tmp/output.txt'
        }
    }


@pytest.fixture
def sample_script_data():
    """示例脚本数据"""
    return {
        'name': '示例脚本',
        'description': '这是一个示例脚本',
        'type': 'python',
        'content': 'print("Hello, World!")',
        'version': '1.0.0',
        'parameters_schema': {
            'input_file': {
                'type': 'string',
                'required': True,
                'description': '输入文件路径'
            },
            'output_file': {
                'type': 'string',
                'required': True,
                'description': '输出文件路径'
            }
        }
    }


@pytest.fixture
def sample_workflow_data():
    """示例工作流数据"""
    return {
        'name': '示例工作流',
        'description': '这是一个示例工作流',
        'workflow_definition': {
            'nodes': [
                {
                    'id': 'start_task',
                    'name': '开始任务',
                    'task_id': 1,
                    'execution_type': 'serial',
                    'parameters': {
                        'input_file': '${workflow.input_file}'
                    }
                },
                {
                    'id': 'end_task',
                    'name': '结束任务',
                    'task_id': 2,
                    'execution_type': 'serial',
                    'condition': 'start_task.status == "success"'
                }
            ],
            'edges': [
                {
                    'source': 'start_task',
                    'target': 'end_task',
                    'condition': 'success'
                }
            ]
        },
        'is_active': True
    }


@pytest.fixture
def api_response_validator():
    """API响应验证器"""
    def validate_response(response, expected_status=200, expected_code=None):
        """验证API响应格式"""
        assert response.status_code == expected_status
        
        if response.content_type == 'application/json':
            data = json.loads(response.data)
            
            # 验证基本结构
            assert 'code' in data
            assert 'message' in data
            assert 'data' in data
            
            # 验证状态码
            if expected_code:
                assert data['code'] == expected_code
            else:
                assert data['code'] == expected_status
            
            return data
        
        return None
    
    return validate_response


@pytest.fixture
def performance_monitor():
    """性能监控器"""
    import time
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
        
        def get_duration(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
        
        def assert_duration_under(self, max_duration):
            duration = self.get_duration()
            assert duration is not None, "性能监控未正确启动/停止"
            assert duration < max_duration, f"响应时间 {duration:.3f}s 超过限制 {max_duration}s"
    
    return PerformanceMonitor()


# pytest标记定义
pytest_plugins = []

def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "api: API接口测试"
    )
    config.addinivalue_line(
        "markers", "workflow: 工作流相关测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
