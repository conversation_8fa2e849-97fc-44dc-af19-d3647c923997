{"ast": null, "code": "import React from 'react';\nexport const ModalContext = /*#__PURE__*/React.createContext({});\nexport const {\n  Provider: ModalContextProvider\n} = ModalContext;", "map": {"version": 3, "names": ["React", "ModalContext", "createContext", "Provider", "ModalContextProvider"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/modal/context.js"], "sourcesContent": ["import React from 'react';\nexport const ModalContext = /*#__PURE__*/React.createContext({});\nexport const {\n  Provider: ModalContextProvider\n} = ModalContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAChE,OAAO,MAAM;EACXC,QAAQ,EAAEC;AACZ,CAAC,GAAGH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}