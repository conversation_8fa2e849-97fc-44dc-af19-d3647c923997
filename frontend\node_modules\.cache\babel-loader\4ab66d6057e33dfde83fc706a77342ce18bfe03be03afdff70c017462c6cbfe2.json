{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport FieldContext from \"./FieldContext\";\nimport Field from \"./Field\";\nimport { move as _move, getNamePath } from \"./utils/valueUtil\";\nimport ListContext from \"./ListContext\";\nfunction List(_ref) {\n  var name = _ref.name,\n    initialValue = _ref.initialValue,\n    children = _ref.children,\n    rules = _ref.rules,\n    validateTrigger = _ref.validateTrigger,\n    isListField = _ref.isListField;\n  var context = React.useContext(FieldContext);\n  var wrapperListContext = React.useContext(ListContext);\n  var keyRef = React.useRef({\n    keys: [],\n    id: 0\n  });\n  var keyManager = keyRef.current;\n  var prefixName = React.useMemo(function () {\n    var parentPrefixName = getNamePath(context.prefixName) || [];\n    return [].concat(_toConsumableArray(parentPrefixName), _toConsumableArray(getNamePath(name)));\n  }, [context.prefixName, name]);\n  var fieldContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, context), {}, {\n      prefixName: prefixName\n    });\n  }, [context, prefixName]);\n\n  // List context\n  var listContext = React.useMemo(function () {\n    return {\n      getKey: function getKey(namePath) {\n        var len = prefixName.length;\n        var pathName = namePath[len];\n        return [keyManager.keys[pathName], namePath.slice(len + 1)];\n      }\n    };\n  }, [prefixName]);\n\n  // User should not pass `children` as other type.\n  if (typeof children !== 'function') {\n    warning(false, 'Form.List only accepts function as children.');\n    return null;\n  }\n  var shouldUpdate = function shouldUpdate(prevValue, nextValue, _ref2) {\n    var source = _ref2.source;\n    if (source === 'internal') {\n      return false;\n    }\n    return prevValue !== nextValue;\n  };\n  return /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: listContext\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: fieldContext\n  }, /*#__PURE__*/React.createElement(Field, {\n    name: [],\n    shouldUpdate: shouldUpdate,\n    rules: rules,\n    validateTrigger: validateTrigger,\n    initialValue: initialValue,\n    isList: true,\n    isListField: isListField !== null && isListField !== void 0 ? isListField : !!wrapperListContext\n  }, function (_ref3, meta) {\n    var _ref3$value = _ref3.value,\n      value = _ref3$value === void 0 ? [] : _ref3$value,\n      onChange = _ref3.onChange;\n    var getFieldValue = context.getFieldValue;\n    var getNewValue = function getNewValue() {\n      var values = getFieldValue(prefixName || []);\n      return values || [];\n    };\n    /**\n     * Always get latest value in case user update fields by `form` api.\n     */\n    var operations = {\n      add: function add(defaultValue, index) {\n        // Mapping keys\n        var newValue = getNewValue();\n        if (index >= 0 && index <= newValue.length) {\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys.slice(0, index)), [keyManager.id], _toConsumableArray(keyManager.keys.slice(index)));\n          onChange([].concat(_toConsumableArray(newValue.slice(0, index)), [defaultValue], _toConsumableArray(newValue.slice(index))));\n        } else {\n          if (process.env.NODE_ENV !== 'production' && (index < 0 || index > newValue.length)) {\n            warning(false, 'The second parameter of the add function should be a valid positive number.');\n          }\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys), [keyManager.id]);\n          onChange([].concat(_toConsumableArray(newValue), [defaultValue]));\n        }\n        keyManager.id += 1;\n      },\n      remove: function remove(index) {\n        var newValue = getNewValue();\n        var indexSet = new Set(Array.isArray(index) ? index : [index]);\n        if (indexSet.size <= 0) {\n          return;\n        }\n        keyManager.keys = keyManager.keys.filter(function (_, keysIndex) {\n          return !indexSet.has(keysIndex);\n        });\n\n        // Trigger store change\n        onChange(newValue.filter(function (_, valueIndex) {\n          return !indexSet.has(valueIndex);\n        }));\n      },\n      move: function move(from, to) {\n        if (from === to) {\n          return;\n        }\n        var newValue = getNewValue();\n\n        // Do not handle out of range\n        if (from < 0 || from >= newValue.length || to < 0 || to >= newValue.length) {\n          return;\n        }\n        keyManager.keys = _move(keyManager.keys, from, to);\n\n        // Trigger store change\n        onChange(_move(newValue, from, to));\n      }\n    };\n    var listValue = value || [];\n    if (!Array.isArray(listValue)) {\n      listValue = [];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"Current value of '\".concat(prefixName.join(' > '), \"' is not an array type.\"));\n      }\n    }\n    return children(listValue.map(function (__, index) {\n      var key = keyManager.keys[index];\n      if (key === undefined) {\n        keyManager.keys[index] = keyManager.id;\n        key = keyManager.keys[index];\n        keyManager.id += 1;\n      }\n      return {\n        name: index,\n        key: key,\n        isListField: true\n      };\n    }), operations, meta);\n  })));\n}\nexport default List;", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "React", "warning", "FieldContext", "Field", "move", "_move", "getNamePath", "ListContext", "List", "_ref", "name", "initialValue", "children", "rules", "validate<PERSON><PERSON>ger", "isListField", "context", "useContext", "wrapperListContext", "keyRef", "useRef", "keys", "id", "keyManager", "current", "prefixName", "useMemo", "parentPrefixName", "concat", "fieldContext", "listContext", "<PERSON><PERSON><PERSON>", "namePath", "len", "length", "pathName", "slice", "shouldUpdate", "prevValue", "nextValue", "_ref2", "source", "createElement", "Provider", "value", "isList", "_ref3", "meta", "_ref3$value", "onChange", "getFieldValue", "getNewValue", "values", "operations", "add", "defaultValue", "index", "newValue", "process", "env", "NODE_ENV", "remove", "indexSet", "Set", "Array", "isArray", "size", "filter", "_", "keysIndex", "has", "valueIndex", "from", "to", "listValue", "join", "map", "__", "key", "undefined"], "sources": ["E:/code1/task3/frontend/node_modules/rc-field-form/es/List.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nimport FieldContext from \"./FieldContext\";\nimport Field from \"./Field\";\nimport { move as _move, getNamePath } from \"./utils/valueUtil\";\nimport ListContext from \"./ListContext\";\nfunction List(_ref) {\n  var name = _ref.name,\n    initialValue = _ref.initialValue,\n    children = _ref.children,\n    rules = _ref.rules,\n    validateTrigger = _ref.validateTrigger,\n    isListField = _ref.isListField;\n  var context = React.useContext(FieldContext);\n  var wrapperListContext = React.useContext(ListContext);\n  var keyRef = React.useRef({\n    keys: [],\n    id: 0\n  });\n  var keyManager = keyRef.current;\n  var prefixName = React.useMemo(function () {\n    var parentPrefixName = getNamePath(context.prefixName) || [];\n    return [].concat(_toConsumableArray(parentPrefixName), _toConsumableArray(getNamePath(name)));\n  }, [context.prefixName, name]);\n  var fieldContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, context), {}, {\n      prefixName: prefixName\n    });\n  }, [context, prefixName]);\n\n  // List context\n  var listContext = React.useMemo(function () {\n    return {\n      getKey: function getKey(namePath) {\n        var len = prefixName.length;\n        var pathName = namePath[len];\n        return [keyManager.keys[pathName], namePath.slice(len + 1)];\n      }\n    };\n  }, [prefixName]);\n\n  // User should not pass `children` as other type.\n  if (typeof children !== 'function') {\n    warning(false, 'Form.List only accepts function as children.');\n    return null;\n  }\n  var shouldUpdate = function shouldUpdate(prevValue, nextValue, _ref2) {\n    var source = _ref2.source;\n    if (source === 'internal') {\n      return false;\n    }\n    return prevValue !== nextValue;\n  };\n  return /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: listContext\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: fieldContext\n  }, /*#__PURE__*/React.createElement(Field, {\n    name: [],\n    shouldUpdate: shouldUpdate,\n    rules: rules,\n    validateTrigger: validateTrigger,\n    initialValue: initialValue,\n    isList: true,\n    isListField: isListField !== null && isListField !== void 0 ? isListField : !!wrapperListContext\n  }, function (_ref3, meta) {\n    var _ref3$value = _ref3.value,\n      value = _ref3$value === void 0 ? [] : _ref3$value,\n      onChange = _ref3.onChange;\n    var getFieldValue = context.getFieldValue;\n    var getNewValue = function getNewValue() {\n      var values = getFieldValue(prefixName || []);\n      return values || [];\n    };\n    /**\n     * Always get latest value in case user update fields by `form` api.\n     */\n    var operations = {\n      add: function add(defaultValue, index) {\n        // Mapping keys\n        var newValue = getNewValue();\n        if (index >= 0 && index <= newValue.length) {\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys.slice(0, index)), [keyManager.id], _toConsumableArray(keyManager.keys.slice(index)));\n          onChange([].concat(_toConsumableArray(newValue.slice(0, index)), [defaultValue], _toConsumableArray(newValue.slice(index))));\n        } else {\n          if (process.env.NODE_ENV !== 'production' && (index < 0 || index > newValue.length)) {\n            warning(false, 'The second parameter of the add function should be a valid positive number.');\n          }\n          keyManager.keys = [].concat(_toConsumableArray(keyManager.keys), [keyManager.id]);\n          onChange([].concat(_toConsumableArray(newValue), [defaultValue]));\n        }\n        keyManager.id += 1;\n      },\n      remove: function remove(index) {\n        var newValue = getNewValue();\n        var indexSet = new Set(Array.isArray(index) ? index : [index]);\n        if (indexSet.size <= 0) {\n          return;\n        }\n        keyManager.keys = keyManager.keys.filter(function (_, keysIndex) {\n          return !indexSet.has(keysIndex);\n        });\n\n        // Trigger store change\n        onChange(newValue.filter(function (_, valueIndex) {\n          return !indexSet.has(valueIndex);\n        }));\n      },\n      move: function move(from, to) {\n        if (from === to) {\n          return;\n        }\n        var newValue = getNewValue();\n\n        // Do not handle out of range\n        if (from < 0 || from >= newValue.length || to < 0 || to >= newValue.length) {\n          return;\n        }\n        keyManager.keys = _move(keyManager.keys, from, to);\n\n        // Trigger store change\n        onChange(_move(newValue, from, to));\n      }\n    };\n    var listValue = value || [];\n    if (!Array.isArray(listValue)) {\n      listValue = [];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"Current value of '\".concat(prefixName.join(' > '), \"' is not an array type.\"));\n      }\n    }\n    return children(listValue.map(function (__, index) {\n      var key = keyManager.keys[index];\n      if (key === undefined) {\n        keyManager.keys[index] = keyManager.id;\n        key = keyManager.keys[index];\n        keyManager.id += 1;\n      }\n      return {\n        name: index,\n        key: key,\n        isListField: true\n      };\n    }), operations, meta);\n  })));\n}\nexport default List;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,IAAI,IAAIC,KAAK,EAAEC,WAAW,QAAQ,mBAAmB;AAC9D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,YAAY,GAAGF,IAAI,CAACE,YAAY;IAChCC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,eAAe,GAAGL,IAAI,CAACK,eAAe;IACtCC,WAAW,GAAGN,IAAI,CAACM,WAAW;EAChC,IAAIC,OAAO,GAAGhB,KAAK,CAACiB,UAAU,CAACf,YAAY,CAAC;EAC5C,IAAIgB,kBAAkB,GAAGlB,KAAK,CAACiB,UAAU,CAACV,WAAW,CAAC;EACtD,IAAIY,MAAM,GAAGnB,KAAK,CAACoB,MAAM,CAAC;IACxBC,IAAI,EAAE,EAAE;IACRC,EAAE,EAAE;EACN,CAAC,CAAC;EACF,IAAIC,UAAU,GAAGJ,MAAM,CAACK,OAAO;EAC/B,IAAIC,UAAU,GAAGzB,KAAK,CAAC0B,OAAO,CAAC,YAAY;IACzC,IAAIC,gBAAgB,GAAGrB,WAAW,CAACU,OAAO,CAACS,UAAU,CAAC,IAAI,EAAE;IAC5D,OAAO,EAAE,CAACG,MAAM,CAAC7B,kBAAkB,CAAC4B,gBAAgB,CAAC,EAAE5B,kBAAkB,CAACO,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC;EAC/F,CAAC,EAAE,CAACM,OAAO,CAACS,UAAU,EAAEf,IAAI,CAAC,CAAC;EAC9B,IAAImB,YAAY,GAAG7B,KAAK,CAAC0B,OAAO,CAAC,YAAY;IAC3C,OAAO5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkB,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACnDS,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,OAAO,EAAES,UAAU,CAAC,CAAC;;EAEzB;EACA,IAAIK,WAAW,GAAG9B,KAAK,CAAC0B,OAAO,CAAC,YAAY;IAC1C,OAAO;MACLK,MAAM,EAAE,SAASA,MAAMA,CAACC,QAAQ,EAAE;QAChC,IAAIC,GAAG,GAAGR,UAAU,CAACS,MAAM;QAC3B,IAAIC,QAAQ,GAAGH,QAAQ,CAACC,GAAG,CAAC;QAC5B,OAAO,CAACV,UAAU,CAACF,IAAI,CAACc,QAAQ,CAAC,EAAEH,QAAQ,CAACI,KAAK,CAACH,GAAG,GAAG,CAAC,CAAC,CAAC;MAC7D;IACF,CAAC;EACH,CAAC,EAAE,CAACR,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAI,OAAOb,QAAQ,KAAK,UAAU,EAAE;IAClCX,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC;IAC9D,OAAO,IAAI;EACb;EACA,IAAIoC,YAAY,GAAG,SAASA,YAAYA,CAACC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAE;IACpE,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,IAAIA,MAAM,KAAK,UAAU,EAAE;MACzB,OAAO,KAAK;IACd;IACA,OAAOH,SAAS,KAAKC,SAAS;EAChC,CAAC;EACD,OAAO,aAAavC,KAAK,CAAC0C,aAAa,CAACnC,WAAW,CAACoC,QAAQ,EAAE;IAC5DC,KAAK,EAAEd;EACT,CAAC,EAAE,aAAa9B,KAAK,CAAC0C,aAAa,CAACxC,YAAY,CAACyC,QAAQ,EAAE;IACzDC,KAAK,EAAEf;EACT,CAAC,EAAE,aAAa7B,KAAK,CAAC0C,aAAa,CAACvC,KAAK,EAAE;IACzCO,IAAI,EAAE,EAAE;IACR2B,YAAY,EAAEA,YAAY;IAC1BxB,KAAK,EAAEA,KAAK;IACZC,eAAe,EAAEA,eAAe;IAChCH,YAAY,EAAEA,YAAY;IAC1BkC,MAAM,EAAE,IAAI;IACZ9B,WAAW,EAAEA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,CAACG;EAChF,CAAC,EAAE,UAAU4B,KAAK,EAAEC,IAAI,EAAE;IACxB,IAAIC,WAAW,GAAGF,KAAK,CAACF,KAAK;MAC3BA,KAAK,GAAGI,WAAW,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,WAAW;MACjDC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IAC3B,IAAIC,aAAa,GAAGlC,OAAO,CAACkC,aAAa;IACzC,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;MACvC,IAAIC,MAAM,GAAGF,aAAa,CAACzB,UAAU,IAAI,EAAE,CAAC;MAC5C,OAAO2B,MAAM,IAAI,EAAE;IACrB,CAAC;IACD;AACJ;AACA;IACI,IAAIC,UAAU,GAAG;MACfC,GAAG,EAAE,SAASA,GAAGA,CAACC,YAAY,EAAEC,KAAK,EAAE;QACrC;QACA,IAAIC,QAAQ,GAAGN,WAAW,CAAC,CAAC;QAC5B,IAAIK,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAIC,QAAQ,CAACvB,MAAM,EAAE;UAC1CX,UAAU,CAACF,IAAI,GAAG,EAAE,CAACO,MAAM,CAAC7B,kBAAkB,CAACwB,UAAU,CAACF,IAAI,CAACe,KAAK,CAAC,CAAC,EAAEoB,KAAK,CAAC,CAAC,EAAE,CAACjC,UAAU,CAACD,EAAE,CAAC,EAAEvB,kBAAkB,CAACwB,UAAU,CAACF,IAAI,CAACe,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC;UACnJP,QAAQ,CAAC,EAAE,CAACrB,MAAM,CAAC7B,kBAAkB,CAAC0D,QAAQ,CAACrB,KAAK,CAAC,CAAC,EAAEoB,KAAK,CAAC,CAAC,EAAE,CAACD,YAAY,CAAC,EAAExD,kBAAkB,CAAC0D,QAAQ,CAACrB,KAAK,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9H,CAAC,MAAM;UACL,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAKJ,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGC,QAAQ,CAACvB,MAAM,CAAC,EAAE;YACnFjC,OAAO,CAAC,KAAK,EAAE,6EAA6E,CAAC;UAC/F;UACAsB,UAAU,CAACF,IAAI,GAAG,EAAE,CAACO,MAAM,CAAC7B,kBAAkB,CAACwB,UAAU,CAACF,IAAI,CAAC,EAAE,CAACE,UAAU,CAACD,EAAE,CAAC,CAAC;UACjF2B,QAAQ,CAAC,EAAE,CAACrB,MAAM,CAAC7B,kBAAkB,CAAC0D,QAAQ,CAAC,EAAE,CAACF,YAAY,CAAC,CAAC,CAAC;QACnE;QACAhC,UAAU,CAACD,EAAE,IAAI,CAAC;MACpB,CAAC;MACDuC,MAAM,EAAE,SAASA,MAAMA,CAACL,KAAK,EAAE;QAC7B,IAAIC,QAAQ,GAAGN,WAAW,CAAC,CAAC;QAC5B,IAAIW,QAAQ,GAAG,IAAIC,GAAG,CAACC,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAC;QAC9D,IAAIM,QAAQ,CAACI,IAAI,IAAI,CAAC,EAAE;UACtB;QACF;QACA3C,UAAU,CAACF,IAAI,GAAGE,UAAU,CAACF,IAAI,CAAC8C,MAAM,CAAC,UAAUC,CAAC,EAAEC,SAAS,EAAE;UAC/D,OAAO,CAACP,QAAQ,CAACQ,GAAG,CAACD,SAAS,CAAC;QACjC,CAAC,CAAC;;QAEF;QACApB,QAAQ,CAACQ,QAAQ,CAACU,MAAM,CAAC,UAAUC,CAAC,EAAEG,UAAU,EAAE;UAChD,OAAO,CAACT,QAAQ,CAACQ,GAAG,CAACC,UAAU,CAAC;QAClC,CAAC,CAAC,CAAC;MACL,CAAC;MACDnE,IAAI,EAAE,SAASA,IAAIA,CAACoE,IAAI,EAAEC,EAAE,EAAE;QAC5B,IAAID,IAAI,KAAKC,EAAE,EAAE;UACf;QACF;QACA,IAAIhB,QAAQ,GAAGN,WAAW,CAAC,CAAC;;QAE5B;QACA,IAAIqB,IAAI,GAAG,CAAC,IAAIA,IAAI,IAAIf,QAAQ,CAACvB,MAAM,IAAIuC,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAIhB,QAAQ,CAACvB,MAAM,EAAE;UAC1E;QACF;QACAX,UAAU,CAACF,IAAI,GAAGhB,KAAK,CAACkB,UAAU,CAACF,IAAI,EAAEmD,IAAI,EAAEC,EAAE,CAAC;;QAElD;QACAxB,QAAQ,CAAC5C,KAAK,CAACoD,QAAQ,EAAEe,IAAI,EAAEC,EAAE,CAAC,CAAC;MACrC;IACF,CAAC;IACD,IAAIC,SAAS,GAAG9B,KAAK,IAAI,EAAE;IAC3B,IAAI,CAACoB,KAAK,CAACC,OAAO,CAACS,SAAS,CAAC,EAAE;MAC7BA,SAAS,GAAG,EAAE;MACd,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC3D,OAAO,CAAC,KAAK,EAAE,oBAAoB,CAAC2B,MAAM,CAACH,UAAU,CAACkD,IAAI,CAAC,KAAK,CAAC,EAAE,yBAAyB,CAAC,CAAC;MAChG;IACF;IACA,OAAO/D,QAAQ,CAAC8D,SAAS,CAACE,GAAG,CAAC,UAAUC,EAAE,EAAErB,KAAK,EAAE;MACjD,IAAIsB,GAAG,GAAGvD,UAAU,CAACF,IAAI,CAACmC,KAAK,CAAC;MAChC,IAAIsB,GAAG,KAAKC,SAAS,EAAE;QACrBxD,UAAU,CAACF,IAAI,CAACmC,KAAK,CAAC,GAAGjC,UAAU,CAACD,EAAE;QACtCwD,GAAG,GAAGvD,UAAU,CAACF,IAAI,CAACmC,KAAK,CAAC;QAC5BjC,UAAU,CAACD,EAAE,IAAI,CAAC;MACpB;MACA,OAAO;QACLZ,IAAI,EAAE8C,KAAK;QACXsB,GAAG,EAAEA,GAAG;QACR/D,WAAW,EAAE;MACf,CAAC;IACH,CAAC,CAAC,EAAEsC,UAAU,EAAEN,IAAI,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAevC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}