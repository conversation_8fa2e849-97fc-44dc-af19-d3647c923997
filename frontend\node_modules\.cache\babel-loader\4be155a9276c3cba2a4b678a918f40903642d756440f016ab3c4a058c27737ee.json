{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport UpOutlined from \"@ant-design/icons/es/icons/UpOutlined\";\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nfunction getValidNumber(num) {\n  return typeof num === 'number' && !Number.isNaN(num) ? Math.round(num) : 0;\n}\nconst SplitBar = props => {\n  const {\n    prefixCls,\n    vertical,\n    index,\n    active,\n    ariaNow,\n    ariaMin,\n    ariaMax,\n    resizable,\n    startCollapsible,\n    endCollapsible,\n    onOffsetStart,\n    onOffsetUpdate,\n    onOffsetEnd,\n    onCollapse,\n    lazy,\n    containerSize\n  } = props;\n  const splitBarPrefixCls = `${prefixCls}-bar`;\n  // ======================== Resize ========================\n  const [startPos, setStartPos] = useState(null);\n  const [constrainedOffset, setConstrainedOffset] = useState(0);\n  const constrainedOffsetX = vertical ? 0 : constrainedOffset;\n  const constrainedOffsetY = vertical ? constrainedOffset : 0;\n  const onMouseDown = e => {\n    if (resizable && e.currentTarget) {\n      setStartPos([e.pageX, e.pageY]);\n      onOffsetStart(index);\n    }\n  };\n  const onTouchStart = e => {\n    if (resizable && e.touches.length === 1) {\n      const touch = e.touches[0];\n      setStartPos([touch.pageX, touch.pageY]);\n      onOffsetStart(index);\n    }\n  };\n  // Updated constraint calculation\n  const getConstrainedOffset = rawOffset => {\n    const currentPos = containerSize * ariaNow / 100;\n    const newPos = currentPos + rawOffset;\n    // Calculate available space\n    const minAllowed = Math.max(0, containerSize * ariaMin / 100);\n    const maxAllowed = Math.min(containerSize, containerSize * ariaMax / 100);\n    // Constrain new position within bounds\n    const clampedPos = Math.max(minAllowed, Math.min(maxAllowed, newPos));\n    return clampedPos - currentPos;\n  };\n  const handleLazyMove = useEvent((offsetX, offsetY) => {\n    const constrainedOffsetValue = getConstrainedOffset(vertical ? offsetY : offsetX);\n    setConstrainedOffset(constrainedOffsetValue);\n  });\n  const handleLazyEnd = useEvent(() => {\n    onOffsetUpdate(index, constrainedOffsetX, constrainedOffsetY, true);\n    setConstrainedOffset(0);\n    onOffsetEnd(true);\n  });\n  React.useLayoutEffect(() => {\n    if (startPos) {\n      const onMouseMove = e => {\n        const {\n          pageX,\n          pageY\n        } = e;\n        const offsetX = pageX - startPos[0];\n        const offsetY = pageY - startPos[1];\n        if (lazy) {\n          handleLazyMove(offsetX, offsetY);\n        } else {\n          onOffsetUpdate(index, offsetX, offsetY);\n        }\n      };\n      const onMouseUp = () => {\n        if (lazy) {\n          handleLazyEnd();\n        } else {\n          onOffsetEnd();\n        }\n        setStartPos(null);\n      };\n      const handleTouchMove = e => {\n        if (e.touches.length === 1) {\n          const touch = e.touches[0];\n          const offsetX = touch.pageX - startPos[0];\n          const offsetY = touch.pageY - startPos[1];\n          if (lazy) {\n            handleLazyMove(offsetX, offsetY);\n          } else {\n            onOffsetUpdate(index, offsetX, offsetY);\n          }\n        }\n      };\n      const handleTouchEnd = () => {\n        if (lazy) {\n          handleLazyEnd();\n        } else {\n          onOffsetEnd();\n        }\n        setStartPos(null);\n      };\n      const eventHandlerMap = {\n        mousemove: onMouseMove,\n        mouseup: onMouseUp,\n        touchmove: handleTouchMove,\n        touchend: handleTouchEnd\n      };\n      for (const [event, handler] of Object.entries(eventHandlerMap)) {\n        window.addEventListener(event, handler);\n      }\n      return () => {\n        for (const [event, handler] of Object.entries(eventHandlerMap)) {\n          window.removeEventListener(event, handler);\n        }\n      };\n    }\n  }, [startPos]);\n  const transformStyle = {\n    [`--${splitBarPrefixCls}-preview-offset`]: `${constrainedOffset}px`\n  };\n  // ======================== Render ========================\n  const StartIcon = vertical ? UpOutlined : LeftOutlined;\n  const EndIcon = vertical ? DownOutlined : RightOutlined;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: splitBarPrefixCls,\n    role: \"separator\",\n    \"aria-valuenow\": getValidNumber(ariaNow),\n    \"aria-valuemin\": getValidNumber(ariaMin),\n    \"aria-valuemax\": getValidNumber(ariaMax)\n  }, lazy && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-preview`, {\n      [`${splitBarPrefixCls}-preview-active`]: !!constrainedOffset\n    }),\n    style: transformStyle\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-dragger`, {\n      [`${splitBarPrefixCls}-dragger-disabled`]: !resizable,\n      [`${splitBarPrefixCls}-dragger-active`]: active\n    }),\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart\n  }), startCollapsible && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-collapse-bar`, `${splitBarPrefixCls}-collapse-bar-start`),\n    onClick: () => onCollapse(index, 'start')\n  }, /*#__PURE__*/React.createElement(StartIcon, {\n    className: classNames(`${splitBarPrefixCls}-collapse-icon`, `${splitBarPrefixCls}-collapse-start`)\n  }))), endCollapsible && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-collapse-bar`, `${splitBarPrefixCls}-collapse-bar-end`),\n    onClick: () => onCollapse(index, 'end')\n  }, /*#__PURE__*/React.createElement(EndIcon, {\n    className: classNames(`${splitBarPrefixCls}-collapse-icon`, `${splitBarPrefixCls}-collapse-end`)\n  }))));\n};\nexport default SplitBar;", "map": {"version": 3, "names": ["React", "useState", "DownOutlined", "LeftOutlined", "RightOutlined", "UpOutlined", "classNames", "useEvent", "getValidNumber", "num", "Number", "isNaN", "Math", "round", "SplitBar", "props", "prefixCls", "vertical", "index", "active", "ariaNow", "aria<PERSON><PERSON>", "ariaMax", "resizable", "startCollapsible", "endCollapsible", "onOffsetStart", "onOffsetUpdate", "onOffsetEnd", "onCollapse", "lazy", "containerSize", "splitBarPrefixCls", "startPos", "setStartPos", "constrainedOffset", "setConstrainedOffset", "constrainedOffsetX", "constrainedOffsetY", "onMouseDown", "e", "currentTarget", "pageX", "pageY", "onTouchStart", "touches", "length", "touch", "getConstrainedOffset", "rawOffset", "currentPos", "newPos", "minAllowed", "max", "maxAllowed", "min", "clampedPos", "handleLazyMove", "offsetX", "offsetY", "constrainedOffsetValue", "handleLazyEnd", "useLayoutEffect", "onMouseMove", "onMouseUp", "handleTouchMove", "handleTouchEnd", "eventHandlerMap", "mousemove", "mouseup", "touchmove", "touchend", "event", "handler", "Object", "entries", "window", "addEventListener", "removeEventListener", "transformStyle", "StartIcon", "EndIcon", "createElement", "className", "role", "style", "onClick"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/splitter/SplitBar.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport UpOutlined from \"@ant-design/icons/es/icons/UpOutlined\";\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nfunction getValidNumber(num) {\n  return typeof num === 'number' && !Number.isNaN(num) ? Math.round(num) : 0;\n}\nconst SplitBar = props => {\n  const {\n    prefixCls,\n    vertical,\n    index,\n    active,\n    ariaNow,\n    ariaMin,\n    ariaMax,\n    resizable,\n    startCollapsible,\n    endCollapsible,\n    onOffsetStart,\n    onOffsetUpdate,\n    onOffsetEnd,\n    onCollapse,\n    lazy,\n    containerSize\n  } = props;\n  const splitBarPrefixCls = `${prefixCls}-bar`;\n  // ======================== Resize ========================\n  const [startPos, setStartPos] = useState(null);\n  const [constrainedOffset, setConstrainedOffset] = useState(0);\n  const constrainedOffsetX = vertical ? 0 : constrainedOffset;\n  const constrainedOffsetY = vertical ? constrainedOffset : 0;\n  const onMouseDown = e => {\n    if (resizable && e.currentTarget) {\n      setStartPos([e.pageX, e.pageY]);\n      onOffsetStart(index);\n    }\n  };\n  const onTouchStart = e => {\n    if (resizable && e.touches.length === 1) {\n      const touch = e.touches[0];\n      setStartPos([touch.pageX, touch.pageY]);\n      onOffsetStart(index);\n    }\n  };\n  // Updated constraint calculation\n  const getConstrainedOffset = rawOffset => {\n    const currentPos = containerSize * ariaNow / 100;\n    const newPos = currentPos + rawOffset;\n    // Calculate available space\n    const minAllowed = Math.max(0, containerSize * ariaMin / 100);\n    const maxAllowed = Math.min(containerSize, containerSize * ariaMax / 100);\n    // Constrain new position within bounds\n    const clampedPos = Math.max(minAllowed, Math.min(maxAllowed, newPos));\n    return clampedPos - currentPos;\n  };\n  const handleLazyMove = useEvent((offsetX, offsetY) => {\n    const constrainedOffsetValue = getConstrainedOffset(vertical ? offsetY : offsetX);\n    setConstrainedOffset(constrainedOffsetValue);\n  });\n  const handleLazyEnd = useEvent(() => {\n    onOffsetUpdate(index, constrainedOffsetX, constrainedOffsetY, true);\n    setConstrainedOffset(0);\n    onOffsetEnd(true);\n  });\n  React.useLayoutEffect(() => {\n    if (startPos) {\n      const onMouseMove = e => {\n        const {\n          pageX,\n          pageY\n        } = e;\n        const offsetX = pageX - startPos[0];\n        const offsetY = pageY - startPos[1];\n        if (lazy) {\n          handleLazyMove(offsetX, offsetY);\n        } else {\n          onOffsetUpdate(index, offsetX, offsetY);\n        }\n      };\n      const onMouseUp = () => {\n        if (lazy) {\n          handleLazyEnd();\n        } else {\n          onOffsetEnd();\n        }\n        setStartPos(null);\n      };\n      const handleTouchMove = e => {\n        if (e.touches.length === 1) {\n          const touch = e.touches[0];\n          const offsetX = touch.pageX - startPos[0];\n          const offsetY = touch.pageY - startPos[1];\n          if (lazy) {\n            handleLazyMove(offsetX, offsetY);\n          } else {\n            onOffsetUpdate(index, offsetX, offsetY);\n          }\n        }\n      };\n      const handleTouchEnd = () => {\n        if (lazy) {\n          handleLazyEnd();\n        } else {\n          onOffsetEnd();\n        }\n        setStartPos(null);\n      };\n      const eventHandlerMap = {\n        mousemove: onMouseMove,\n        mouseup: onMouseUp,\n        touchmove: handleTouchMove,\n        touchend: handleTouchEnd\n      };\n      for (const [event, handler] of Object.entries(eventHandlerMap)) {\n        window.addEventListener(event, handler);\n      }\n      return () => {\n        for (const [event, handler] of Object.entries(eventHandlerMap)) {\n          window.removeEventListener(event, handler);\n        }\n      };\n    }\n  }, [startPos]);\n  const transformStyle = {\n    [`--${splitBarPrefixCls}-preview-offset`]: `${constrainedOffset}px`\n  };\n  // ======================== Render ========================\n  const StartIcon = vertical ? UpOutlined : LeftOutlined;\n  const EndIcon = vertical ? DownOutlined : RightOutlined;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: splitBarPrefixCls,\n    role: \"separator\",\n    \"aria-valuenow\": getValidNumber(ariaNow),\n    \"aria-valuemin\": getValidNumber(ariaMin),\n    \"aria-valuemax\": getValidNumber(ariaMax)\n  }, lazy && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-preview`, {\n      [`${splitBarPrefixCls}-preview-active`]: !!constrainedOffset\n    }),\n    style: transformStyle\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-dragger`, {\n      [`${splitBarPrefixCls}-dragger-disabled`]: !resizable,\n      [`${splitBarPrefixCls}-dragger-active`]: active\n    }),\n    onMouseDown: onMouseDown,\n    onTouchStart: onTouchStart\n  }), startCollapsible && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-collapse-bar`, `${splitBarPrefixCls}-collapse-bar-start`),\n    onClick: () => onCollapse(index, 'start')\n  }, /*#__PURE__*/React.createElement(StartIcon, {\n    className: classNames(`${splitBarPrefixCls}-collapse-icon`, `${splitBarPrefixCls}-collapse-start`)\n  }))), endCollapsible && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${splitBarPrefixCls}-collapse-bar`, `${splitBarPrefixCls}-collapse-bar-end`),\n    onClick: () => onCollapse(index, 'end')\n  }, /*#__PURE__*/React.createElement(EndIcon, {\n    className: classNames(`${splitBarPrefixCls}-collapse-icon`, `${splitBarPrefixCls}-collapse-end`)\n  }))));\n};\nexport default SplitBar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,uCAAuC;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,GAAG,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC,GAAG,CAAC;AAC5E;AACA,MAAMK,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,SAAS;IACTC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,gBAAgB;IAChBC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC,UAAU;IACVC,IAAI;IACJC;EACF,CAAC,GAAGhB,KAAK;EACT,MAAMiB,iBAAiB,GAAG,GAAGhB,SAAS,MAAM;EAC5C;EACA,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACkC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAMoC,kBAAkB,GAAGpB,QAAQ,GAAG,CAAC,GAAGkB,iBAAiB;EAC3D,MAAMG,kBAAkB,GAAGrB,QAAQ,GAAGkB,iBAAiB,GAAG,CAAC;EAC3D,MAAMI,WAAW,GAAGC,CAAC,IAAI;IACvB,IAAIjB,SAAS,IAAIiB,CAAC,CAACC,aAAa,EAAE;MAChCP,WAAW,CAAC,CAACM,CAAC,CAACE,KAAK,EAAEF,CAAC,CAACG,KAAK,CAAC,CAAC;MAC/BjB,aAAa,CAACR,KAAK,CAAC;IACtB;EACF,CAAC;EACD,MAAM0B,YAAY,GAAGJ,CAAC,IAAI;IACxB,IAAIjB,SAAS,IAAIiB,CAAC,CAACK,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;MACvC,MAAMC,KAAK,GAAGP,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC;MAC1BX,WAAW,CAAC,CAACa,KAAK,CAACL,KAAK,EAAEK,KAAK,CAACJ,KAAK,CAAC,CAAC;MACvCjB,aAAa,CAACR,KAAK,CAAC;IACtB;EACF,CAAC;EACD;EACA,MAAM8B,oBAAoB,GAAGC,SAAS,IAAI;IACxC,MAAMC,UAAU,GAAGnB,aAAa,GAAGX,OAAO,GAAG,GAAG;IAChD,MAAM+B,MAAM,GAAGD,UAAU,GAAGD,SAAS;IACrC;IACA,MAAMG,UAAU,GAAGxC,IAAI,CAACyC,GAAG,CAAC,CAAC,EAAEtB,aAAa,GAAGV,OAAO,GAAG,GAAG,CAAC;IAC7D,MAAMiC,UAAU,GAAG1C,IAAI,CAAC2C,GAAG,CAACxB,aAAa,EAAEA,aAAa,GAAGT,OAAO,GAAG,GAAG,CAAC;IACzE;IACA,MAAMkC,UAAU,GAAG5C,IAAI,CAACyC,GAAG,CAACD,UAAU,EAAExC,IAAI,CAAC2C,GAAG,CAACD,UAAU,EAAEH,MAAM,CAAC,CAAC;IACrE,OAAOK,UAAU,GAAGN,UAAU;EAChC,CAAC;EACD,MAAMO,cAAc,GAAGlD,QAAQ,CAAC,CAACmD,OAAO,EAAEC,OAAO,KAAK;IACpD,MAAMC,sBAAsB,GAAGZ,oBAAoB,CAAC/B,QAAQ,GAAG0C,OAAO,GAAGD,OAAO,CAAC;IACjFtB,oBAAoB,CAACwB,sBAAsB,CAAC;EAC9C,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGtD,QAAQ,CAAC,MAAM;IACnCoB,cAAc,CAACT,KAAK,EAAEmB,kBAAkB,EAAEC,kBAAkB,EAAE,IAAI,CAAC;IACnEF,oBAAoB,CAAC,CAAC,CAAC;IACvBR,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC,CAAC;EACF5B,KAAK,CAAC8D,eAAe,CAAC,MAAM;IAC1B,IAAI7B,QAAQ,EAAE;MACZ,MAAM8B,WAAW,GAAGvB,CAAC,IAAI;QACvB,MAAM;UACJE,KAAK;UACLC;QACF,CAAC,GAAGH,CAAC;QACL,MAAMkB,OAAO,GAAGhB,KAAK,GAAGT,QAAQ,CAAC,CAAC,CAAC;QACnC,MAAM0B,OAAO,GAAGhB,KAAK,GAAGV,QAAQ,CAAC,CAAC,CAAC;QACnC,IAAIH,IAAI,EAAE;UACR2B,cAAc,CAACC,OAAO,EAAEC,OAAO,CAAC;QAClC,CAAC,MAAM;UACLhC,cAAc,CAACT,KAAK,EAAEwC,OAAO,EAAEC,OAAO,CAAC;QACzC;MACF,CAAC;MACD,MAAMK,SAAS,GAAGA,CAAA,KAAM;QACtB,IAAIlC,IAAI,EAAE;UACR+B,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UACLjC,WAAW,CAAC,CAAC;QACf;QACAM,WAAW,CAAC,IAAI,CAAC;MACnB,CAAC;MACD,MAAM+B,eAAe,GAAGzB,CAAC,IAAI;QAC3B,IAAIA,CAAC,CAACK,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;UAC1B,MAAMC,KAAK,GAAGP,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC;UAC1B,MAAMa,OAAO,GAAGX,KAAK,CAACL,KAAK,GAAGT,QAAQ,CAAC,CAAC,CAAC;UACzC,MAAM0B,OAAO,GAAGZ,KAAK,CAACJ,KAAK,GAAGV,QAAQ,CAAC,CAAC,CAAC;UACzC,IAAIH,IAAI,EAAE;YACR2B,cAAc,CAACC,OAAO,EAAEC,OAAO,CAAC;UAClC,CAAC,MAAM;YACLhC,cAAc,CAACT,KAAK,EAAEwC,OAAO,EAAEC,OAAO,CAAC;UACzC;QACF;MACF,CAAC;MACD,MAAMO,cAAc,GAAGA,CAAA,KAAM;QAC3B,IAAIpC,IAAI,EAAE;UACR+B,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UACLjC,WAAW,CAAC,CAAC;QACf;QACAM,WAAW,CAAC,IAAI,CAAC;MACnB,CAAC;MACD,MAAMiC,eAAe,GAAG;QACtBC,SAAS,EAAEL,WAAW;QACtBM,OAAO,EAAEL,SAAS;QAClBM,SAAS,EAAEL,eAAe;QAC1BM,QAAQ,EAAEL;MACZ,CAAC;MACD,KAAK,MAAM,CAACM,KAAK,EAAEC,OAAO,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,eAAe,CAAC,EAAE;QAC9DS,MAAM,CAACC,gBAAgB,CAACL,KAAK,EAAEC,OAAO,CAAC;MACzC;MACA,OAAO,MAAM;QACX,KAAK,MAAM,CAACD,KAAK,EAAEC,OAAO,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,eAAe,CAAC,EAAE;UAC9DS,MAAM,CAACE,mBAAmB,CAACN,KAAK,EAAEC,OAAO,CAAC;QAC5C;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACxC,QAAQ,CAAC,CAAC;EACd,MAAM8C,cAAc,GAAG;IACrB,CAAC,KAAK/C,iBAAiB,iBAAiB,GAAG,GAAGG,iBAAiB;EACjE,CAAC;EACD;EACA,MAAM6C,SAAS,GAAG/D,QAAQ,GAAGZ,UAAU,GAAGF,YAAY;EACtD,MAAM8E,OAAO,GAAGhE,QAAQ,GAAGf,YAAY,GAAGE,aAAa;EACvD,OAAO,aAAaJ,KAAK,CAACkF,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEnD,iBAAiB;IAC5BoD,IAAI,EAAE,WAAW;IACjB,eAAe,EAAE5E,cAAc,CAACY,OAAO,CAAC;IACxC,eAAe,EAAEZ,cAAc,CAACa,OAAO,CAAC;IACxC,eAAe,EAAEb,cAAc,CAACc,OAAO;EACzC,CAAC,EAAEQ,IAAI,KAAK,aAAa9B,KAAK,CAACkF,aAAa,CAAC,KAAK,EAAE;IAClDC,SAAS,EAAE7E,UAAU,CAAC,GAAG0B,iBAAiB,UAAU,EAAE;MACpD,CAAC,GAAGA,iBAAiB,iBAAiB,GAAG,CAAC,CAACG;IAC7C,CAAC,CAAC;IACFkD,KAAK,EAAEN;EACT,CAAC,CAAC,CAAC,EAAE,aAAa/E,KAAK,CAACkF,aAAa,CAAC,KAAK,EAAE;IAC3CC,SAAS,EAAE7E,UAAU,CAAC,GAAG0B,iBAAiB,UAAU,EAAE;MACpD,CAAC,GAAGA,iBAAiB,mBAAmB,GAAG,CAACT,SAAS;MACrD,CAAC,GAAGS,iBAAiB,iBAAiB,GAAGb;IAC3C,CAAC,CAAC;IACFoB,WAAW,EAAEA,WAAW;IACxBK,YAAY,EAAEA;EAChB,CAAC,CAAC,EAAEpB,gBAAgB,KAAK,aAAaxB,KAAK,CAACkF,aAAa,CAAC,KAAK,EAAE;IAC/DC,SAAS,EAAE7E,UAAU,CAAC,GAAG0B,iBAAiB,eAAe,EAAE,GAAGA,iBAAiB,qBAAqB,CAAC;IACrGsD,OAAO,EAAEA,CAAA,KAAMzD,UAAU,CAACX,KAAK,EAAE,OAAO;EAC1C,CAAC,EAAE,aAAalB,KAAK,CAACkF,aAAa,CAACF,SAAS,EAAE;IAC7CG,SAAS,EAAE7E,UAAU,CAAC,GAAG0B,iBAAiB,gBAAgB,EAAE,GAAGA,iBAAiB,iBAAiB;EACnG,CAAC,CAAC,CAAC,CAAC,EAAEP,cAAc,KAAK,aAAazB,KAAK,CAACkF,aAAa,CAAC,KAAK,EAAE;IAC/DC,SAAS,EAAE7E,UAAU,CAAC,GAAG0B,iBAAiB,eAAe,EAAE,GAAGA,iBAAiB,mBAAmB,CAAC;IACnGsD,OAAO,EAAEA,CAAA,KAAMzD,UAAU,CAACX,KAAK,EAAE,KAAK;EACxC,CAAC,EAAE,aAAalB,KAAK,CAACkF,aAAa,CAACD,OAAO,EAAE;IAC3CE,SAAS,EAAE7E,UAAU,CAAC,GAAG0B,iBAAiB,gBAAgB,EAAE,GAAGA,iBAAiB,eAAe;EACjG,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,eAAelB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}