{"ast": null, "code": "export function getTargetRect(target) {\n  return target !== window ? target.getBoundingClientRect() : {\n    top: 0,\n    bottom: window.innerHeight\n  };\n}\nexport function getFixedTop(placeholderRect, targetRect, offsetTop) {\n  if (offsetTop !== undefined && Math.round(targetRect.top) > Math.round(placeholderRect.top) - offsetTop) {\n    return offsetTop + targetRect.top;\n  }\n  return undefined;\n}\nexport function getFixedBottom(placeholderRect, targetRect, offsetBottom) {\n  if (offsetBottom !== undefined && Math.round(targetRect.bottom) < Math.round(placeholderRect.bottom) + offsetBottom) {\n    const targetBottomOffset = window.innerHeight - targetRect.bottom;\n    return offsetBottom + targetBottomOffset;\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["getTargetRect", "target", "window", "getBoundingClientRect", "top", "bottom", "innerHeight", "getFixedTop", "placeholder<PERSON><PERSON><PERSON>", "targetRect", "offsetTop", "undefined", "Math", "round", "getFixedBottom", "offsetBottom", "targetBottomOffset"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/affix/utils.js"], "sourcesContent": ["export function getTargetRect(target) {\n  return target !== window ? target.getBoundingClientRect() : {\n    top: 0,\n    bottom: window.innerHeight\n  };\n}\nexport function getFixedTop(placeholderRect, targetRect, offsetTop) {\n  if (offsetTop !== undefined && Math.round(targetRect.top) > Math.round(placeholderRect.top) - offsetTop) {\n    return offsetTop + targetRect.top;\n  }\n  return undefined;\n}\nexport function getFixedBottom(placeholderRect, targetRect, offsetBottom) {\n  if (offsetBottom !== undefined && Math.round(targetRect.bottom) < Math.round(placeholderRect.bottom) + offsetBottom) {\n    const targetBottomOffset = window.innerHeight - targetRect.bottom;\n    return offsetBottom + targetBottomOffset;\n  }\n  return undefined;\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAOA,MAAM,KAAKC,MAAM,GAAGD,MAAM,CAACE,qBAAqB,CAAC,CAAC,GAAG;IAC1DC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAEH,MAAM,CAACI;EACjB,CAAC;AACH;AACA,OAAO,SAASC,WAAWA,CAACC,eAAe,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAClE,IAAIA,SAAS,KAAKC,SAAS,IAAIC,IAAI,CAACC,KAAK,CAACJ,UAAU,CAACL,GAAG,CAAC,GAAGQ,IAAI,CAACC,KAAK,CAACL,eAAe,CAACJ,GAAG,CAAC,GAAGM,SAAS,EAAE;IACvG,OAAOA,SAAS,GAAGD,UAAU,CAACL,GAAG;EACnC;EACA,OAAOO,SAAS;AAClB;AACA,OAAO,SAASG,cAAcA,CAACN,eAAe,EAAEC,UAAU,EAAEM,YAAY,EAAE;EACxE,IAAIA,YAAY,KAAKJ,SAAS,IAAIC,IAAI,CAACC,KAAK,CAACJ,UAAU,CAACJ,MAAM,CAAC,GAAGO,IAAI,CAACC,KAAK,CAACL,eAAe,CAACH,MAAM,CAAC,GAAGU,YAAY,EAAE;IACnH,MAAMC,kBAAkB,GAAGd,MAAM,CAACI,WAAW,GAAGG,UAAU,CAACJ,MAAM;IACjE,OAAOU,YAAY,GAAGC,kBAAkB;EAC1C;EACA,OAAOL,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}