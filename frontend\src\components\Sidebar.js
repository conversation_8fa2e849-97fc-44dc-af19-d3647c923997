import React from 'react';
import { Layout, Menu } from 'antd';
import { DesktopOutlined, FileOutlined, SettingOutlined, DashboardOutlined, NodeIndexOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Sider } = Layout;

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
      onClick: () => navigate('/dashboard'),
    },
    {
      key: '/tasks',
      icon: <DesktopOutlined />,
      label: '任务管理',
      onClick: () => navigate('/tasks'),
    },
    {
      key: '/workflows',
      icon: <NodeIndexOutlined />,
      label: '任务编排',
      onClick: () => navigate('/workflows'),
    },
    {
      key: '/scripts',
      icon: <FileOutlined />,
      label: '脚本管理',
      onClick: () => navigate('/scripts'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      children: [
        {
          key: '/settings',
          label: '通用设置',
          onClick: () => navigate('/settings'),
        },
        {
          key: '/basic-settings',
          label: '基本设置',
          onClick: () => navigate('/basic-settings'),
        },
        {
          key: '/log-settings',
          label: '日志设置',
          onClick: () => navigate('/log-settings'),
        },
        {
          key: '/security-settings',
          label: '安全设置',
          onClick: () => navigate('/security-settings'),
        },
      ],
    },
  ];
  
  const selectedKey = menuItems.find(item => 
    location.pathname.startsWith(item.key)
  )?.key || '/tasks';

  return (
    <Sider>
      <div className="logo" style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.2)' }} />
      <Menu 
        theme="dark" 
        selectedKeys={[selectedKey]} 
        mode="inline" 
        items={menuItems}
      />
    </Sider>
  );
};

export default Sidebar;