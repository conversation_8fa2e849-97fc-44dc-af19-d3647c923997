{"name": "task-manager-frontend", "version": "1.0.0", "description": "任务管理系统前端", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --coverage --watchAll=false", "eject": "react-scripts eject"}, "dependencies": {"@ant-design/icons": "^5.2.5", "antd": "^5.8.3", "axios": "^1.6.0", "moment": "^2.29.4", "pinia": "^2.1.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "reactflow": "^11.11.4", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "vue": "^3.4.0", "vue-router": "^4.2.5", "web-vitals": "^5.0.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}