{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { ConfigContext } from '../../config-provider';\nimport Input from '../Input';\nconst OTPInput = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      className,\n      value,\n      onChange,\n      onActiveChange,\n      index,\n      mask\n    } = props,\n    restProps = __rest(props, [\"className\", \"value\", \"onChange\", \"onActiveChange\", \"index\", \"mask\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('otp');\n  const maskValue = typeof mask === 'string' ? mask : value;\n  // ========================== Ref ===========================\n  const inputRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => inputRef.current);\n  // ========================= Input ==========================\n  const onInternalChange = e => {\n    onChange(index, e.target.value);\n  };\n  // ========================= Focus ==========================\n  const syncSelection = () => {\n    raf(() => {\n      var _a;\n      const inputEle = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.input;\n      if (document.activeElement === inputEle && inputEle) {\n        inputEle.select();\n      }\n    });\n  };\n  // ======================== Keyboard ========================\n  const onInternalKeyDown = event => {\n    const {\n      key,\n      ctrlKey,\n      metaKey\n    } = event;\n    if (key === 'ArrowLeft') {\n      onActiveChange(index - 1);\n    } else if (key === 'ArrowRight') {\n      onActiveChange(index + 1);\n    } else if (key === 'z' && (ctrlKey || metaKey)) {\n      event.preventDefault();\n    }\n    syncSelection();\n  };\n  const onInternalKeyUp = e => {\n    if (e.key === 'Backspace' && !value) {\n      onActiveChange(index - 1);\n    }\n    syncSelection();\n  };\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-input-wrapper`,\n    role: \"presentation\"\n  }, mask && value !== '' && value !== undefined && (/*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-mask-icon`,\n    \"aria-hidden\": \"true\"\n  }, maskValue)), /*#__PURE__*/React.createElement(Input, Object.assign({\n    \"aria-label\": `OTP Input ${index + 1}`,\n    type: mask === true ? 'password' : 'text'\n  }, restProps, {\n    ref: inputRef,\n    value: value,\n    onInput: onInternalChange,\n    onFocus: syncSelection,\n    onKeyDown: onInternalKeyDown,\n    onKeyUp: onInternalKeyUp,\n    onMouseDown: syncSelection,\n    onMouseUp: syncSelection,\n    className: classNames(className, {\n      [`${prefixCls}-mask-input`]: mask\n    })\n  })));\n});\nexport default OTPInput;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "raf", "ConfigContext", "Input", "OTPInput", "forwardRef", "props", "ref", "className", "value", "onChange", "onActiveChange", "index", "mask", "restProps", "getPrefixCls", "useContext", "prefixCls", "maskValue", "inputRef", "useRef", "useImperativeHandle", "current", "onInternalChange", "target", "syncSelection", "_a", "inputEle", "input", "document", "activeElement", "select", "onInternalKeyDown", "event", "key", "ctrl<PERSON>ey", "metaKey", "preventDefault", "onInternalKeyUp", "createElement", "role", "undefined", "assign", "type", "onInput", "onFocus", "onKeyDown", "onKeyUp", "onMouseDown", "onMouseUp"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/input/OTP/OTPInput.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport { ConfigContext } from '../../config-provider';\nimport Input from '../Input';\nconst OTPInput = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      className,\n      value,\n      onChange,\n      onActiveChange,\n      index,\n      mask\n    } = props,\n    restProps = __rest(props, [\"className\", \"value\", \"onChange\", \"onActiveChange\", \"index\", \"mask\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('otp');\n  const maskValue = typeof mask === 'string' ? mask : value;\n  // ========================== Ref ===========================\n  const inputRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => inputRef.current);\n  // ========================= Input ==========================\n  const onInternalChange = e => {\n    onChange(index, e.target.value);\n  };\n  // ========================= Focus ==========================\n  const syncSelection = () => {\n    raf(() => {\n      var _a;\n      const inputEle = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.input;\n      if (document.activeElement === inputEle && inputEle) {\n        inputEle.select();\n      }\n    });\n  };\n  // ======================== Keyboard ========================\n  const onInternalKeyDown = event => {\n    const {\n      key,\n      ctrlKey,\n      metaKey\n    } = event;\n    if (key === 'ArrowLeft') {\n      onActiveChange(index - 1);\n    } else if (key === 'ArrowRight') {\n      onActiveChange(index + 1);\n    } else if (key === 'z' && (ctrlKey || metaKey)) {\n      event.preventDefault();\n    }\n    syncSelection();\n  };\n  const onInternalKeyUp = e => {\n    if (e.key === 'Backspace' && !value) {\n      onActiveChange(index - 1);\n    }\n    syncSelection();\n  };\n  // ========================= Render =========================\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-input-wrapper`,\n    role: \"presentation\"\n  }, mask && value !== '' && value !== undefined && (/*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-mask-icon`,\n    \"aria-hidden\": \"true\"\n  }, maskValue)), /*#__PURE__*/React.createElement(Input, Object.assign({\n    \"aria-label\": `OTP Input ${index + 1}`,\n    type: mask === true ? 'password' : 'text'\n  }, restProps, {\n    ref: inputRef,\n    value: value,\n    onInput: onInternalChange,\n    onFocus: syncSelection,\n    onKeyDown: onInternalKeyDown,\n    onKeyUp: onInternalKeyUp,\n    onMouseDown: syncSelection,\n    onMouseUp: syncSelection,\n    className: classNames(className, {\n      [`${prefixCls}-mask-input`]: mask\n    })\n  })));\n});\nexport default OTPInput;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,KAAK,MAAM,UAAU;AAC5B,MAAMC,QAAQ,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC7D,MAAM;MACFC,SAAS;MACTC,KAAK;MACLC,QAAQ;MACRC,cAAc;MACdC,KAAK;MACLC;IACF,CAAC,GAAGP,KAAK;IACTQ,SAAS,GAAG7B,MAAM,CAACqB,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAClG,MAAM;IACJS;EACF,CAAC,GAAGhB,KAAK,CAACiB,UAAU,CAACd,aAAa,CAAC;EACnC,MAAMe,SAAS,GAAGF,YAAY,CAAC,KAAK,CAAC;EACrC,MAAMG,SAAS,GAAG,OAAOL,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGJ,KAAK;EACzD;EACA,MAAMU,QAAQ,GAAGpB,KAAK,CAACqB,MAAM,CAAC,IAAI,CAAC;EACnCrB,KAAK,CAACsB,mBAAmB,CAACd,GAAG,EAAE,MAAMY,QAAQ,CAACG,OAAO,CAAC;EACtD;EACA,MAAMC,gBAAgB,GAAGpC,CAAC,IAAI;IAC5BuB,QAAQ,CAACE,KAAK,EAAEzB,CAAC,CAACqC,MAAM,CAACf,KAAK,CAAC;EACjC,CAAC;EACD;EACA,MAAMgB,aAAa,GAAGA,CAAA,KAAM;IAC1BxB,GAAG,CAAC,MAAM;MACR,IAAIyB,EAAE;MACN,MAAMC,QAAQ,GAAG,CAACD,EAAE,GAAGP,QAAQ,CAACG,OAAO,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,KAAK;MACtF,IAAIC,QAAQ,CAACC,aAAa,KAAKH,QAAQ,IAAIA,QAAQ,EAAE;QACnDA,QAAQ,CAACI,MAAM,CAAC,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACA,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;IACjC,MAAM;MACJC,GAAG;MACHC,OAAO;MACPC;IACF,CAAC,GAAGH,KAAK;IACT,IAAIC,GAAG,KAAK,WAAW,EAAE;MACvBvB,cAAc,CAACC,KAAK,GAAG,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIsB,GAAG,KAAK,YAAY,EAAE;MAC/BvB,cAAc,CAACC,KAAK,GAAG,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIsB,GAAG,KAAK,GAAG,KAAKC,OAAO,IAAIC,OAAO,CAAC,EAAE;MAC9CH,KAAK,CAACI,cAAc,CAAC,CAAC;IACxB;IACAZ,aAAa,CAAC,CAAC;EACjB,CAAC;EACD,MAAMa,eAAe,GAAGnD,CAAC,IAAI;IAC3B,IAAIA,CAAC,CAAC+C,GAAG,KAAK,WAAW,IAAI,CAACzB,KAAK,EAAE;MACnCE,cAAc,CAACC,KAAK,GAAG,CAAC,CAAC;IAC3B;IACAa,aAAa,CAAC,CAAC;EACjB,CAAC;EACD;EACA,OAAO,aAAa1B,KAAK,CAACwC,aAAa,CAAC,MAAM,EAAE;IAC9C/B,SAAS,EAAE,GAAGS,SAAS,gBAAgB;IACvCuB,IAAI,EAAE;EACR,CAAC,EAAE3B,IAAI,IAAIJ,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAKgC,SAAS,KAAK,aAAa1C,KAAK,CAACwC,aAAa,CAAC,MAAM,EAAE;IAC1F/B,SAAS,EAAE,GAAGS,SAAS,YAAY;IACnC,aAAa,EAAE;EACjB,CAAC,EAAEC,SAAS,CAAC,CAAC,EAAE,aAAanB,KAAK,CAACwC,aAAa,CAACpC,KAAK,EAAEb,MAAM,CAACoD,MAAM,CAAC;IACpE,YAAY,EAAE,aAAa9B,KAAK,GAAG,CAAC,EAAE;IACtC+B,IAAI,EAAE9B,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG;EACrC,CAAC,EAAEC,SAAS,EAAE;IACZP,GAAG,EAAEY,QAAQ;IACbV,KAAK,EAAEA,KAAK;IACZmC,OAAO,EAAErB,gBAAgB;IACzBsB,OAAO,EAAEpB,aAAa;IACtBqB,SAAS,EAAEd,iBAAiB;IAC5Be,OAAO,EAAET,eAAe;IACxBU,WAAW,EAAEvB,aAAa;IAC1BwB,SAAS,EAAExB,aAAa;IACxBjB,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAE;MAC/B,CAAC,GAAGS,SAAS,aAAa,GAAGJ;IAC/B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,eAAeT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}