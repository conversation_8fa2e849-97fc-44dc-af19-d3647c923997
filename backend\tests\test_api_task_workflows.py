"""
任务工作流API测试
"""
import pytest
import json
from unittest.mock import patch, MagicMock


class TestTaskWorkflowsAPI:
    """任务工作流API测试类"""
    
    def test_get_workflows_empty(self, client, db_session):
        """测试获取空工作流列表"""
        response = client.get('/task-workflows/workflows')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data'] == []
    
    def test_create_workflow_success(self, client, db_session, sample_workflow_data):
        """测试成功创建工作流"""
        response = client.post(
            '/task-workflows/workflows',
            data=json.dumps(sample_workflow_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'id' in data['data']
        assert data['data']['name'] == sample_workflow_data['name']
    
    def test_create_workflow_missing_name(self, client, db_session):
        """测试创建工作流缺少名称"""
        workflow_data = {
            'description': '测试工作流'
        }
        response = client.post(
            '/task-workflows/workflows',
            data=json.dumps(workflow_data),
            content_type='application/json'
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert '工作流名称不能为空' in data['message']
    
    def test_create_workflow_invalid_definition(self, client, db_session):
        """测试创建工作流定义无效"""
        workflow_data = {
            'name': '测试工作流',
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1'},
                    {'id': 'task2', 'name': '任务2'}
                ],
                'edges': [
                    {'source': 'task1', 'target': 'task2'},
                    {'source': 'task2', 'target': 'task1'}  # 循环依赖
                ]
            }
        }
        response = client.post(
            '/task-workflows/workflows',
            data=json.dumps(workflow_data),
            content_type='application/json'
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert '循环依赖' in data['message']
    
    def test_get_workflow_by_id(self, client, db_session, test_factory):
        """测试根据ID获取工作流"""
        workflow = test_factory.create_workflow(db_session, name='测试工作流')
        
        response = client.get(f'/task-workflows/workflows/{workflow.id}')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['id'] == workflow.id
        assert data['data']['name'] == '测试工作流'
    
    def test_get_workflow_not_found(self, client, db_session):
        """测试获取不存在的工作流"""
        response = client.get('/task-workflows/workflows/999')
        assert response.status_code == 404
        data = json.loads(response.data)
        assert data['code'] == 404
        assert '工作流不存在' in data['message']
    
    def test_update_workflow(self, client, db_session, test_factory):
        """测试更新工作流"""
        workflow = test_factory.create_workflow(db_session, name='原始名称')
        
        update_data = {
            'name': '更新后的名称',
            'description': '更新后的描述'
        }
        
        response = client.put(
            f'/task-workflows/workflows/{workflow.id}',
            data=json.dumps(update_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['name'] == '更新后的名称'
    
    def test_delete_workflow(self, client, db_session, test_factory):
        """测试删除工作流"""
        workflow = test_factory.create_workflow(db_session, name='待删除工作流')
        
        response = client.delete(f'/task-workflows/workflows/{workflow.id}')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        
        # 验证工作流已被删除
        response = client.get(f'/task-workflows/workflows/{workflow.id}')
        assert response.status_code == 404
    
    @patch('app.api.task_workflows.current_app')
    def test_execute_workflow(self, mock_app, client, db_session, test_factory):
        """测试执行工作流"""
        workflow = test_factory.create_workflow(db_session, name='可执行工作流')
        
        # 模拟调度器
        mock_scheduler = MagicMock()
        mock_app.scheduler = mock_scheduler
        
        response = client.post(f'/task-workflows/workflows/{workflow.id}/execute')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'execution_id' in data['data']
        
        # 验证调度器被调用
        mock_scheduler.execute_workflow.assert_called_once()
    
    def test_validate_workflow_valid(self, client, db_session):
        """测试验证有效工作流"""
        workflow_definition = {
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1'},
                    {'id': 'task2', 'name': '任务2'}
                ],
                'edges': [
                    {'source': 'task1', 'target': 'task2'}
                ]
            }
        }
        
        response = client.post(
            '/task-workflows/workflows/validate',
            data=json.dumps(workflow_definition),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['valid'] is True
    
    def test_validate_workflow_invalid(self, client, db_session):
        """测试验证无效工作流"""
        workflow_definition = {
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1'},
                    {'id': 'task2', 'name': '任务2'}
                ],
                'edges': [
                    {'source': 'task1', 'target': 'task2'},
                    {'source': 'task2', 'target': 'task1'}  # 循环依赖
                ]
            }
        }
        
        response = client.post(
            '/task-workflows/workflows/validate',
            data=json.dumps(workflow_definition),
            content_type='application/json'
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400
        assert data['data']['valid'] is False
    
    def test_get_execution_plan(self, client, db_session, test_factory):
        """测试获取执行计划"""
        workflow_definition = {
            'nodes': [
                {'id': 'task1', 'name': '任务1'},
                {'id': 'task2', 'name': '任务2'},
                {'id': 'task3', 'name': '任务3'}
            ],
            'edges': [
                {'source': 'task1', 'target': 'task2'},
                {'source': 'task1', 'target': 'task3'}
            ]
        }
        
        workflow = test_factory.create_workflow(
            db_session, 
            workflow_definition=workflow_definition
        )
        
        response = client.get(f'/task-workflows/workflows/{workflow.id}/execution-plan')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'execution_order' in data['data']
        assert data['data']['valid'] is True
    
    def test_validate_condition_valid(self, client, db_session):
        """测试验证有效条件"""
        condition_data = {
            'condition': 'status == "success"'
        }
        
        response = client.post(
            '/task-workflows/conditions/validate',
            data=json.dumps(condition_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['valid'] is True
    
    def test_validate_condition_invalid(self, client, db_session):
        """测试验证无效条件"""
        condition_data = {
            'condition': 'invalid syntax =='
        }
        
        response = client.post(
            '/task-workflows/conditions/validate',
            data=json.dumps(condition_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['valid'] is False
    
    def test_get_condition_help(self, client, db_session):
        """测试获取条件帮助信息"""
        response = client.get('/task-workflows/conditions/help')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert '预定义条件' in data['data']
        assert '变量' in data['data']
        assert '操作符' in data['data']
    
    def test_validate_parameters(self, client, db_session):
        """测试验证参数"""
        parameters_data = {
            'parameters': {
                'param1': {
                    'type': 'string',
                    'value': 'test_value',
                    'constraints': {'min_length': 3}
                },
                'param2': {
                    'type': 'integer',
                    'value': 42,
                    'constraints': {'min': 0, 'max': 100}
                }
            }
        }
        
        response = client.post(
            '/task-workflows/parameters/validate',
            data=json.dumps(parameters_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['all_valid'] is True
    
    def test_get_parameter_schema(self, client, db_session):
        """测试获取参数模式"""
        response = client.get('/task-workflows/parameters/schema')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'types' in data['data']
        assert 'constraints' in data['data']
        assert 'examples' in data['data']
    
    def test_api_error_handling(self, client, db_session):
        """测试API错误处理"""
        # 测试无效JSON
        response = client.post(
            '/task-workflows/workflows',
            data='invalid json',
            content_type='application/json'
        )
        assert response.status_code == 400
        
        # 测试空请求体
        response = client.post(
            '/task-workflows/workflows/validate',
            data='',
            content_type='application/json'
        )
        assert response.status_code == 400
