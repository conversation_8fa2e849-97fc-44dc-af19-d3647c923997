"""
测试配置文件
"""
import os
import tempfile
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app import create_app
from app.database import Base, get_db
from app.models import task, script, setting, notification, task_execution, task_workflow, script_version, system_setting, workflow_task

@pytest.fixture(scope='session')
def app():
    """创建测试应用实例"""
    # 创建临时数据库文件
    db_fd, db_path = tempfile.mkstemp(suffix='.db')
    
    # 测试配置
    test_config = {
        'TESTING': True,
        'DATABASE_URL': f'sqlite:///{db_path}',
        'SECRET_KEY': 'test-secret-key',
        'WTF_CSRF_ENABLED': False,
    }
    
    # 创建应用
    app = create_app(test_config)
    
    # 设置应用上下文
    with app.app_context():
        # 创建数据库表
        engine = create_engine(test_config['DATABASE_URL'])
        Base.metadata.create_all(engine)
        
        yield app
        
        # 清理
        Base.metadata.drop_all(engine)
        os.close(db_fd)
        os.unlink(db_path)

@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """创建CLI测试运行器"""
    return app.test_cli_runner()

@pytest.fixture
def db_session(app):
    """创建数据库会话"""
    with app.app_context():
        # 创建新的数据库引擎和会话
        engine = create_engine(app.config['DATABASE_URL'])
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 替换应用的数据库会话
        def get_test_db():
            try:
                yield session
            finally:
                session.close()
        
        app.dependency_overrides = {get_db: get_test_db}
        
        yield session
        
        # 清理
        session.rollback()
        session.close()

@pytest.fixture
def sample_task_data():
    """示例任务数据"""
    return {
        'name': '测试任务',
        'description': '这是一个测试任务',
        'cron_expression': '0 0 * * *',
        'is_active': True
    }

@pytest.fixture
def sample_script_data():
    """示例脚本数据"""
    return {
        'name': '测试脚本',
        'description': '这是一个测试脚本',
        'type': 'python',
        'content': 'print("Hello, World!")',
        'version': '1.0.0'
    }

@pytest.fixture
def sample_workflow_data():
    """示例工作流数据"""
    return {
        'name': '测试工作流',
        'description': '这是一个测试工作流',
        'workflow_definition': {
            'nodes': [
                {
                    'id': 'task1',
                    'name': '任务1',
                    'task_id': 1,
                    'execution_type': 'serial'
                },
                {
                    'id': 'task2',
                    'name': '任务2',
                    'task_id': 2,
                    'execution_type': 'parallel'
                }
            ],
            'edges': [
                {
                    'source': 'task1',
                    'target': 'task2',
                    'condition': 'success'
                }
            ]
        }
    }

@pytest.fixture
def auth_headers():
    """认证头信息"""
    return {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
    }

@pytest.fixture(autouse=True)
def clean_database(db_session):
    """自动清理数据库"""
    yield
    # 测试后清理所有表
    for table in reversed(Base.metadata.sorted_tables):
        db_session.execute(table.delete())
    db_session.commit()

class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    def create_task(session, **kwargs):
        """创建测试任务"""
        from app.models.task import Task
        
        default_data = {
            'name': '测试任务',
            'description': '测试描述',
            'cron_expression': '0 0 * * *',
            'is_active': True
        }
        default_data.update(kwargs)
        
        task = Task(**default_data)
        session.add(task)
        session.commit()
        session.refresh(task)
        return task
    
    @staticmethod
    def create_script(session, **kwargs):
        """创建测试脚本"""
        from app.models.script import Script
        
        default_data = {
            'name': '测试脚本',
            'description': '测试描述',
            'type': 'python',
            'content': 'print("test")',
            'version': '1.0.0'
        }
        default_data.update(kwargs)
        
        script = Script(**default_data)
        session.add(script)
        session.commit()
        session.refresh(script)
        return script
    
    @staticmethod
    def create_workflow(session, **kwargs):
        """创建测试工作流"""
        from app.models.task_workflow import TaskWorkflow
        
        default_data = {
            'name': '测试工作流',
            'description': '测试描述',
            'workflow_definition': {
                'nodes': [],
                'edges': []
            }
        }
        default_data.update(kwargs)
        
        workflow = TaskWorkflow(**default_data)
        session.add(workflow)
        session.commit()
        session.refresh(workflow)
        return workflow

@pytest.fixture
def test_factory():
    """测试数据工厂实例"""
    return TestDataFactory
