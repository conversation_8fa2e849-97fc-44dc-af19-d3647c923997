import os
import subprocess
import signal
import platform
from functools import wraps
from flask import abort

# Windows兼容性处理
try:
    import resource
    HAS_RESOURCE = True
except ImportError:
    # Windows系统没有resource模块
    HAS_RESOURCE = False
    resource = None

# 检查signal.alarm是否可用（Windows上不可用）
HAS_ALARM = hasattr(signal, 'alarm')

def limit_execution_time(seconds):
    """限制脚本执行时间的装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not HAS_ALARM:
                # Windows系统不支持signal.alarm，使用替代方案或跳过
                print(f"Warning: Execution time limiting not supported on {platform.system()}")
                return func(*args, **kwargs)

            # 设置执行时间限制（仅在支持的系统上）
            signal.alarm(seconds)
            try:
                result = func(*args, **kwargs)
                signal.alarm(0)  # 取消时间限制
                return result
            except Exception as e:
                signal.alarm(0)  # 取消时间限制
                raise e
        return wrapper
    return decorator

def limit_memory_usage(max_memory_mb):
    """限制脚本内存使用的函数"""
    if not HAS_RESOURCE:
        # Windows系统不支持resource模块，使用替代方案或跳过
        print(f"Warning: Memory limiting not supported on {platform.system()}")
        return

    # 设置内存限制（仅在支持的系统上）
    resource.setrlimit(resource.RLIMIT_AS, (max_memory_mb * 1024 * 1024, max_memory_mb * 1024 * 1024))

def validate_file_path(file_path, allowed_dirs):
    """验证文件路径是否在允许的目录范围内"""
    # 获取文件的绝对路径
    abs_path = os.path.abspath(file_path)
    
    # 检查是否在允许的目录中
    for allowed_dir in allowed_dirs:
        abs_allowed_dir = os.path.abspath(allowed_dir)
        if abs_path.startswith(abs_allowed_dir):
            return True
    
    return False

def run_script_in_sandbox(script_content, script_type, timeout=3600, memory_limit_mb=256):
    """在沙箱环境中运行脚本"""
    try:
        # 限制内存使用
        limit_memory_usage(memory_limit_mb)
        
        if script_type == 'shell':
            # 使用subprocess运行shell脚本，并限制执行时间
            result = subprocess.run(
                script_content,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.stdout, result.stderr if result.stderr else None
        elif script_type == 'python':
            # 对于Python脚本，可以将其写入临时文件并执行
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                temp_file = f.name
            
            try:
                # 使用subprocess运行Python脚本
                result = subprocess.run(
                    ['python', temp_file],
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                return result.stdout, result.stderr if result.stderr else None
            finally:
                os.unlink(temp_file)
        else:
            raise Exception(f"不支持的脚本类型: {script_type}")
    except subprocess.TimeoutExpired as e:
        return None, f"脚本执行超时: {str(e)}"
    except Exception as e:
        return None, f"脚本执行错误: {str(e)}"