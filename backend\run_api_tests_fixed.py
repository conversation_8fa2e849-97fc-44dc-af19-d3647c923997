#!/usr/bin/env python3
"""
修复后的API测试运行器
"""
import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def create_simple_api_test():
    """创建简化的API测试"""
    print("🔧 创建简化的API测试...")
    
    test_content = '''"""
简化的API测试
"""
import pytest
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.mock_app import create_mock_app


@pytest.fixture
def app():
    """创建测试应用"""
    return create_mock_app()


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()


@pytest.fixture
def auth_headers():
    """认证头部"""
    return {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
    }


@pytest.mark.api
class TestTaskAPI:
    """任务API测试"""
    
    def test_get_tasks(self, client):
        """测试获取任务列表"""
        response = client.get('/api/tasks')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_task(self, client, auth_headers):
        """测试创建任务"""
        task_data = {
            'name': '测试任务',
            'description': '这是一个测试任务',
            'cron_expression': '0 0 * * *',
            'script_id': 1
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
        assert 'id' in data['data']
    
    def test_create_task_missing_name(self, client, auth_headers):
        """测试创建任务缺少名称"""
        task_data = {
            'description': '缺少名称的任务',
            'cron_expression': '0 0 * * *'
        }
        
        response = client.post('/api/tasks',
                             data=json.dumps(task_data),
                             headers=auth_headers)
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert data['code'] == 400


@pytest.mark.api
class TestScriptAPI:
    """脚本API测试"""
    
    def test_get_scripts(self, client):
        """测试获取脚本列表"""
        response = client.get('/api/scripts')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_script(self, client, auth_headers):
        """测试创建脚本"""
        script_data = {
            'name': '测试脚本',
            'type': 'python',
            'content': 'print("Hello, World!")',
            'version': '1.0.0'
        }
        
        response = client.post('/api/scripts',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
    
    def test_validate_script(self, client, auth_headers):
        """测试脚本验证"""
        script_data = {
            'type': 'python',
            'content': 'print("Valid code")'
        }
        
        response = client.post('/api/scripts/validate',
                             data=json.dumps(script_data),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True


@pytest.mark.api
class TestWorkflowAPI:
    """工作流API测试"""
    
    def test_get_workflows(self, client):
        """测试获取工作流列表"""
        response = client.get('/api/workflows')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert isinstance(data['data'], list)
    
    def test_create_workflow(self, client, auth_headers):
        """测试创建工作流"""
        workflow_data = {
            'name': '测试工作流',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'task1',
                        'name': '任务1',
                        'task_id': 1,
                        'execution_type': 'serial'
                    }
                ],
                'edges': []
            }
        }
        
        response = client.post('/api/workflows',
                             data=json.dumps(workflow_data),
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['code'] == 201
    
    def test_validate_workflow(self, client, auth_headers):
        """测试工作流验证"""
        workflow_def = {
            'workflow_definition': {
                'nodes': [
                    {'id': 'task1', 'name': '任务1', 'task_id': 1}
                ],
                'edges': []
            }
        }
        
        response = client.post('/api/workflows/validate',
                             data=json.dumps(workflow_def),
                             headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True


@pytest.mark.performance
class TestAPIPerformance:
    """API性能测试"""
    
    def test_response_time(self, client):
        """测试响应时间"""
        import time
        
        start_time = time.time()
        response = client.get('/api/tasks')
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response.status_code == 200
        assert response_time < 1.0  # 响应时间小于1秒
'''
    
    test_file = Path(__file__).parent / "tests" / "test_api_fixed.py"
    test_file.write_text(test_content, encoding='utf-8')
    print(f"  ✅ 创建了简化API测试: {test_file}")
    
    return True


def run_fixed_api_tests(coverage=False, performance=False):
    """运行修复后的API测试"""
    print("🚀 运行修复后的API测试")
    print("=" * 60)
    
    # 构建pytest命令
    cmd = [sys.executable, "-m", "pytest"]
    
    # 添加覆盖率选项
    if coverage:
        cmd.extend([
            "--cov=tests.mock_app",
            "--cov-report=html:htmlcov_api_fixed",
            "--cov-report=term-missing"
        ])
    
    # 添加测试文件
    cmd.append("tests/test_api_fixed.py")
    
    # 添加标记过滤
    if performance:
        cmd.extend(["-m", "performance"])
    else:
        cmd.extend(["-m", "api"])
    
    # 添加其他选项
    cmd.extend([
        "-v",
        "--tb=short"
    ])
    
    print("🔧 运行命令:")
    print(" ".join(cmd))
    print()
    
    try:
        result = subprocess.run(cmd, cwd=Path(__file__).parent, timeout=120)
        
        if result.returncode == 0:
            print("\n✅ API测试成功完成！")
            if coverage:
                print("📊 覆盖率报告已生成: htmlcov_api_fixed/index.html")
            return True
        else:
            print("\n❌ API测试失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ API测试超时")
        return False
    except Exception as e:
        print(f"💥 运行API测试时出错: {e}")
        return False


def validate_api_endpoints():
    """验证API端点"""
    print("🔍 验证API端点...")
    
    from tests.mock_app import create_mock_app
    
    app = create_mock_app()
    client = app.test_client()
    
    endpoints = [
        ('GET', '/api/tasks'),
        ('GET', '/api/scripts'),
        ('GET', '/api/workflows'),
    ]
    
    passed = 0
    total = len(endpoints)
    
    for method, endpoint in endpoints:
        try:
            if method == 'GET':
                response = client.get(endpoint)
            elif method == 'POST':
                response = client.post(endpoint, json={})
            
            if response.status_code in [200, 400, 401]:  # 预期的状态码
                print(f"  ✅ {method} {endpoint} - 状态码: {response.status_code}")
                passed += 1
            else:
                print(f"  ❌ {method} {endpoint} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"  💥 {method} {endpoint} - 错误: {e}")
    
    print(f"\n📊 端点验证结果: {passed}/{total} 通过")
    return passed >= total * 0.8


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="修复后的API测试运行器")
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--validate", action="store_true", help="验证API端点")
    parser.add_argument("--create", action="store_true", help="创建简化测试")
    
    args = parser.parse_args()
    
    if args.create:
        success = create_simple_api_test()
        if success:
            print("✅ 简化API测试创建成功")
        sys.exit(0 if success else 1)
    
    if args.validate:
        success = validate_api_endpoints()
        sys.exit(0 if success else 1)
    
    # 首先创建简化测试
    create_simple_api_test()
    
    # 运行API测试
    success = run_fixed_api_tests(
        coverage=args.coverage,
        performance=args.performance
    )
    
    if success:
        print("\n🎉 API测试完成！")
        print("\n💡 后续步骤:")
        print("1. 查看覆盖率报告: htmlcov_api_fixed/index.html")
        print("2. 运行性能测试: python run_api_tests_fixed.py --performance")
        print("3. 验证端点: python run_api_tests_fixed.py --validate")
    else:
        print("\n⚠️  API测试未完全通过")
        print("\n💡 建议:")
        print("1. 检查mock应用配置")
        print("2. 验证API端点: python run_api_tests_fixed.py --validate")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
