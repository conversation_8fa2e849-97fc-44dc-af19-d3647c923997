# 测试使用指南

## 快速开始

### 1. 基本功能验证
```bash
python test_simple.py
```
这个脚本会验证所有核心模块是否正常工作。

### 2. 功能演示
```bash
python test_demo.py
```
这个脚本会演示所有测试功能的工作原理。

### 3. 完整测试套件
```bash
# 使用修复后的测试脚本
python run_tests.py --coverage

# 或者使用基础unittest版本
python run_basic_tests.py
```

## 测试脚本说明

### run_tests.py (主要测试脚本)
支持的参数：
- `--coverage`: 生成覆盖率报告
- `--no-coverage`: 禁用覆盖率报告
- `--verbose` / `-v`: 详细输出
- `--quiet`: 静默模式
- `--file` / `-f`: 运行特定测试文件
- `--test` / `-t`: 运行特定测试方法

示例：
```bash
# 运行所有测试并生成覆盖率报告
python run_tests.py --coverage

# 运行特定测试文件
python run_tests.py --file tests/test_dependency_manager.py

# 运行特定测试方法
python run_tests.py --test test_add_dependency
```

### test_simple.py (基本验证)
快速验证所有核心模块是否可以正常导入和基本功能是否工作。

### test_demo.py (功能演示)
详细演示每个模块的测试功能，包括：
- 依赖管理器的循环检测和拓扑排序
- 条件解析器的表达式评估
- 参数管理器的类型转换和验证
- 执行管理器的并发控制

### run_basic_tests.py (unittest版本)
使用Python标准库unittest模块的测试版本，不依赖外部测试框架。

## 测试文件结构

```
backend/
├── tests/                          # 测试目录
│   ├── conftest.py                 # pytest配置和fixtures
│   ├── test_api_task_workflows.py  # API测试
│   ├── test_dependency_manager.py  # 依赖管理器测试
│   ├── test_condition_parser.py    # 条件解析器测试
│   ├── test_parameter_manager.py   # 参数管理器测试
│   ├── test_execution_manager.py   # 执行管理器测试
│   ├── test_models.py              # 数据库模型测试
│   ├── test_integration.py         # 集成测试
│   └── README.md                   # 详细测试文档
├── config/
│   └── test_config.py              # 测试环境配置
├── run_tests.py                    # 主要测试脚本
├── run_basic_tests.py              # 基础unittest测试
├── test_simple.py                  # 简单验证脚本
├── test_demo.py                    # 功能演示脚本
└── pytest.ini                     # pytest配置文件
```

## 前端测试

```bash
cd frontend

# 安装依赖
npm install

# 运行测试
npm test

# 生成覆盖率报告
npm run test:coverage

# 使用自定义脚本
node run_tests.js --all
```

## 测试覆盖的功能

### 后端测试
✅ **API端点测试**
- 工作流CRUD操作
- 工作流验证和执行
- 条件验证API
- 参数验证API

✅ **核心工具类测试**
- 依赖管理器：循环检测、拓扑排序、执行计划
- 条件解析器：预定义条件、自定义表达式、安全评估
- 参数管理器：类型转换、验证、引用解析、输出提取
- 执行管理器：并发控制、状态管理、回调机制

✅ **数据库模型测试**
- 任务、脚本、工作流模型的CRUD操作
- 模型间的关联关系
- 数据完整性约束

✅ **集成测试**
- 完整工作流生命周期
- 条件分支处理
- 并行执行验证
- 性能和并发测试

### 前端测试
✅ **组件测试**
- TaskWorkflow页面组件
- WorkflowDesigner设计器组件
- 用户交互和状态管理
- API调用和错误处理

## 故障排除

### 常见问题

1. **模块导入错误**
   - 确保在backend目录下运行测试
   - 检查Python路径设置

2. **pytest未安装**
   - 运行 `pip install pytest pytest-cov`
   - 或使用 `run_basic_tests.py` 替代

3. **数据库连接错误**
   - 测试使用内存数据库，通常不会有连接问题
   - 检查SQLAlchemy是否正确安装

4. **前端测试失败**
   - 确保Node.js和npm已安装
   - 运行 `npm install` 安装依赖

### 调试技巧

1. **运行单个测试**
   ```bash
   python test_simple.py  # 基本验证
   python test_demo.py    # 功能演示
   ```

2. **查看详细错误**
   ```bash
   python run_tests.py --verbose
   ```

3. **测试特定功能**
   ```bash
   python run_tests.py --test dependency_manager
   ```

## 持续集成

测试脚本支持CI/CD集成：

```yaml
# GitHub Actions示例
- name: Run Backend Tests
  run: |
    cd backend
    python run_tests.py --coverage --quiet

- name: Run Frontend Tests  
  run: |
    cd frontend
    node run_tests.js --all
```

## 测试最佳实践

1. **运行测试前**
   - 确保所有依赖已安装
   - 检查代码语法错误

2. **测试开发**
   - 新功能必须包含测试
   - 保持测试独立性
   - 使用描述性测试名称

3. **覆盖率目标**
   - 整体覆盖率 ≥ 90%
   - 核心业务逻辑 100% 覆盖
   - 定期检查覆盖率报告

## 联系支持

如果遇到测试相关问题：
1. 查看 `tests/README.md` 详细文档
2. 运行基本验证脚本确认环境
3. 检查错误日志和堆栈跟踪
