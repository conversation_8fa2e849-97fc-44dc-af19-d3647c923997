"""
数据库模型测试
"""
import pytest
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from app.models.task import Task
from app.models.script import Script
from app.models.task_execution import TaskExecution
from app.models.task_workflow import TaskWorkflow, TaskWorkflowExecution
from app.models.workflow_task import WorkflowTask


class TestTaskModel:
    """任务模型测试"""
    
    def test_create_task(self, db_session):
        """测试创建任务"""
        task = Task(
            name="测试任务",
            description="这是一个测试任务",
            cron_expression="0 0 * * *",
            is_active=True
        )
        
        db_session.add(task)
        db_session.commit()
        
        assert task.id is not None
        assert task.name == "测试任务"
        assert task.is_active is True
        assert task.created_at is not None
    
    def test_task_name_required(self, db_session):
        """测试任务名称必填"""
        task = Task(
            description="测试任务",
            cron_expression="0 0 * * *"
        )
        
        db_session.add(task)
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_task_script_relationship(self, db_session):
        """测试任务与脚本的关联关系"""
        script = Script(
            name="测试脚本",
            type="python",
            content="print('hello')",
            version="1.0.0"
        )
        
        task = Task(
            name="测试任务",
            cron_expression="0 0 * * *",
            script=script
        )
        
        db_session.add(task)
        db_session.commit()
        
        assert task.script is not None
        assert task.script.name == "测试脚本"
        assert script.tasks[0] == task
    
    def test_task_executions_relationship(self, db_session):
        """测试任务与执行记录的关联关系"""
        task = Task(
            name="测试任务",
            cron_expression="0 0 * * *"
        )
        
        execution = TaskExecution(
            task=task,
            status="success",
            start_time=datetime.utcnow()
        )
        
        db_session.add(task)
        db_session.add(execution)
        db_session.commit()
        
        assert len(task.executions) == 1
        assert task.executions[0].status == "success"
        assert execution.task == task


class TestScriptModel:
    """脚本模型测试"""
    
    def test_create_script(self, db_session):
        """测试创建脚本"""
        script = Script(
            name="测试脚本",
            description="这是一个测试脚本",
            type="python",
            content="print('hello world')",
            version="1.0.0"
        )
        
        db_session.add(script)
        db_session.commit()
        
        assert script.id is not None
        assert script.name == "测试脚本"
        assert script.type == "python"
        assert script.created_at is not None
    
    def test_script_name_required(self, db_session):
        """测试脚本名称必填"""
        script = Script(
            type="python",
            content="print('hello')"
        )
        
        db_session.add(script)
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_script_type_required(self, db_session):
        """测试脚本类型必填"""
        script = Script(
            name="测试脚本",
            content="print('hello')"
        )
        
        db_session.add(script)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestTaskExecutionModel:
    """任务执行记录模型测试"""
    
    def test_create_execution(self, db_session):
        """测试创建执行记录"""
        task = Task(
            name="测试任务",
            cron_expression="0 0 * * *"
        )
        
        execution = TaskExecution(
            task=task,
            status="running",
            start_time=datetime.utcnow(),
            input_parameters={"param1": "value1"},
            output_parameters={"result": "success"}
        )
        
        db_session.add(task)
        db_session.add(execution)
        db_session.commit()
        
        assert execution.id is not None
        assert execution.status == "running"
        assert execution.input_parameters == {"param1": "value1"}
        assert execution.output_parameters == {"result": "success"}
        assert execution.return_code == 0  # 默认值
    
    def test_execution_duration_calculation(self, db_session):
        """测试执行时长计算"""
        task = Task(
            name="测试任务",
            cron_expression="0 0 * * *"
        )
        
        start_time = datetime.utcnow()
        execution = TaskExecution(
            task=task,
            status="success",
            start_time=start_time,
            duration=120.5
        )
        
        db_session.add(task)
        db_session.add(execution)
        db_session.commit()
        
        assert execution.duration == 120.5
    
    def test_execution_task_required(self, db_session):
        """测试执行记录必须关联任务"""
        execution = TaskExecution(
            status="running",
            start_time=datetime.utcnow()
        )
        
        db_session.add(execution)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestTaskWorkflowModel:
    """任务工作流模型测试"""
    
    def test_create_workflow(self, db_session):
        """测试创建工作流"""
        workflow_definition = {
            "nodes": [
                {"id": "task1", "name": "任务1"},
                {"id": "task2", "name": "任务2"}
            ],
            "edges": [
                {"source": "task1", "target": "task2"}
            ]
        }
        
        workflow = TaskWorkflow(
            name="测试工作流",
            description="这是一个测试工作流",
            workflow_definition=workflow_definition
        )
        
        db_session.add(workflow)
        db_session.commit()
        
        assert workflow.id is not None
        assert workflow.name == "测试工作流"
        assert workflow.workflow_definition == workflow_definition
        assert workflow.created_at is not None
    
    def test_workflow_name_required(self, db_session):
        """测试工作流名称必填"""
        workflow = TaskWorkflow(
            description="测试工作流"
        )
        
        db_session.add(workflow)
        with pytest.raises(IntegrityError):
            db_session.commit()
    
    def test_workflow_executions_relationship(self, db_session):
        """测试工作流与执行记录的关联关系"""
        workflow = TaskWorkflow(
            name="测试工作流",
            workflow_definition={"nodes": [], "edges": []}
        )
        
        execution = TaskWorkflowExecution(
            workflow=workflow,
            status="running",
            start_time=datetime.utcnow()
        )
        
        db_session.add(workflow)
        db_session.add(execution)
        db_session.commit()
        
        assert len(workflow.executions) == 1
        assert workflow.executions[0].status == "running"
        assert execution.workflow == workflow


class TestTaskWorkflowExecutionModel:
    """工作流执行记录模型测试"""
    
    def test_create_workflow_execution(self, db_session):
        """测试创建工作流执行记录"""
        workflow = TaskWorkflow(
            name="测试工作流",
            workflow_definition={"nodes": [], "edges": []}
        )
        
        execution = TaskWorkflowExecution(
            workflow=workflow,
            status="success",
            start_time=datetime.utcnow(),
            output="工作流执行完成"
        )
        
        db_session.add(workflow)
        db_session.add(execution)
        db_session.commit()
        
        assert execution.id is not None
        assert execution.status == "success"
        assert execution.output == "工作流执行完成"
    
    def test_workflow_execution_workflow_required(self, db_session):
        """测试工作流执行记录必须关联工作流"""
        execution = TaskWorkflowExecution(
            status="running",
            start_time=datetime.utcnow()
        )
        
        db_session.add(execution)
        with pytest.raises(IntegrityError):
            db_session.commit()


class TestWorkflowTaskModel:
    """工作流任务关系模型测试"""
    
    def test_create_workflow_task(self, db_session):
        """测试创建工作流任务关系"""
        workflow = TaskWorkflow(
            name="测试工作流",
            workflow_definition={"nodes": [], "edges": []}
        )
        
        task = Task(
            name="测试任务",
            cron_expression="0 0 * * *"
        )
        
        dependency_task = Task(
            name="依赖任务",
            cron_expression="0 0 * * *"
        )
        
        workflow_task = WorkflowTask(
            workflow=workflow,
            task=task,
            dependency_task=dependency_task,
            execution_order=1,
            condition_expression="success",
            node_id="task1",
            node_config={"timeout": 3600}
        )
        
        db_session.add(workflow)
        db_session.add(task)
        db_session.add(dependency_task)
        db_session.add(workflow_task)
        db_session.commit()
        
        assert workflow_task.id is not None
        assert workflow_task.execution_order == 1
        assert workflow_task.condition_expression == "success"
        assert workflow_task.node_config == {"timeout": 3600}
    
    def test_workflow_task_relationships(self, db_session):
        """测试工作流任务关系的关联"""
        workflow = TaskWorkflow(
            name="测试工作流",
            workflow_definition={"nodes": [], "edges": []}
        )
        
        task = Task(
            name="测试任务",
            cron_expression="0 0 * * *"
        )
        
        workflow_task = WorkflowTask(
            workflow=workflow,
            task=task,
            node_id="task1"
        )
        
        db_session.add(workflow)
        db_session.add(task)
        db_session.add(workflow_task)
        db_session.commit()
        
        assert workflow_task.workflow == workflow
        assert workflow_task.task == task
        assert workflow in [wt.workflow for wt in task.workflow_tasks]


class TestModelIntegration:
    """模型集成测试"""
    
    def test_complete_workflow_setup(self, db_session):
        """测试完整的工作流设置"""
        # 创建脚本
        script1 = Script(
            name="脚本1",
            type="python",
            content="print('task1')",
            version="1.0.0"
        )
        
        script2 = Script(
            name="脚本2",
            type="python",
            content="print('task2')",
            version="1.0.0"
        )
        
        # 创建任务
        task1 = Task(
            name="任务1",
            cron_expression="0 0 * * *",
            script=script1
        )
        
        task2 = Task(
            name="任务2",
            cron_expression="0 0 * * *",
            script=script2
        )
        
        # 创建工作流
        workflow = TaskWorkflow(
            name="完整工作流",
            workflow_definition={
                "nodes": [
                    {"id": "task1", "name": "任务1", "task_id": 1},
                    {"id": "task2", "name": "任务2", "task_id": 2}
                ],
                "edges": [
                    {"source": "task1", "target": "task2"}
                ]
            }
        )
        
        # 创建工作流任务关系
        workflow_task1 = WorkflowTask(
            workflow=workflow,
            task=task1,
            node_id="task1",
            execution_order=1
        )
        
        workflow_task2 = WorkflowTask(
            workflow=workflow,
            task=task2,
            dependency_task=task1,
            node_id="task2",
            execution_order=2,
            condition_expression="success"
        )
        
        # 保存所有对象
        db_session.add_all([script1, script2, task1, task2, workflow, workflow_task1, workflow_task2])
        db_session.commit()
        
        # 验证关系
        assert len(workflow.workflow_tasks) == 2
        assert task1.script == script1
        assert task2.script == script2
        assert workflow_task2.dependency_task == task1
        
        # 创建执行记录
        task_execution = TaskExecution(
            task=task1,
            status="success",
            start_time=datetime.utcnow()
        )
        
        workflow_execution = TaskWorkflowExecution(
            workflow=workflow,
            status="running",
            start_time=datetime.utcnow()
        )
        
        db_session.add_all([task_execution, workflow_execution])
        db_session.commit()
        
        # 验证执行记录
        assert len(task1.executions) == 1
        assert len(workflow.executions) == 1
        assert task_execution.task == task1
        assert workflow_execution.workflow == workflow
