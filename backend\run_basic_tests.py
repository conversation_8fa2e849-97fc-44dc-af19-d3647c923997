#!/usr/bin/env python3
"""
基础测试运行脚本（使用unittest）
"""
import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestDependencyManager(unittest.TestCase):
    """依赖管理器测试"""
    
    def setUp(self):
        from app.utils.dependency_manager import DependencyManager
        self.dm = DependencyManager()
    
    def test_add_dependency(self):
        """测试添加依赖关系"""
        self.dm.add_dependency('task2', 'task1')
        
        self.assertIn('task1', self.dm.graph['task2'])
        self.assertIn('task2', self.dm.reverse_graph['task1'])
    
    def test_has_cycle_no_cycle(self):
        """测试无循环依赖"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        
        self.assertFalse(self.dm.has_cycle())
    
    def test_has_cycle_with_cycle(self):
        """测试有循环依赖"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        self.dm.add_dependency('task1', 'task3')
        
        self.assertTrue(self.dm.has_cycle())
    
    def test_topological_sort(self):
        """测试拓扑排序"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        
        order = self.dm.topological_sort()
        self.assertEqual(len(order), 3)
        self.assertEqual(order[0], ['task1'])
        self.assertEqual(order[1], ['task2'])
        self.assertEqual(order[2], ['task3'])


class TestConditionParser(unittest.TestCase):
    """条件解析器测试"""
    
    def setUp(self):
        from app.utils.condition_parser import ConditionParser
        self.parser = ConditionParser()
    
    def test_predefined_conditions(self):
        """测试预定义条件"""
        test_cases = [
            ('success', {'status': 'success'}, True),
            ('success', {'status': 'failed'}, False),
            ('failed', {'status': 'failed'}, True),
            ('failed', {'status': 'success'}, False),
            ('always', {'status': 'success'}, True),
            ('always', {'status': 'failed'}, True),
        ]
        
        for condition, context, expected in test_cases:
            with self.subTest(condition=condition, context=context):
                result = self.parser.parse_condition(condition, context)
                self.assertEqual(result, expected)
    
    def test_simple_comparisons(self):
        """测试简单比较条件"""
        test_cases = [
            ('status == "success"', {'status': 'success'}, True),
            ('status == "success"', {'status': 'failed'}, False),
            ('duration > 60', {'duration': 120}, True),
            ('duration > 60', {'duration': 30}, False),
        ]
        
        for condition, context, expected in test_cases:
            with self.subTest(condition=condition, context=context):
                result = self.parser.parse_condition(condition, context)
                self.assertEqual(result, expected)
    
    def test_validate_condition(self):
        """测试条件验证"""
        valid_conditions = [
            'success',
            'status == "success"',
            'duration > 60',
        ]
        
        for condition in valid_conditions:
            with self.subTest(condition=condition):
                is_valid, message = self.parser.validate_condition(condition)
                self.assertTrue(is_valid, f"条件 '{condition}' 应该是有效的: {message}")


class TestParameterManager(unittest.TestCase):
    """参数管理器测试"""
    
    def setUp(self):
        from app.utils.parameter_manager import ParameterManager, ParameterType
        self.manager = ParameterManager()
        self.ParameterType = ParameterType
    
    def test_convert_parameter_string(self):
        """测试字符串类型转换"""
        result = self.manager.convert_parameter("hello", self.ParameterType.STRING)
        self.assertEqual(result, "hello")
        
        result = self.manager.convert_parameter(123, self.ParameterType.STRING)
        self.assertEqual(result, "123")
    
    def test_convert_parameter_integer(self):
        """测试整数类型转换"""
        result = self.manager.convert_parameter("42", self.ParameterType.INTEGER)
        self.assertEqual(result, 42)
        
        result = self.manager.convert_parameter(42.7, self.ParameterType.INTEGER)
        self.assertEqual(result, 42)
    
    def test_convert_parameter_boolean(self):
        """测试布尔类型转换"""
        true_values = ["true", "True", "1", "yes", True, 1]
        false_values = ["false", "False", "0", "no", False, 0, ""]
        
        for value in true_values:
            with self.subTest(value=value):
                result = self.manager.convert_parameter(value, self.ParameterType.BOOLEAN)
                self.assertTrue(result)
        
        for value in false_values:
            with self.subTest(value=value):
                result = self.manager.convert_parameter(value, self.ParameterType.BOOLEAN)
                self.assertFalse(result)
    
    def test_validate_parameter(self):
        """测试参数验证"""
        # 有效参数
        is_valid, message = self.manager.validate_parameter(
            "test", self.ParameterType.STRING, {'min_length': 3}
        )
        self.assertTrue(is_valid)
        
        # 无效参数
        is_valid, message = self.manager.validate_parameter(
            "hi", self.ParameterType.STRING, {'min_length': 3}
        )
        self.assertFalse(is_valid)


class TestExecutionManager(unittest.TestCase):
    """执行管理器测试"""
    
    def setUp(self):
        from app.utils.execution_manager import ExecutionManager, TaskNode
        self.manager = ExecutionManager(max_workers=2)
        self.TaskNode = TaskNode
    
    def tearDown(self):
        self.manager.cleanup()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.manager.max_workers, 2)
        self.assertEqual(len(self.manager.task_results), 0)
        self.assertEqual(len(self.manager.task_status), 0)
    
    def test_task_node_creation(self):
        """测试任务节点创建"""
        node = self.TaskNode(id="task1", name="任务1", task_id=1)
        
        self.assertEqual(node.id, "task1")
        self.assertEqual(node.name, "任务1")
        self.assertEqual(node.task_id, 1)


def run_tests():
    """运行所有测试"""
    print("🚀 运行基础单元测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestDependencyManager,
        TestConditionParser,
        TestParameterManager,
        TestExecutionManager
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
        print(f"📊 运行了 {result.testsRun} 个测试")
        return True
    else:
        print("❌ 部分测试失败")
        print(f"📊 运行了 {result.testsRun} 个测试")
        print(f"❌ 失败: {len(result.failures)}")
        print(f"💥 错误: {len(result.errors)}")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
