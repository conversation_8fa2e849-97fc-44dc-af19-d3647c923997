{"ast": null, "code": "import { FastColor } from '@ant-design/fast-color';\nexport const getAlphaColor = (baseColor, alpha) => new FastColor(baseColor).setA(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new FastColor(baseColor);\n  return instance.lighten(brightness).toHexString();\n};", "map": {"version": 3, "names": ["FastColor", "getAlphaColor", "baseColor", "alpha", "setA", "toRgbString", "getSolidColor", "brightness", "instance", "lighten", "toHexString"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/theme/themes/dark/colorAlgorithm.js"], "sourcesContent": ["import { FastColor } from '@ant-design/fast-color';\nexport const getAlphaColor = (baseColor, alpha) => new FastColor(baseColor).setA(alpha).toRgbString();\nexport const getSolidColor = (baseColor, brightness) => {\n  const instance = new FastColor(baseColor);\n  return instance.lighten(brightness).toHexString();\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,OAAO,MAAMC,aAAa,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK,IAAIH,SAAS,CAACE,SAAS,CAAC,CAACE,IAAI,CAACD,KAAK,CAAC,CAACE,WAAW,CAAC,CAAC;AACrG,OAAO,MAAMC,aAAa,GAAGA,CAACJ,SAAS,EAAEK,UAAU,KAAK;EACtD,MAAMC,QAAQ,GAAG,IAAIR,SAAS,CAACE,SAAS,CAAC;EACzC,OAAOM,QAAQ,CAACC,OAAO,CAACF,UAAU,CAAC,CAACG,WAAW,CAAC,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}