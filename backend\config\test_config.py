"""
测试环境配置
"""
import os
import tempfile
from pathlib import Path

# 基础配置
class TestConfig:
    """测试环境配置类"""
    
    # 基本设置
    TESTING = True
    DEBUG = True
    SECRET_KEY = 'test-secret-key-for-testing-only'
    
    # 数据库配置
    # 使用内存数据库进行测试
    DATABASE_URL = 'sqlite:///:memory:'
    
    # 或者使用临时文件数据库
    # temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    # DATABASE_URL = f'sqlite:///{temp_db.name}'
    
    # 禁用CSRF保护
    WTF_CSRF_ENABLED = False
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    LOG_TO_FILE = False
    
    # 安全配置
    SECURITY_ENABLED = False
    
    # 调度器配置
    SCHEDULER_ENABLED = False
    
    # WebSocket配置
    WEBSOCKET_ENABLED = False
    
    # 文件上传配置
    UPLOAD_FOLDER = tempfile.mkdtemp()
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # API配置
    API_RATE_LIMIT = None  # 禁用速率限制
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    
    # 邮件配置（测试环境不发送真实邮件）
    MAIL_SUPPRESS_SEND = True
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        pass

# 集成测试配置
class IntegrationTestConfig(TestConfig):
    """集成测试配置"""
    
    # 使用临时文件数据库以便在测试间保持数据
    temp_db_path = os.path.join(tempfile.gettempdir(), 'integration_test.db')
    DATABASE_URL = f'sqlite:///{temp_db_path}'
    
    # 启用调度器进行集成测试
    SCHEDULER_ENABLED = True
    
    @classmethod
    def cleanup(cls):
        """清理测试数据"""
        if os.path.exists(cls.temp_db_path):
            os.unlink(cls.temp_db_path)

# 性能测试配置
class PerformanceTestConfig(TestConfig):
    """性能测试配置"""
    
    # 使用文件数据库以获得更真实的性能数据
    temp_db_path = os.path.join(tempfile.gettempdir(), 'performance_test.db')
    DATABASE_URL = f'sqlite:///{temp_db_path}'
    
    # 启用所有功能
    SCHEDULER_ENABLED = True
    WEBSOCKET_ENABLED = True
    SECURITY_ENABLED = True
    
    # 性能监控
    PROFILE = True
    
    @classmethod
    def cleanup(cls):
        """清理测试数据"""
        if os.path.exists(cls.temp_db_path):
            os.unlink(cls.temp_db_path)

# 配置映射
config = {
    'test': TestConfig,
    'integration': IntegrationTestConfig,
    'performance': PerformanceTestConfig,
    'default': TestConfig
}
