{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { clearFix, resetComponent } from '../../style';\nimport { genCompactItemStyle } from '../../style/compact-item';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { initComponentToken, initInputToken } from './token';\nimport { genBorderlessStyle, genFilledGroupStyle, genFilledStyle, genOutlinedGroupStyle, genOutlinedStyle, genUnderlinedStyle } from './variants';\nexport { initComponentToken, initInputToken };\nexport const genPlaceholderStyle = color => ({\n  // Firefox\n  '&::-moz-placeholder': {\n    opacity: 1\n  },\n  '&::placeholder': {\n    color,\n    userSelect: 'none' // https://github.com/ant-design/ant-design/pull/32639\n  },\n  '&:placeholder-shown': {\n    textOverflow: 'ellipsis'\n  }\n});\nexport const genActiveStyle = token => ({\n  borderColor: token.activeBorderColor,\n  boxShadow: token.activeShadow,\n  outline: 0,\n  backgroundColor: token.activeBg\n});\nconst genInputLargeStyle = token => {\n  const {\n    paddingBlockLG,\n    lineHeightLG,\n    borderRadiusLG,\n    paddingInlineLG\n  } = token;\n  return {\n    padding: `${unit(paddingBlockLG)} ${unit(paddingInlineLG)}`,\n    fontSize: token.inputFontSizeLG,\n    lineHeight: lineHeightLG,\n    borderRadius: borderRadiusLG\n  };\n};\nexport const genInputSmallStyle = token => ({\n  padding: `${unit(token.paddingBlockSM)} ${unit(token.paddingInlineSM)}`,\n  fontSize: token.inputFontSizeSM,\n  borderRadius: token.borderRadiusSM\n});\nexport const genBasicInputStyle = token => Object.assign(Object.assign({\n  position: 'relative',\n  display: 'inline-block',\n  width: '100%',\n  minWidth: 0,\n  padding: `${unit(token.paddingBlock)} ${unit(token.paddingInline)}`,\n  color: token.colorText,\n  fontSize: token.inputFontSize,\n  lineHeight: token.lineHeight,\n  borderRadius: token.borderRadius,\n  transition: `all ${token.motionDurationMid}`\n}, genPlaceholderStyle(token.colorTextPlaceholder)), {\n  // Size\n  '&-lg': Object.assign({}, genInputLargeStyle(token)),\n  '&-sm': Object.assign({}, genInputSmallStyle(token)),\n  // RTL\n  '&-rtl, &-textarea-rtl': {\n    direction: 'rtl'\n  }\n});\nexport const genInputGroupStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  return {\n    position: 'relative',\n    display: 'table',\n    width: '100%',\n    borderCollapse: 'separate',\n    borderSpacing: 0,\n    // Undo padding and float of grid classes\n    \"&[class*='col-']\": {\n      paddingInlineEnd: token.paddingXS,\n      '&:last-child': {\n        paddingInlineEnd: 0\n      }\n    },\n    // Sizing options\n    [`&-lg ${componentCls}, &-lg > ${componentCls}-group-addon`]: Object.assign({}, genInputLargeStyle(token)),\n    [`&-sm ${componentCls}, &-sm > ${componentCls}-group-addon`]: Object.assign({}, genInputSmallStyle(token)),\n    // Fix https://github.com/ant-design/ant-design/issues/5754\n    [`&-lg ${antCls}-select-single ${antCls}-select-selector`]: {\n      height: token.controlHeightLG\n    },\n    [`&-sm ${antCls}-select-single ${antCls}-select-selector`]: {\n      height: token.controlHeightSM\n    },\n    [`> ${componentCls}`]: {\n      display: 'table-cell',\n      '&:not(:first-child):not(:last-child)': {\n        borderRadius: 0\n      }\n    },\n    [`${componentCls}-group`]: {\n      '&-addon, &-wrap': {\n        display: 'table-cell',\n        width: 1,\n        whiteSpace: 'nowrap',\n        verticalAlign: 'middle',\n        '&:not(:first-child):not(:last-child)': {\n          borderRadius: 0\n        }\n      },\n      '&-wrap > *': {\n        display: 'block !important'\n      },\n      '&-addon': {\n        position: 'relative',\n        padding: `0 ${unit(token.paddingInline)}`,\n        color: token.colorText,\n        fontWeight: 'normal',\n        fontSize: token.inputFontSize,\n        textAlign: 'center',\n        borderRadius: token.borderRadius,\n        transition: `all ${token.motionDurationSlow}`,\n        lineHeight: 1,\n        // Reset Select's style in addon\n        [`${antCls}-select`]: {\n          margin: `${unit(token.calc(token.paddingBlock).add(1).mul(-1).equal())} ${unit(token.calc(token.paddingInline).mul(-1).equal())}`,\n          [`&${antCls}-select-single:not(${antCls}-select-customize-input):not(${antCls}-pagination-size-changer)`]: {\n            [`${antCls}-select-selector`]: {\n              backgroundColor: 'inherit',\n              border: `${unit(token.lineWidth)} ${token.lineType} transparent`,\n              boxShadow: 'none'\n            }\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/31333\n        [`${antCls}-cascader-picker`]: {\n          margin: `-9px ${unit(token.calc(token.paddingInline).mul(-1).equal())}`,\n          backgroundColor: 'transparent',\n          [`${antCls}-cascader-input`]: {\n            textAlign: 'start',\n            border: 0,\n            boxShadow: 'none'\n          }\n        }\n      }\n    },\n    [componentCls]: {\n      width: '100%',\n      marginBottom: 0,\n      textAlign: 'inherit',\n      '&:focus': {\n        zIndex: 1,\n        // Fix https://gw.alipayobjects.com/zos/rmsportal/DHNpoqfMXSfrSnlZvhsJ.png\n        borderInlineEndWidth: 1\n      },\n      '&:hover': {\n        zIndex: 1,\n        borderInlineEndWidth: 1,\n        [`${componentCls}-search-with-button &`]: {\n          zIndex: 0\n        }\n      }\n    },\n    // Reset rounded corners\n    [`> ${componentCls}:first-child, ${componentCls}-group-addon:first-child`]: {\n      borderStartEndRadius: 0,\n      borderEndEndRadius: 0,\n      // Reset Select's style in addon\n      [`${antCls}-select ${antCls}-select-selector`]: {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0\n      }\n    },\n    [`> ${componentCls}-affix-wrapper`]: {\n      [`&:not(:first-child) ${componentCls}`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      },\n      [`&:not(:last-child) ${componentCls}`]: {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0\n      }\n    },\n    [`> ${componentCls}:last-child, ${componentCls}-group-addon:last-child`]: {\n      borderStartStartRadius: 0,\n      borderEndStartRadius: 0,\n      // Reset Select's style in addon\n      [`${antCls}-select ${antCls}-select-selector`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`${componentCls}-affix-wrapper`]: {\n      '&:not(:last-child)': {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0,\n        [`${componentCls}-search &`]: {\n          borderStartStartRadius: token.borderRadius,\n          borderEndStartRadius: token.borderRadius\n        }\n      },\n      [`&:not(:first-child), ${componentCls}-search &:not(:first-child)`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`&${componentCls}-group-compact`]: Object.assign(Object.assign({\n      display: 'block'\n    }, clearFix()), {\n      [`${componentCls}-group-addon, ${componentCls}-group-wrap, > ${componentCls}`]: {\n        '&:not(:first-child):not(:last-child)': {\n          borderInlineEndWidth: token.lineWidth,\n          '&:hover, &:focus': {\n            zIndex: 1\n          }\n        }\n      },\n      '& > *': {\n        display: 'inline-flex',\n        float: 'none',\n        verticalAlign: 'top',\n        // https://github.com/ant-design/ant-design-pro/issues/139\n        borderRadius: 0\n      },\n      [`\n        & > ${componentCls}-affix-wrapper,\n        & > ${componentCls}-number-affix-wrapper,\n        & > ${antCls}-picker-range\n      `]: {\n        display: 'inline-flex'\n      },\n      '& > *:not(:last-child)': {\n        marginInlineEnd: token.calc(token.lineWidth).mul(-1).equal(),\n        borderInlineEndWidth: token.lineWidth\n      },\n      // Undo float for .ant-input-group .ant-input\n      [componentCls]: {\n        float: 'none'\n      },\n      // reset border for Select, DatePicker, AutoComplete, Cascader, Mention, TimePicker, Input\n      [`& > ${antCls}-select > ${antCls}-select-selector,\n      & > ${antCls}-select-auto-complete ${componentCls},\n      & > ${antCls}-cascader-picker ${componentCls},\n      & > ${componentCls}-group-wrapper ${componentCls}`]: {\n        borderInlineEndWidth: token.lineWidth,\n        borderRadius: 0,\n        '&:hover, &:focus': {\n          zIndex: 1\n        }\n      },\n      [`& > ${antCls}-select-focused`]: {\n        zIndex: 1\n      },\n      // update z-index for arrow icon\n      [`& > ${antCls}-select > ${antCls}-select-arrow`]: {\n        zIndex: 1 // https://github.com/ant-design/ant-design/issues/20371\n      },\n      [`& > *:first-child,\n      & > ${antCls}-select:first-child > ${antCls}-select-selector,\n      & > ${antCls}-select-auto-complete:first-child ${componentCls},\n      & > ${antCls}-cascader-picker:first-child ${componentCls}`]: {\n        borderStartStartRadius: token.borderRadius,\n        borderEndStartRadius: token.borderRadius\n      },\n      [`& > *:last-child,\n      & > ${antCls}-select:last-child > ${antCls}-select-selector,\n      & > ${antCls}-cascader-picker:last-child ${componentCls},\n      & > ${antCls}-cascader-picker-focused:last-child ${componentCls}`]: {\n        borderInlineEndWidth: token.lineWidth,\n        borderStartEndRadius: token.borderRadius,\n        borderEndEndRadius: token.borderRadius\n      },\n      // https://github.com/ant-design/ant-design/issues/12493\n      [`& > ${antCls}-select-auto-complete ${componentCls}`]: {\n        verticalAlign: 'top'\n      },\n      [`${componentCls}-group-wrapper + ${componentCls}-group-wrapper`]: {\n        marginInlineStart: token.calc(token.lineWidth).mul(-1).equal(),\n        [`${componentCls}-affix-wrapper`]: {\n          borderRadius: 0\n        }\n      },\n      [`${componentCls}-group-wrapper:not(:last-child)`]: {\n        [`&${componentCls}-search > ${componentCls}-group`]: {\n          [`& > ${componentCls}-group-addon > ${componentCls}-search-button`]: {\n            borderRadius: 0\n          },\n          [`& > ${componentCls}`]: {\n            borderStartStartRadius: token.borderRadius,\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0,\n            borderEndStartRadius: token.borderRadius\n          }\n        }\n      }\n    })\n  };\n};\nexport const genInputStyle = token => {\n  const {\n    componentCls,\n    controlHeightSM,\n    lineWidth,\n    calc\n  } = token;\n  const FIXED_CHROME_COLOR_HEIGHT = 16;\n  const colorSmallPadding = calc(controlHeightSM).sub(calc(lineWidth).mul(2)).sub(FIXED_CHROME_COLOR_HEIGHT).div(2).equal();\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genBasicInputStyle(token)), genOutlinedStyle(token)), genFilledStyle(token)), genBorderlessStyle(token)), genUnderlinedStyle(token)), {\n      '&[type=\"color\"]': {\n        height: token.controlHeight,\n        [`&${componentCls}-lg`]: {\n          height: token.controlHeightLG\n        },\n        [`&${componentCls}-sm`]: {\n          height: controlHeightSM,\n          paddingTop: colorSmallPadding,\n          paddingBottom: colorSmallPadding\n        }\n      },\n      '&[type=\"search\"]::-webkit-search-cancel-button, &[type=\"search\"]::-webkit-search-decoration': {\n        appearance: 'none'\n      }\n    })\n  };\n};\nconst genAllowClearStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    // ========================= Input =========================\n    [`${componentCls}-clear-icon`]: {\n      margin: 0,\n      padding: 0,\n      lineHeight: 0,\n      color: token.colorTextQuaternary,\n      fontSize: token.fontSizeIcon,\n      verticalAlign: -1,\n      // https://github.com/ant-design/ant-design/pull/18151\n      // https://codesandbox.io/s/wizardly-sun-u10br\n      cursor: 'pointer',\n      transition: `color ${token.motionDurationSlow}`,\n      border: 'none',\n      outline: 'none',\n      backgroundColor: 'transparent',\n      '&:hover': {\n        color: token.colorIcon\n      },\n      '&:active': {\n        color: token.colorText\n      },\n      '&-hidden': {\n        visibility: 'hidden'\n      },\n      '&-has-suffix': {\n        margin: `0 ${unit(token.inputAffixPadding)}`\n      }\n    }\n  };\n};\nexport const genAffixStyle = token => {\n  const {\n    componentCls,\n    inputAffixPadding,\n    colorTextDescription,\n    motionDurationSlow,\n    colorIcon,\n    colorIconHover,\n    iconCls\n  } = token;\n  const affixCls = `${componentCls}-affix-wrapper`;\n  const affixClsDisabled = `${componentCls}-affix-wrapper-disabled`;\n  return {\n    [affixCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, genBasicInputStyle(token)), {\n      display: 'inline-flex',\n      [`&:not(${componentCls}-disabled):hover`]: {\n        zIndex: 1,\n        [`${componentCls}-search-with-button &`]: {\n          zIndex: 0\n        }\n      },\n      '&-focused, &:focus': {\n        zIndex: 1\n      },\n      [`> input${componentCls}`]: {\n        padding: 0\n      },\n      [`> input${componentCls}, > textarea${componentCls}`]: {\n        fontSize: 'inherit',\n        border: 'none',\n        borderRadius: 0,\n        outline: 'none',\n        background: 'transparent',\n        color: 'inherit',\n        '&::-ms-reveal': {\n          display: 'none'\n        },\n        '&:focus': {\n          boxShadow: 'none !important'\n        }\n      },\n      '&::before': {\n        display: 'inline-block',\n        width: 0,\n        visibility: 'hidden',\n        content: '\"\\\\a0\"'\n      },\n      [componentCls]: {\n        '&-prefix, &-suffix': {\n          display: 'flex',\n          flex: 'none',\n          alignItems: 'center',\n          '> *:not(:last-child)': {\n            marginInlineEnd: token.paddingXS\n          }\n        },\n        '&-show-count-suffix': {\n          color: colorTextDescription,\n          direction: 'ltr'\n        },\n        '&-show-count-has-suffix': {\n          marginInlineEnd: token.paddingXXS\n        },\n        '&-prefix': {\n          marginInlineEnd: inputAffixPadding\n        },\n        '&-suffix': {\n          marginInlineStart: inputAffixPadding\n        }\n      }\n    }), genAllowClearStyle(token)), {\n      // password\n      [`${iconCls}${componentCls}-password-icon`]: {\n        color: colorIcon,\n        cursor: 'pointer',\n        transition: `all ${motionDurationSlow}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }),\n    // 覆盖 affix-wrapper borderRadius！\n    [`${componentCls}-underlined`]: {\n      borderRadius: 0\n    },\n    [affixClsDisabled]: {\n      // password disabled\n      [`${iconCls}${componentCls}-password-icon`]: {\n        color: colorIcon,\n        cursor: 'not-allowed',\n        '&:hover': {\n          color: colorIcon\n        }\n      }\n    }\n  };\n};\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    borderRadiusLG,\n    borderRadiusSM\n  } = token;\n  return {\n    [`${componentCls}-group`]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genInputGroupStyle(token)), {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-wrapper': Object.assign(Object.assign(Object.assign({\n        display: 'inline-block',\n        width: '100%',\n        textAlign: 'start',\n        verticalAlign: 'top',\n        '&-rtl': {\n          direction: 'rtl'\n        },\n        // Size\n        '&-lg': {\n          [`${componentCls}-group-addon`]: {\n            borderRadius: borderRadiusLG,\n            fontSize: token.inputFontSizeLG\n          }\n        },\n        '&-sm': {\n          [`${componentCls}-group-addon`]: {\n            borderRadius: borderRadiusSM\n          }\n        }\n      }, genOutlinedGroupStyle(token)), genFilledGroupStyle(token)), {\n        // '&-disabled': {\n        //   [`${componentCls}-group-addon`]: {\n        //     ...genDisabledStyle(token),\n        //   },\n        // },\n        // Fix the issue of using icons in Space Compact mode\n        // https://github.com/ant-design/ant-design/issues/42122\n        [`&:not(${componentCls}-compact-first-item):not(${componentCls}-compact-last-item)${componentCls}-compact-item`]: {\n          [`${componentCls}, ${componentCls}-group-addon`]: {\n            borderRadius: 0\n          }\n        },\n        [`&:not(${componentCls}-compact-last-item)${componentCls}-compact-first-item`]: {\n          [`${componentCls}, ${componentCls}-group-addon`]: {\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0\n          }\n        },\n        [`&:not(${componentCls}-compact-first-item)${componentCls}-compact-last-item`]: {\n          [`${componentCls}, ${componentCls}-group-addon`]: {\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0\n          }\n        },\n        // Fix the issue of input use show-count param in space compact mode\n        // https://github.com/ant-design/ant-design/issues/46872\n        [`&:not(${componentCls}-compact-last-item)${componentCls}-compact-item`]: {\n          [`${componentCls}-affix-wrapper`]: {\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0\n          }\n        },\n        // Fix the issue of input use `addonAfter` param in space compact mode\n        // https://github.com/ant-design/ant-design/issues/52483\n        [`&:not(${componentCls}-compact-first-item)${componentCls}-compact-item`]: {\n          [`${componentCls}-affix-wrapper`]: {\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0\n          }\n        }\n      })\n    })\n  };\n};\nconst genSearchInputStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  const searchPrefixCls = `${componentCls}-search`;\n  return {\n    [searchPrefixCls]: {\n      [componentCls]: {\n        '&:hover, &:focus': {\n          [`+ ${componentCls}-group-addon ${searchPrefixCls}-button:not(${antCls}-btn-color-primary):not(${antCls}-btn-variant-text)`]: {\n            borderInlineStartColor: token.colorPrimaryHover\n          }\n        }\n      },\n      [`${componentCls}-affix-wrapper`]: {\n        height: token.controlHeight,\n        borderRadius: 0\n      },\n      // fix slight height diff in Firefox:\n      // https://ant.design/components/auto-complete-cn/#auto-complete-demo-certain-category\n      [`${componentCls}-lg`]: {\n        lineHeight: token.calc(token.lineHeightLG).sub(0.0002).equal()\n      },\n      [`> ${componentCls}-group`]: {\n        [`> ${componentCls}-group-addon:last-child`]: {\n          insetInlineStart: -1,\n          padding: 0,\n          border: 0,\n          [`${searchPrefixCls}-button`]: {\n            // Fix https://github.com/ant-design/ant-design/issues/47150\n            marginInlineEnd: -1,\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0,\n            boxShadow: 'none'\n          },\n          [`${searchPrefixCls}-button:not(${antCls}-btn-color-primary)`]: {\n            color: token.colorTextDescription,\n            '&:hover': {\n              color: token.colorPrimaryHover\n            },\n            '&:active': {\n              color: token.colorPrimaryActive\n            },\n            [`&${antCls}-btn-loading::before`]: {\n              inset: 0\n            }\n          }\n        }\n      },\n      [`${searchPrefixCls}-button`]: {\n        height: token.controlHeight,\n        '&:hover, &:focus': {\n          zIndex: 1\n        }\n      },\n      '&-large': {\n        [`${componentCls}-affix-wrapper, ${searchPrefixCls}-button`]: {\n          height: token.controlHeightLG\n        }\n      },\n      '&-small': {\n        [`${componentCls}-affix-wrapper, ${searchPrefixCls}-button`]: {\n          height: token.controlHeightSM\n        }\n      },\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      // ===================== Compact Item Customized Styles =====================\n      [`&${componentCls}-compact-item`]: {\n        [`&:not(${componentCls}-compact-last-item)`]: {\n          [`${componentCls}-group-addon`]: {\n            [`${componentCls}-search-button`]: {\n              marginInlineEnd: token.calc(token.lineWidth).mul(-1).equal(),\n              borderRadius: 0\n            }\n          }\n        },\n        [`&:not(${componentCls}-compact-first-item)`]: {\n          [`${componentCls},${componentCls}-affix-wrapper`]: {\n            borderRadius: 0\n          }\n        },\n        [`> ${componentCls}-group-addon ${componentCls}-search-button,\n        > ${componentCls},\n        ${componentCls}-affix-wrapper`]: {\n          '&:hover, &:focus, &:active': {\n            zIndex: 2\n          }\n        },\n        [`> ${componentCls}-affix-wrapper-focused`]: {\n          zIndex: 2\n        }\n      }\n    }\n  };\n};\n// ============================== Range ===============================\nconst genRangeStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-out-of-range`]: {\n      [`&, & input, & textarea, ${componentCls}-show-count-suffix, ${componentCls}-data-count`]: {\n        color: token.colorError\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const useSharedStyle = genStyleHooks(['Input', 'Shared'], token => {\n  const inputToken = mergeToken(token, initInputToken(token));\n  return [genInputStyle(inputToken), genAffixStyle(inputToken)];\n}, initComponentToken, {\n  resetFont: false\n});\nexport default genStyleHooks(['Input', 'Component'], token => {\n  const inputToken = mergeToken(token, initInputToken(token));\n  return [genGroupStyle(inputToken), genSearchInputStyle(inputToken), genRangeStyle(inputToken),\n  // =====================================================\n  // ==             Space Compact                       ==\n  // =====================================================\n  genCompactItemStyle(inputToken)];\n}, initComponentToken, {\n  resetFont: false\n});", "map": {"version": 3, "names": ["unit", "clearFix", "resetComponent", "genCompactItemStyle", "genStyleHooks", "mergeToken", "initComponentToken", "initInputToken", "genBorderlessStyle", "genFilledGroupStyle", "genFilledStyle", "genOutlinedGroupStyle", "genOutlinedStyle", "genUnderlinedStyle", "genPlaceholderStyle", "color", "opacity", "userSelect", "textOverflow", "genActiveStyle", "token", "borderColor", "activeBorderColor", "boxShadow", "activeShadow", "outline", "backgroundColor", "activeBg", "genInputLargeStyle", "paddingBlockLG", "lineHeightLG", "borderRadiusLG", "paddingInlineLG", "padding", "fontSize", "inputFontSizeLG", "lineHeight", "borderRadius", "genInputSmallStyle", "paddingBlockSM", "paddingInlineSM", "inputFontSizeSM", "borderRadiusSM", "genBasicInputStyle", "Object", "assign", "position", "display", "width", "min<PERSON><PERSON><PERSON>", "paddingBlock", "paddingInline", "colorText", "inputFontSize", "transition", "motionDurationMid", "colorTextPlaceholder", "direction", "genInputGroupStyle", "componentCls", "antCls", "borderCollapse", "borderSpacing", "paddingInlineEnd", "paddingXS", "height", "controlHeightLG", "controlHeightSM", "whiteSpace", "verticalAlign", "fontWeight", "textAlign", "motionDurationSlow", "margin", "calc", "add", "mul", "equal", "border", "lineWidth", "lineType", "marginBottom", "zIndex", "borderInlineEndWidth", "borderStartEndRadius", "borderEndEndRadius", "borderStartStartRadius", "borderEndStartRadius", "float", "marginInlineEnd", "marginInlineStart", "genInputStyle", "FIXED_CHROME_COLOR_HEIGHT", "colorSmallPadding", "sub", "div", "controlHeight", "paddingTop", "paddingBottom", "appearance", "genAllowClearStyle", "colorTextQuaternary", "fontSizeIcon", "cursor", "colorIcon", "visibility", "inputAffixPadding", "genAffixStyle", "colorTextDescription", "colorIconHover", "iconCls", "affixCls", "affixClsDisabled", "background", "content", "flex", "alignItems", "paddingXXS", "genGroupStyle", "genSearchInputStyle", "searchPrefixCls", "borderInlineStartColor", "colorPrimaryHover", "insetInlineStart", "colorPrimaryActive", "inset", "genRangeStyle", "colorError", "useSharedStyle", "inputToken", "resetFont"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/input/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { clearFix, resetComponent } from '../../style';\nimport { genCompactItemStyle } from '../../style/compact-item';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { initComponentToken, initInputToken } from './token';\nimport { genBorderlessStyle, genFilledGroupStyle, genFilledStyle, genOutlinedGroupStyle, genOutlinedStyle, genUnderlinedStyle } from './variants';\nexport { initComponentToken, initInputToken };\nexport const genPlaceholderStyle = color => ({\n  // Firefox\n  '&::-moz-placeholder': {\n    opacity: 1\n  },\n  '&::placeholder': {\n    color,\n    userSelect: 'none' // https://github.com/ant-design/ant-design/pull/32639\n  },\n  '&:placeholder-shown': {\n    textOverflow: 'ellipsis'\n  }\n});\nexport const genActiveStyle = token => ({\n  borderColor: token.activeBorderColor,\n  boxShadow: token.activeShadow,\n  outline: 0,\n  backgroundColor: token.activeBg\n});\nconst genInputLargeStyle = token => {\n  const {\n    paddingBlockLG,\n    lineHeightLG,\n    borderRadiusLG,\n    paddingInlineLG\n  } = token;\n  return {\n    padding: `${unit(paddingBlockLG)} ${unit(paddingInlineLG)}`,\n    fontSize: token.inputFontSizeLG,\n    lineHeight: lineHeightLG,\n    borderRadius: borderRadiusLG\n  };\n};\nexport const genInputSmallStyle = token => ({\n  padding: `${unit(token.paddingBlockSM)} ${unit(token.paddingInlineSM)}`,\n  fontSize: token.inputFontSizeSM,\n  borderRadius: token.borderRadiusSM\n});\nexport const genBasicInputStyle = token => Object.assign(Object.assign({\n  position: 'relative',\n  display: 'inline-block',\n  width: '100%',\n  minWidth: 0,\n  padding: `${unit(token.paddingBlock)} ${unit(token.paddingInline)}`,\n  color: token.colorText,\n  fontSize: token.inputFontSize,\n  lineHeight: token.lineHeight,\n  borderRadius: token.borderRadius,\n  transition: `all ${token.motionDurationMid}`\n}, genPlaceholderStyle(token.colorTextPlaceholder)), {\n  // Size\n  '&-lg': Object.assign({}, genInputLargeStyle(token)),\n  '&-sm': Object.assign({}, genInputSmallStyle(token)),\n  // RTL\n  '&-rtl, &-textarea-rtl': {\n    direction: 'rtl'\n  }\n});\nexport const genInputGroupStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  return {\n    position: 'relative',\n    display: 'table',\n    width: '100%',\n    borderCollapse: 'separate',\n    borderSpacing: 0,\n    // Undo padding and float of grid classes\n    \"&[class*='col-']\": {\n      paddingInlineEnd: token.paddingXS,\n      '&:last-child': {\n        paddingInlineEnd: 0\n      }\n    },\n    // Sizing options\n    [`&-lg ${componentCls}, &-lg > ${componentCls}-group-addon`]: Object.assign({}, genInputLargeStyle(token)),\n    [`&-sm ${componentCls}, &-sm > ${componentCls}-group-addon`]: Object.assign({}, genInputSmallStyle(token)),\n    // Fix https://github.com/ant-design/ant-design/issues/5754\n    [`&-lg ${antCls}-select-single ${antCls}-select-selector`]: {\n      height: token.controlHeightLG\n    },\n    [`&-sm ${antCls}-select-single ${antCls}-select-selector`]: {\n      height: token.controlHeightSM\n    },\n    [`> ${componentCls}`]: {\n      display: 'table-cell',\n      '&:not(:first-child):not(:last-child)': {\n        borderRadius: 0\n      }\n    },\n    [`${componentCls}-group`]: {\n      '&-addon, &-wrap': {\n        display: 'table-cell',\n        width: 1,\n        whiteSpace: 'nowrap',\n        verticalAlign: 'middle',\n        '&:not(:first-child):not(:last-child)': {\n          borderRadius: 0\n        }\n      },\n      '&-wrap > *': {\n        display: 'block !important'\n      },\n      '&-addon': {\n        position: 'relative',\n        padding: `0 ${unit(token.paddingInline)}`,\n        color: token.colorText,\n        fontWeight: 'normal',\n        fontSize: token.inputFontSize,\n        textAlign: 'center',\n        borderRadius: token.borderRadius,\n        transition: `all ${token.motionDurationSlow}`,\n        lineHeight: 1,\n        // Reset Select's style in addon\n        [`${antCls}-select`]: {\n          margin: `${unit(token.calc(token.paddingBlock).add(1).mul(-1).equal())} ${unit(token.calc(token.paddingInline).mul(-1).equal())}`,\n          [`&${antCls}-select-single:not(${antCls}-select-customize-input):not(${antCls}-pagination-size-changer)`]: {\n            [`${antCls}-select-selector`]: {\n              backgroundColor: 'inherit',\n              border: `${unit(token.lineWidth)} ${token.lineType} transparent`,\n              boxShadow: 'none'\n            }\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/31333\n        [`${antCls}-cascader-picker`]: {\n          margin: `-9px ${unit(token.calc(token.paddingInline).mul(-1).equal())}`,\n          backgroundColor: 'transparent',\n          [`${antCls}-cascader-input`]: {\n            textAlign: 'start',\n            border: 0,\n            boxShadow: 'none'\n          }\n        }\n      }\n    },\n    [componentCls]: {\n      width: '100%',\n      marginBottom: 0,\n      textAlign: 'inherit',\n      '&:focus': {\n        zIndex: 1,\n        // Fix https://gw.alipayobjects.com/zos/rmsportal/DHNpoqfMXSfrSnlZvhsJ.png\n        borderInlineEndWidth: 1\n      },\n      '&:hover': {\n        zIndex: 1,\n        borderInlineEndWidth: 1,\n        [`${componentCls}-search-with-button &`]: {\n          zIndex: 0\n        }\n      }\n    },\n    // Reset rounded corners\n    [`> ${componentCls}:first-child, ${componentCls}-group-addon:first-child`]: {\n      borderStartEndRadius: 0,\n      borderEndEndRadius: 0,\n      // Reset Select's style in addon\n      [`${antCls}-select ${antCls}-select-selector`]: {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0\n      }\n    },\n    [`> ${componentCls}-affix-wrapper`]: {\n      [`&:not(:first-child) ${componentCls}`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      },\n      [`&:not(:last-child) ${componentCls}`]: {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0\n      }\n    },\n    [`> ${componentCls}:last-child, ${componentCls}-group-addon:last-child`]: {\n      borderStartStartRadius: 0,\n      borderEndStartRadius: 0,\n      // Reset Select's style in addon\n      [`${antCls}-select ${antCls}-select-selector`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`${componentCls}-affix-wrapper`]: {\n      '&:not(:last-child)': {\n        borderStartEndRadius: 0,\n        borderEndEndRadius: 0,\n        [`${componentCls}-search &`]: {\n          borderStartStartRadius: token.borderRadius,\n          borderEndStartRadius: token.borderRadius\n        }\n      },\n      [`&:not(:first-child), ${componentCls}-search &:not(:first-child)`]: {\n        borderStartStartRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`&${componentCls}-group-compact`]: Object.assign(Object.assign({\n      display: 'block'\n    }, clearFix()), {\n      [`${componentCls}-group-addon, ${componentCls}-group-wrap, > ${componentCls}`]: {\n        '&:not(:first-child):not(:last-child)': {\n          borderInlineEndWidth: token.lineWidth,\n          '&:hover, &:focus': {\n            zIndex: 1\n          }\n        }\n      },\n      '& > *': {\n        display: 'inline-flex',\n        float: 'none',\n        verticalAlign: 'top',\n        // https://github.com/ant-design/ant-design-pro/issues/139\n        borderRadius: 0\n      },\n      [`\n        & > ${componentCls}-affix-wrapper,\n        & > ${componentCls}-number-affix-wrapper,\n        & > ${antCls}-picker-range\n      `]: {\n        display: 'inline-flex'\n      },\n      '& > *:not(:last-child)': {\n        marginInlineEnd: token.calc(token.lineWidth).mul(-1).equal(),\n        borderInlineEndWidth: token.lineWidth\n      },\n      // Undo float for .ant-input-group .ant-input\n      [componentCls]: {\n        float: 'none'\n      },\n      // reset border for Select, DatePicker, AutoComplete, Cascader, Mention, TimePicker, Input\n      [`& > ${antCls}-select > ${antCls}-select-selector,\n      & > ${antCls}-select-auto-complete ${componentCls},\n      & > ${antCls}-cascader-picker ${componentCls},\n      & > ${componentCls}-group-wrapper ${componentCls}`]: {\n        borderInlineEndWidth: token.lineWidth,\n        borderRadius: 0,\n        '&:hover, &:focus': {\n          zIndex: 1\n        }\n      },\n      [`& > ${antCls}-select-focused`]: {\n        zIndex: 1\n      },\n      // update z-index for arrow icon\n      [`& > ${antCls}-select > ${antCls}-select-arrow`]: {\n        zIndex: 1 // https://github.com/ant-design/ant-design/issues/20371\n      },\n      [`& > *:first-child,\n      & > ${antCls}-select:first-child > ${antCls}-select-selector,\n      & > ${antCls}-select-auto-complete:first-child ${componentCls},\n      & > ${antCls}-cascader-picker:first-child ${componentCls}`]: {\n        borderStartStartRadius: token.borderRadius,\n        borderEndStartRadius: token.borderRadius\n      },\n      [`& > *:last-child,\n      & > ${antCls}-select:last-child > ${antCls}-select-selector,\n      & > ${antCls}-cascader-picker:last-child ${componentCls},\n      & > ${antCls}-cascader-picker-focused:last-child ${componentCls}`]: {\n        borderInlineEndWidth: token.lineWidth,\n        borderStartEndRadius: token.borderRadius,\n        borderEndEndRadius: token.borderRadius\n      },\n      // https://github.com/ant-design/ant-design/issues/12493\n      [`& > ${antCls}-select-auto-complete ${componentCls}`]: {\n        verticalAlign: 'top'\n      },\n      [`${componentCls}-group-wrapper + ${componentCls}-group-wrapper`]: {\n        marginInlineStart: token.calc(token.lineWidth).mul(-1).equal(),\n        [`${componentCls}-affix-wrapper`]: {\n          borderRadius: 0\n        }\n      },\n      [`${componentCls}-group-wrapper:not(:last-child)`]: {\n        [`&${componentCls}-search > ${componentCls}-group`]: {\n          [`& > ${componentCls}-group-addon > ${componentCls}-search-button`]: {\n            borderRadius: 0\n          },\n          [`& > ${componentCls}`]: {\n            borderStartStartRadius: token.borderRadius,\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0,\n            borderEndStartRadius: token.borderRadius\n          }\n        }\n      }\n    })\n  };\n};\nexport const genInputStyle = token => {\n  const {\n    componentCls,\n    controlHeightSM,\n    lineWidth,\n    calc\n  } = token;\n  const FIXED_CHROME_COLOR_HEIGHT = 16;\n  const colorSmallPadding = calc(controlHeightSM).sub(calc(lineWidth).mul(2)).sub(FIXED_CHROME_COLOR_HEIGHT).div(2).equal();\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genBasicInputStyle(token)), genOutlinedStyle(token)), genFilledStyle(token)), genBorderlessStyle(token)), genUnderlinedStyle(token)), {\n      '&[type=\"color\"]': {\n        height: token.controlHeight,\n        [`&${componentCls}-lg`]: {\n          height: token.controlHeightLG\n        },\n        [`&${componentCls}-sm`]: {\n          height: controlHeightSM,\n          paddingTop: colorSmallPadding,\n          paddingBottom: colorSmallPadding\n        }\n      },\n      '&[type=\"search\"]::-webkit-search-cancel-button, &[type=\"search\"]::-webkit-search-decoration': {\n        appearance: 'none'\n      }\n    })\n  };\n};\nconst genAllowClearStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    // ========================= Input =========================\n    [`${componentCls}-clear-icon`]: {\n      margin: 0,\n      padding: 0,\n      lineHeight: 0,\n      color: token.colorTextQuaternary,\n      fontSize: token.fontSizeIcon,\n      verticalAlign: -1,\n      // https://github.com/ant-design/ant-design/pull/18151\n      // https://codesandbox.io/s/wizardly-sun-u10br\n      cursor: 'pointer',\n      transition: `color ${token.motionDurationSlow}`,\n      border: 'none',\n      outline: 'none',\n      backgroundColor: 'transparent',\n      '&:hover': {\n        color: token.colorIcon\n      },\n      '&:active': {\n        color: token.colorText\n      },\n      '&-hidden': {\n        visibility: 'hidden'\n      },\n      '&-has-suffix': {\n        margin: `0 ${unit(token.inputAffixPadding)}`\n      }\n    }\n  };\n};\nexport const genAffixStyle = token => {\n  const {\n    componentCls,\n    inputAffixPadding,\n    colorTextDescription,\n    motionDurationSlow,\n    colorIcon,\n    colorIconHover,\n    iconCls\n  } = token;\n  const affixCls = `${componentCls}-affix-wrapper`;\n  const affixClsDisabled = `${componentCls}-affix-wrapper-disabled`;\n  return {\n    [affixCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, genBasicInputStyle(token)), {\n      display: 'inline-flex',\n      [`&:not(${componentCls}-disabled):hover`]: {\n        zIndex: 1,\n        [`${componentCls}-search-with-button &`]: {\n          zIndex: 0\n        }\n      },\n      '&-focused, &:focus': {\n        zIndex: 1\n      },\n      [`> input${componentCls}`]: {\n        padding: 0\n      },\n      [`> input${componentCls}, > textarea${componentCls}`]: {\n        fontSize: 'inherit',\n        border: 'none',\n        borderRadius: 0,\n        outline: 'none',\n        background: 'transparent',\n        color: 'inherit',\n        '&::-ms-reveal': {\n          display: 'none'\n        },\n        '&:focus': {\n          boxShadow: 'none !important'\n        }\n      },\n      '&::before': {\n        display: 'inline-block',\n        width: 0,\n        visibility: 'hidden',\n        content: '\"\\\\a0\"'\n      },\n      [componentCls]: {\n        '&-prefix, &-suffix': {\n          display: 'flex',\n          flex: 'none',\n          alignItems: 'center',\n          '> *:not(:last-child)': {\n            marginInlineEnd: token.paddingXS\n          }\n        },\n        '&-show-count-suffix': {\n          color: colorTextDescription,\n          direction: 'ltr'\n        },\n        '&-show-count-has-suffix': {\n          marginInlineEnd: token.paddingXXS\n        },\n        '&-prefix': {\n          marginInlineEnd: inputAffixPadding\n        },\n        '&-suffix': {\n          marginInlineStart: inputAffixPadding\n        }\n      }\n    }), genAllowClearStyle(token)), {\n      // password\n      [`${iconCls}${componentCls}-password-icon`]: {\n        color: colorIcon,\n        cursor: 'pointer',\n        transition: `all ${motionDurationSlow}`,\n        '&:hover': {\n          color: colorIconHover\n        }\n      }\n    }),\n    // 覆盖 affix-wrapper borderRadius！\n    [`${componentCls}-underlined`]: {\n      borderRadius: 0\n    },\n    [affixClsDisabled]: {\n      // password disabled\n      [`${iconCls}${componentCls}-password-icon`]: {\n        color: colorIcon,\n        cursor: 'not-allowed',\n        '&:hover': {\n          color: colorIcon\n        }\n      }\n    }\n  };\n};\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    borderRadiusLG,\n    borderRadiusSM\n  } = token;\n  return {\n    [`${componentCls}-group`]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genInputGroupStyle(token)), {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-wrapper': Object.assign(Object.assign(Object.assign({\n        display: 'inline-block',\n        width: '100%',\n        textAlign: 'start',\n        verticalAlign: 'top',\n        '&-rtl': {\n          direction: 'rtl'\n        },\n        // Size\n        '&-lg': {\n          [`${componentCls}-group-addon`]: {\n            borderRadius: borderRadiusLG,\n            fontSize: token.inputFontSizeLG\n          }\n        },\n        '&-sm': {\n          [`${componentCls}-group-addon`]: {\n            borderRadius: borderRadiusSM\n          }\n        }\n      }, genOutlinedGroupStyle(token)), genFilledGroupStyle(token)), {\n        // '&-disabled': {\n        //   [`${componentCls}-group-addon`]: {\n        //     ...genDisabledStyle(token),\n        //   },\n        // },\n        // Fix the issue of using icons in Space Compact mode\n        // https://github.com/ant-design/ant-design/issues/42122\n        [`&:not(${componentCls}-compact-first-item):not(${componentCls}-compact-last-item)${componentCls}-compact-item`]: {\n          [`${componentCls}, ${componentCls}-group-addon`]: {\n            borderRadius: 0\n          }\n        },\n        [`&:not(${componentCls}-compact-last-item)${componentCls}-compact-first-item`]: {\n          [`${componentCls}, ${componentCls}-group-addon`]: {\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0\n          }\n        },\n        [`&:not(${componentCls}-compact-first-item)${componentCls}-compact-last-item`]: {\n          [`${componentCls}, ${componentCls}-group-addon`]: {\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0\n          }\n        },\n        // Fix the issue of input use show-count param in space compact mode\n        // https://github.com/ant-design/ant-design/issues/46872\n        [`&:not(${componentCls}-compact-last-item)${componentCls}-compact-item`]: {\n          [`${componentCls}-affix-wrapper`]: {\n            borderStartEndRadius: 0,\n            borderEndEndRadius: 0\n          }\n        },\n        // Fix the issue of input use `addonAfter` param in space compact mode\n        // https://github.com/ant-design/ant-design/issues/52483\n        [`&:not(${componentCls}-compact-first-item)${componentCls}-compact-item`]: {\n          [`${componentCls}-affix-wrapper`]: {\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0\n          }\n        }\n      })\n    })\n  };\n};\nconst genSearchInputStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  const searchPrefixCls = `${componentCls}-search`;\n  return {\n    [searchPrefixCls]: {\n      [componentCls]: {\n        '&:hover, &:focus': {\n          [`+ ${componentCls}-group-addon ${searchPrefixCls}-button:not(${antCls}-btn-color-primary):not(${antCls}-btn-variant-text)`]: {\n            borderInlineStartColor: token.colorPrimaryHover\n          }\n        }\n      },\n      [`${componentCls}-affix-wrapper`]: {\n        height: token.controlHeight,\n        borderRadius: 0\n      },\n      // fix slight height diff in Firefox:\n      // https://ant.design/components/auto-complete-cn/#auto-complete-demo-certain-category\n      [`${componentCls}-lg`]: {\n        lineHeight: token.calc(token.lineHeightLG).sub(0.0002).equal()\n      },\n      [`> ${componentCls}-group`]: {\n        [`> ${componentCls}-group-addon:last-child`]: {\n          insetInlineStart: -1,\n          padding: 0,\n          border: 0,\n          [`${searchPrefixCls}-button`]: {\n            // Fix https://github.com/ant-design/ant-design/issues/47150\n            marginInlineEnd: -1,\n            borderStartStartRadius: 0,\n            borderEndStartRadius: 0,\n            boxShadow: 'none'\n          },\n          [`${searchPrefixCls}-button:not(${antCls}-btn-color-primary)`]: {\n            color: token.colorTextDescription,\n            '&:hover': {\n              color: token.colorPrimaryHover\n            },\n            '&:active': {\n              color: token.colorPrimaryActive\n            },\n            [`&${antCls}-btn-loading::before`]: {\n              inset: 0\n            }\n          }\n        }\n      },\n      [`${searchPrefixCls}-button`]: {\n        height: token.controlHeight,\n        '&:hover, &:focus': {\n          zIndex: 1\n        }\n      },\n      '&-large': {\n        [`${componentCls}-affix-wrapper, ${searchPrefixCls}-button`]: {\n          height: token.controlHeightLG\n        }\n      },\n      '&-small': {\n        [`${componentCls}-affix-wrapper, ${searchPrefixCls}-button`]: {\n          height: token.controlHeightSM\n        }\n      },\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      // ===================== Compact Item Customized Styles =====================\n      [`&${componentCls}-compact-item`]: {\n        [`&:not(${componentCls}-compact-last-item)`]: {\n          [`${componentCls}-group-addon`]: {\n            [`${componentCls}-search-button`]: {\n              marginInlineEnd: token.calc(token.lineWidth).mul(-1).equal(),\n              borderRadius: 0\n            }\n          }\n        },\n        [`&:not(${componentCls}-compact-first-item)`]: {\n          [`${componentCls},${componentCls}-affix-wrapper`]: {\n            borderRadius: 0\n          }\n        },\n        [`> ${componentCls}-group-addon ${componentCls}-search-button,\n        > ${componentCls},\n        ${componentCls}-affix-wrapper`]: {\n          '&:hover, &:focus, &:active': {\n            zIndex: 2\n          }\n        },\n        [`> ${componentCls}-affix-wrapper-focused`]: {\n          zIndex: 2\n        }\n      }\n    }\n  };\n};\n// ============================== Range ===============================\nconst genRangeStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-out-of-range`]: {\n      [`&, & input, & textarea, ${componentCls}-show-count-suffix, ${componentCls}-data-count`]: {\n        color: token.colorError\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const useSharedStyle = genStyleHooks(['Input', 'Shared'], token => {\n  const inputToken = mergeToken(token, initInputToken(token));\n  return [genInputStyle(inputToken), genAffixStyle(inputToken)];\n}, initComponentToken, {\n  resetFont: false\n});\nexport default genStyleHooks(['Input', 'Component'], token => {\n  const inputToken = mergeToken(token, initInputToken(token));\n  return [genGroupStyle(inputToken), genSearchInputStyle(inputToken), genRangeStyle(inputToken),\n  // =====================================================\n  // ==             Space Compact                       ==\n  // =====================================================\n  genCompactItemStyle(inputToken)];\n}, initComponentToken, {\n  resetFont: false\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,QAAQ,EAAEC,cAAc,QAAQ,aAAa;AACtD,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,SAAS;AAC5D,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,YAAY;AACjJ,SAASP,kBAAkB,EAAEC,cAAc;AAC3C,OAAO,MAAMO,mBAAmB,GAAGC,KAAK,KAAK;EAC3C;EACA,qBAAqB,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EACD,gBAAgB,EAAE;IAChBD,KAAK;IACLE,UAAU,EAAE,MAAM,CAAC;EACrB,CAAC;EACD,qBAAqB,EAAE;IACrBC,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,cAAc,GAAGC,KAAK,KAAK;EACtCC,WAAW,EAAED,KAAK,CAACE,iBAAiB;EACpCC,SAAS,EAAEH,KAAK,CAACI,YAAY;EAC7BC,OAAO,EAAE,CAAC;EACVC,eAAe,EAAEN,KAAK,CAACO;AACzB,CAAC,CAAC;AACF,MAAMC,kBAAkB,GAAGR,KAAK,IAAI;EAClC,MAAM;IACJS,cAAc;IACdC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAGZ,KAAK;EACT,OAAO;IACLa,OAAO,EAAE,GAAGjC,IAAI,CAAC6B,cAAc,CAAC,IAAI7B,IAAI,CAACgC,eAAe,CAAC,EAAE;IAC3DE,QAAQ,EAAEd,KAAK,CAACe,eAAe;IAC/BC,UAAU,EAAEN,YAAY;IACxBO,YAAY,EAAEN;EAChB,CAAC;AACH,CAAC;AACD,OAAO,MAAMO,kBAAkB,GAAGlB,KAAK,KAAK;EAC1Ca,OAAO,EAAE,GAAGjC,IAAI,CAACoB,KAAK,CAACmB,cAAc,CAAC,IAAIvC,IAAI,CAACoB,KAAK,CAACoB,eAAe,CAAC,EAAE;EACvEN,QAAQ,EAAEd,KAAK,CAACqB,eAAe;EAC/BJ,YAAY,EAAEjB,KAAK,CAACsB;AACtB,CAAC,CAAC;AACF,OAAO,MAAMC,kBAAkB,GAAGvB,KAAK,IAAIwB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;EACrEC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CAAC;EACXhB,OAAO,EAAE,GAAGjC,IAAI,CAACoB,KAAK,CAAC8B,YAAY,CAAC,IAAIlD,IAAI,CAACoB,KAAK,CAAC+B,aAAa,CAAC,EAAE;EACnEpC,KAAK,EAAEK,KAAK,CAACgC,SAAS;EACtBlB,QAAQ,EAAEd,KAAK,CAACiC,aAAa;EAC7BjB,UAAU,EAAEhB,KAAK,CAACgB,UAAU;EAC5BC,YAAY,EAAEjB,KAAK,CAACiB,YAAY;EAChCiB,UAAU,EAAE,OAAOlC,KAAK,CAACmC,iBAAiB;AAC5C,CAAC,EAAEzC,mBAAmB,CAACM,KAAK,CAACoC,oBAAoB,CAAC,CAAC,EAAE;EACnD;EACA,MAAM,EAAEZ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,kBAAkB,CAACR,KAAK,CAAC,CAAC;EACpD,MAAM,EAAEwB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,kBAAkB,CAAClB,KAAK,CAAC,CAAC;EACpD;EACA,uBAAuB,EAAE;IACvBqC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,kBAAkB,GAAGtC,KAAK,IAAI;EACzC,MAAM;IACJuC,YAAY;IACZC;EACF,CAAC,GAAGxC,KAAK;EACT,OAAO;IACL0B,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE,MAAM;IACba,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE,CAAC;IAChB;IACA,kBAAkB,EAAE;MAClBC,gBAAgB,EAAE3C,KAAK,CAAC4C,SAAS;MACjC,cAAc,EAAE;QACdD,gBAAgB,EAAE;MACpB;IACF,CAAC;IACD;IACA,CAAC,QAAQJ,YAAY,YAAYA,YAAY,cAAc,GAAGf,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,kBAAkB,CAACR,KAAK,CAAC,CAAC;IAC1G,CAAC,QAAQuC,YAAY,YAAYA,YAAY,cAAc,GAAGf,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,kBAAkB,CAAClB,KAAK,CAAC,CAAC;IAC1G;IACA,CAAC,QAAQwC,MAAM,kBAAkBA,MAAM,kBAAkB,GAAG;MAC1DK,MAAM,EAAE7C,KAAK,CAAC8C;IAChB,CAAC;IACD,CAAC,QAAQN,MAAM,kBAAkBA,MAAM,kBAAkB,GAAG;MAC1DK,MAAM,EAAE7C,KAAK,CAAC+C;IAChB,CAAC;IACD,CAAC,KAAKR,YAAY,EAAE,GAAG;MACrBZ,OAAO,EAAE,YAAY;MACrB,sCAAsC,EAAE;QACtCV,YAAY,EAAE;MAChB;IACF,CAAC;IACD,CAAC,GAAGsB,YAAY,QAAQ,GAAG;MACzB,iBAAiB,EAAE;QACjBZ,OAAO,EAAE,YAAY;QACrBC,KAAK,EAAE,CAAC;QACRoB,UAAU,EAAE,QAAQ;QACpBC,aAAa,EAAE,QAAQ;QACvB,sCAAsC,EAAE;UACtChC,YAAY,EAAE;QAChB;MACF,CAAC;MACD,YAAY,EAAE;QACZU,OAAO,EAAE;MACX,CAAC;MACD,SAAS,EAAE;QACTD,QAAQ,EAAE,UAAU;QACpBb,OAAO,EAAE,KAAKjC,IAAI,CAACoB,KAAK,CAAC+B,aAAa,CAAC,EAAE;QACzCpC,KAAK,EAAEK,KAAK,CAACgC,SAAS;QACtBkB,UAAU,EAAE,QAAQ;QACpBpC,QAAQ,EAAEd,KAAK,CAACiC,aAAa;QAC7BkB,SAAS,EAAE,QAAQ;QACnBlC,YAAY,EAAEjB,KAAK,CAACiB,YAAY;QAChCiB,UAAU,EAAE,OAAOlC,KAAK,CAACoD,kBAAkB,EAAE;QAC7CpC,UAAU,EAAE,CAAC;QACb;QACA,CAAC,GAAGwB,MAAM,SAAS,GAAG;UACpBa,MAAM,EAAE,GAAGzE,IAAI,CAACoB,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAAC8B,YAAY,CAAC,CAACyB,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAI7E,IAAI,CAACoB,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAAC+B,aAAa,CAAC,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UACjI,CAAC,IAAIjB,MAAM,sBAAsBA,MAAM,gCAAgCA,MAAM,2BAA2B,GAAG;YACzG,CAAC,GAAGA,MAAM,kBAAkB,GAAG;cAC7BlC,eAAe,EAAE,SAAS;cAC1BoD,MAAM,EAAE,GAAG9E,IAAI,CAACoB,KAAK,CAAC2D,SAAS,CAAC,IAAI3D,KAAK,CAAC4D,QAAQ,cAAc;cAChEzD,SAAS,EAAE;YACb;UACF;QACF,CAAC;QACD;QACA,CAAC,GAAGqC,MAAM,kBAAkB,GAAG;UAC7Ba,MAAM,EAAE,QAAQzE,IAAI,CAACoB,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAAC+B,aAAa,CAAC,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;UACvEnD,eAAe,EAAE,aAAa;UAC9B,CAAC,GAAGkC,MAAM,iBAAiB,GAAG;YAC5BW,SAAS,EAAE,OAAO;YAClBO,MAAM,EAAE,CAAC;YACTvD,SAAS,EAAE;UACb;QACF;MACF;IACF,CAAC;IACD,CAACoC,YAAY,GAAG;MACdX,KAAK,EAAE,MAAM;MACbiC,YAAY,EAAE,CAAC;MACfV,SAAS,EAAE,SAAS;MACpB,SAAS,EAAE;QACTW,MAAM,EAAE,CAAC;QACT;QACAC,oBAAoB,EAAE;MACxB,CAAC;MACD,SAAS,EAAE;QACTD,MAAM,EAAE,CAAC;QACTC,oBAAoB,EAAE,CAAC;QACvB,CAAC,GAAGxB,YAAY,uBAAuB,GAAG;UACxCuB,MAAM,EAAE;QACV;MACF;IACF,CAAC;IACD;IACA,CAAC,KAAKvB,YAAY,iBAAiBA,YAAY,0BAA0B,GAAG;MAC1EyB,oBAAoB,EAAE,CAAC;MACvBC,kBAAkB,EAAE,CAAC;MACrB;MACA,CAAC,GAAGzB,MAAM,WAAWA,MAAM,kBAAkB,GAAG;QAC9CwB,oBAAoB,EAAE,CAAC;QACvBC,kBAAkB,EAAE;MACtB;IACF,CAAC;IACD,CAAC,KAAK1B,YAAY,gBAAgB,GAAG;MACnC,CAAC,uBAAuBA,YAAY,EAAE,GAAG;QACvC2B,sBAAsB,EAAE,CAAC;QACzBC,oBAAoB,EAAE;MACxB,CAAC;MACD,CAAC,sBAAsB5B,YAAY,EAAE,GAAG;QACtCyB,oBAAoB,EAAE,CAAC;QACvBC,kBAAkB,EAAE;MACtB;IACF,CAAC;IACD,CAAC,KAAK1B,YAAY,gBAAgBA,YAAY,yBAAyB,GAAG;MACxE2B,sBAAsB,EAAE,CAAC;MACzBC,oBAAoB,EAAE,CAAC;MACvB;MACA,CAAC,GAAG3B,MAAM,WAAWA,MAAM,kBAAkB,GAAG;QAC9C0B,sBAAsB,EAAE,CAAC;QACzBC,oBAAoB,EAAE;MACxB;IACF,CAAC;IACD,CAAC,GAAG5B,YAAY,gBAAgB,GAAG;MACjC,oBAAoB,EAAE;QACpByB,oBAAoB,EAAE,CAAC;QACvBC,kBAAkB,EAAE,CAAC;QACrB,CAAC,GAAG1B,YAAY,WAAW,GAAG;UAC5B2B,sBAAsB,EAAElE,KAAK,CAACiB,YAAY;UAC1CkD,oBAAoB,EAAEnE,KAAK,CAACiB;QAC9B;MACF,CAAC;MACD,CAAC,wBAAwBsB,YAAY,6BAA6B,GAAG;QACnE2B,sBAAsB,EAAE,CAAC;QACzBC,oBAAoB,EAAE;MACxB;IACF,CAAC;IACD,CAAC,IAAI5B,YAAY,gBAAgB,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAC9DE,OAAO,EAAE;IACX,CAAC,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,GAAG0D,YAAY,iBAAiBA,YAAY,kBAAkBA,YAAY,EAAE,GAAG;QAC9E,sCAAsC,EAAE;UACtCwB,oBAAoB,EAAE/D,KAAK,CAAC2D,SAAS;UACrC,kBAAkB,EAAE;YAClBG,MAAM,EAAE;UACV;QACF;MACF,CAAC;MACD,OAAO,EAAE;QACPnC,OAAO,EAAE,aAAa;QACtByC,KAAK,EAAE,MAAM;QACbnB,aAAa,EAAE,KAAK;QACpB;QACAhC,YAAY,EAAE;MAChB,CAAC;MACD,CAAC;AACP,cAAcsB,YAAY;AAC1B,cAAcA,YAAY;AAC1B,cAAcC,MAAM;AACpB,OAAO,GAAG;QACFb,OAAO,EAAE;MACX,CAAC;MACD,wBAAwB,EAAE;QACxB0C,eAAe,EAAErE,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAAC2D,SAAS,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QAC5DM,oBAAoB,EAAE/D,KAAK,CAAC2D;MAC9B,CAAC;MACD;MACA,CAACpB,YAAY,GAAG;QACd6B,KAAK,EAAE;MACT,CAAC;MACD;MACA,CAAC,OAAO5B,MAAM,aAAaA,MAAM;AACvC,YAAYA,MAAM,yBAAyBD,YAAY;AACvD,YAAYC,MAAM,oBAAoBD,YAAY;AAClD,YAAYA,YAAY,kBAAkBA,YAAY,EAAE,GAAG;QACnDwB,oBAAoB,EAAE/D,KAAK,CAAC2D,SAAS;QACrC1C,YAAY,EAAE,CAAC;QACf,kBAAkB,EAAE;UAClB6C,MAAM,EAAE;QACV;MACF,CAAC;MACD,CAAC,OAAOtB,MAAM,iBAAiB,GAAG;QAChCsB,MAAM,EAAE;MACV,CAAC;MACD;MACA,CAAC,OAAOtB,MAAM,aAAaA,MAAM,eAAe,GAAG;QACjDsB,MAAM,EAAE,CAAC,CAAC;MACZ,CAAC;MACD,CAAC;AACP,YAAYtB,MAAM,yBAAyBA,MAAM;AACjD,YAAYA,MAAM,qCAAqCD,YAAY;AACnE,YAAYC,MAAM,gCAAgCD,YAAY,EAAE,GAAG;QAC3D2B,sBAAsB,EAAElE,KAAK,CAACiB,YAAY;QAC1CkD,oBAAoB,EAAEnE,KAAK,CAACiB;MAC9B,CAAC;MACD,CAAC;AACP,YAAYuB,MAAM,wBAAwBA,MAAM;AAChD,YAAYA,MAAM,+BAA+BD,YAAY;AAC7D,YAAYC,MAAM,uCAAuCD,YAAY,EAAE,GAAG;QAClEwB,oBAAoB,EAAE/D,KAAK,CAAC2D,SAAS;QACrCK,oBAAoB,EAAEhE,KAAK,CAACiB,YAAY;QACxCgD,kBAAkB,EAAEjE,KAAK,CAACiB;MAC5B,CAAC;MACD;MACA,CAAC,OAAOuB,MAAM,yBAAyBD,YAAY,EAAE,GAAG;QACtDU,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,GAAGV,YAAY,oBAAoBA,YAAY,gBAAgB,GAAG;QACjE+B,iBAAiB,EAAEtE,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAAC2D,SAAS,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QAC9D,CAAC,GAAGlB,YAAY,gBAAgB,GAAG;UACjCtB,YAAY,EAAE;QAChB;MACF,CAAC;MACD,CAAC,GAAGsB,YAAY,iCAAiC,GAAG;QAClD,CAAC,IAAIA,YAAY,aAAaA,YAAY,QAAQ,GAAG;UACnD,CAAC,OAAOA,YAAY,kBAAkBA,YAAY,gBAAgB,GAAG;YACnEtB,YAAY,EAAE;UAChB,CAAC;UACD,CAAC,OAAOsB,YAAY,EAAE,GAAG;YACvB2B,sBAAsB,EAAElE,KAAK,CAACiB,YAAY;YAC1C+C,oBAAoB,EAAE,CAAC;YACvBC,kBAAkB,EAAE,CAAC;YACrBE,oBAAoB,EAAEnE,KAAK,CAACiB;UAC9B;QACF;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAMsD,aAAa,GAAGvE,KAAK,IAAI;EACpC,MAAM;IACJuC,YAAY;IACZQ,eAAe;IACfY,SAAS;IACTL;EACF,CAAC,GAAGtD,KAAK;EACT,MAAMwE,yBAAyB,GAAG,EAAE;EACpC,MAAMC,iBAAiB,GAAGnB,IAAI,CAACP,eAAe,CAAC,CAAC2B,GAAG,CAACpB,IAAI,CAACK,SAAS,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAACkB,GAAG,CAACF,yBAAyB,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC,CAAClB,KAAK,CAAC,CAAC;EACzH,OAAO;IACL,CAAClB,YAAY,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3C,cAAc,CAACkB,KAAK,CAAC,CAAC,EAAEuB,kBAAkB,CAACvB,KAAK,CAAC,CAAC,EAAER,gBAAgB,CAACQ,KAAK,CAAC,CAAC,EAAEV,cAAc,CAACU,KAAK,CAAC,CAAC,EAAEZ,kBAAkB,CAACY,KAAK,CAAC,CAAC,EAAEP,kBAAkB,CAACO,KAAK,CAAC,CAAC,EAAE;MAClR,iBAAiB,EAAE;QACjB6C,MAAM,EAAE7C,KAAK,CAAC4E,aAAa;QAC3B,CAAC,IAAIrC,YAAY,KAAK,GAAG;UACvBM,MAAM,EAAE7C,KAAK,CAAC8C;QAChB,CAAC;QACD,CAAC,IAAIP,YAAY,KAAK,GAAG;UACvBM,MAAM,EAAEE,eAAe;UACvB8B,UAAU,EAAEJ,iBAAiB;UAC7BK,aAAa,EAAEL;QACjB;MACF,CAAC;MACD,6FAA6F,EAAE;QAC7FM,UAAU,EAAE;MACd;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMC,kBAAkB,GAAGhF,KAAK,IAAI;EAClC,MAAM;IACJuC;EACF,CAAC,GAAGvC,KAAK;EACT,OAAO;IACL;IACA,CAAC,GAAGuC,YAAY,aAAa,GAAG;MAC9Bc,MAAM,EAAE,CAAC;MACTxC,OAAO,EAAE,CAAC;MACVG,UAAU,EAAE,CAAC;MACbrB,KAAK,EAAEK,KAAK,CAACiF,mBAAmB;MAChCnE,QAAQ,EAAEd,KAAK,CAACkF,YAAY;MAC5BjC,aAAa,EAAE,CAAC,CAAC;MACjB;MACA;MACAkC,MAAM,EAAE,SAAS;MACjBjD,UAAU,EAAE,SAASlC,KAAK,CAACoD,kBAAkB,EAAE;MAC/CM,MAAM,EAAE,MAAM;MACdrD,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,aAAa;MAC9B,SAAS,EAAE;QACTX,KAAK,EAAEK,KAAK,CAACoF;MACf,CAAC;MACD,UAAU,EAAE;QACVzF,KAAK,EAAEK,KAAK,CAACgC;MACf,CAAC;MACD,UAAU,EAAE;QACVqD,UAAU,EAAE;MACd,CAAC;MACD,cAAc,EAAE;QACdhC,MAAM,EAAE,KAAKzE,IAAI,CAACoB,KAAK,CAACsF,iBAAiB,CAAC;MAC5C;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGvF,KAAK,IAAI;EACpC,MAAM;IACJuC,YAAY;IACZ+C,iBAAiB;IACjBE,oBAAoB;IACpBpC,kBAAkB;IAClBgC,SAAS;IACTK,cAAc;IACdC;EACF,CAAC,GAAG1F,KAAK;EACT,MAAM2F,QAAQ,GAAG,GAAGpD,YAAY,gBAAgB;EAChD,MAAMqD,gBAAgB,GAAG,GAAGrD,YAAY,yBAAyB;EACjE,OAAO;IACL,CAACoD,QAAQ,GAAGnE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,kBAAkB,CAACvB,KAAK,CAAC,CAAC,EAAE;MAClG2B,OAAO,EAAE,aAAa;MACtB,CAAC,SAASY,YAAY,kBAAkB,GAAG;QACzCuB,MAAM,EAAE,CAAC;QACT,CAAC,GAAGvB,YAAY,uBAAuB,GAAG;UACxCuB,MAAM,EAAE;QACV;MACF,CAAC;MACD,oBAAoB,EAAE;QACpBA,MAAM,EAAE;MACV,CAAC;MACD,CAAC,UAAUvB,YAAY,EAAE,GAAG;QAC1B1B,OAAO,EAAE;MACX,CAAC;MACD,CAAC,UAAU0B,YAAY,eAAeA,YAAY,EAAE,GAAG;QACrDzB,QAAQ,EAAE,SAAS;QACnB4C,MAAM,EAAE,MAAM;QACdzC,YAAY,EAAE,CAAC;QACfZ,OAAO,EAAE,MAAM;QACfwF,UAAU,EAAE,aAAa;QACzBlG,KAAK,EAAE,SAAS;QAChB,eAAe,EAAE;UACfgC,OAAO,EAAE;QACX,CAAC;QACD,SAAS,EAAE;UACTxB,SAAS,EAAE;QACb;MACF,CAAC;MACD,WAAW,EAAE;QACXwB,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,CAAC;QACRyD,UAAU,EAAE,QAAQ;QACpBS,OAAO,EAAE;MACX,CAAC;MACD,CAACvD,YAAY,GAAG;QACd,oBAAoB,EAAE;UACpBZ,OAAO,EAAE,MAAM;UACfoE,IAAI,EAAE,MAAM;UACZC,UAAU,EAAE,QAAQ;UACpB,sBAAsB,EAAE;YACtB3B,eAAe,EAAErE,KAAK,CAAC4C;UACzB;QACF,CAAC;QACD,qBAAqB,EAAE;UACrBjD,KAAK,EAAE6F,oBAAoB;UAC3BnD,SAAS,EAAE;QACb,CAAC;QACD,yBAAyB,EAAE;UACzBgC,eAAe,EAAErE,KAAK,CAACiG;QACzB,CAAC;QACD,UAAU,EAAE;UACV5B,eAAe,EAAEiB;QACnB,CAAC;QACD,UAAU,EAAE;UACVhB,iBAAiB,EAAEgB;QACrB;MACF;IACF,CAAC,CAAC,EAAEN,kBAAkB,CAAChF,KAAK,CAAC,CAAC,EAAE;MAC9B;MACA,CAAC,GAAG0F,OAAO,GAAGnD,YAAY,gBAAgB,GAAG;QAC3C5C,KAAK,EAAEyF,SAAS;QAChBD,MAAM,EAAE,SAAS;QACjBjD,UAAU,EAAE,OAAOkB,kBAAkB,EAAE;QACvC,SAAS,EAAE;UACTzD,KAAK,EAAE8F;QACT;MACF;IACF,CAAC,CAAC;IACF;IACA,CAAC,GAAGlD,YAAY,aAAa,GAAG;MAC9BtB,YAAY,EAAE;IAChB,CAAC;IACD,CAAC2E,gBAAgB,GAAG;MAClB;MACA,CAAC,GAAGF,OAAO,GAAGnD,YAAY,gBAAgB,GAAG;QAC3C5C,KAAK,EAAEyF,SAAS;QAChBD,MAAM,EAAE,aAAa;QACrB,SAAS,EAAE;UACTxF,KAAK,EAAEyF;QACT;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMc,aAAa,GAAGlG,KAAK,IAAI;EAC7B,MAAM;IACJuC,YAAY;IACZ5B,cAAc;IACdW;EACF,CAAC,GAAGtB,KAAK;EACT,OAAO;IACL,CAAC,GAAGuC,YAAY,QAAQ,GAAGf,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3C,cAAc,CAACkB,KAAK,CAAC,CAAC,EAAEsC,kBAAkB,CAACtC,KAAK,CAAC,CAAC,EAAE;MAC3H,OAAO,EAAE;QACPqC,SAAS,EAAE;MACb,CAAC;MACD,WAAW,EAAEb,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QACrDE,OAAO,EAAE,cAAc;QACvBC,KAAK,EAAE,MAAM;QACbuB,SAAS,EAAE,OAAO;QAClBF,aAAa,EAAE,KAAK;QACpB,OAAO,EAAE;UACPZ,SAAS,EAAE;QACb,CAAC;QACD;QACA,MAAM,EAAE;UACN,CAAC,GAAGE,YAAY,cAAc,GAAG;YAC/BtB,YAAY,EAAEN,cAAc;YAC5BG,QAAQ,EAAEd,KAAK,CAACe;UAClB;QACF,CAAC;QACD,MAAM,EAAE;UACN,CAAC,GAAGwB,YAAY,cAAc,GAAG;YAC/BtB,YAAY,EAAEK;UAChB;QACF;MACF,CAAC,EAAE/B,qBAAqB,CAACS,KAAK,CAAC,CAAC,EAAEX,mBAAmB,CAACW,KAAK,CAAC,CAAC,EAAE;QAC7D;QACA;QACA;QACA;QACA;QACA;QACA;QACA,CAAC,SAASuC,YAAY,4BAA4BA,YAAY,sBAAsBA,YAAY,eAAe,GAAG;UAChH,CAAC,GAAGA,YAAY,KAAKA,YAAY,cAAc,GAAG;YAChDtB,YAAY,EAAE;UAChB;QACF,CAAC;QACD,CAAC,SAASsB,YAAY,sBAAsBA,YAAY,qBAAqB,GAAG;UAC9E,CAAC,GAAGA,YAAY,KAAKA,YAAY,cAAc,GAAG;YAChDyB,oBAAoB,EAAE,CAAC;YACvBC,kBAAkB,EAAE;UACtB;QACF,CAAC;QACD,CAAC,SAAS1B,YAAY,uBAAuBA,YAAY,oBAAoB,GAAG;UAC9E,CAAC,GAAGA,YAAY,KAAKA,YAAY,cAAc,GAAG;YAChD2B,sBAAsB,EAAE,CAAC;YACzBC,oBAAoB,EAAE;UACxB;QACF,CAAC;QACD;QACA;QACA,CAAC,SAAS5B,YAAY,sBAAsBA,YAAY,eAAe,GAAG;UACxE,CAAC,GAAGA,YAAY,gBAAgB,GAAG;YACjCyB,oBAAoB,EAAE,CAAC;YACvBC,kBAAkB,EAAE;UACtB;QACF,CAAC;QACD;QACA;QACA,CAAC,SAAS1B,YAAY,uBAAuBA,YAAY,eAAe,GAAG;UACzE,CAAC,GAAGA,YAAY,gBAAgB,GAAG;YACjC2B,sBAAsB,EAAE,CAAC;YACzBC,oBAAoB,EAAE;UACxB;QACF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMgC,mBAAmB,GAAGnG,KAAK,IAAI;EACnC,MAAM;IACJuC,YAAY;IACZC;EACF,CAAC,GAAGxC,KAAK;EACT,MAAMoG,eAAe,GAAG,GAAG7D,YAAY,SAAS;EAChD,OAAO;IACL,CAAC6D,eAAe,GAAG;MACjB,CAAC7D,YAAY,GAAG;QACd,kBAAkB,EAAE;UAClB,CAAC,KAAKA,YAAY,gBAAgB6D,eAAe,eAAe5D,MAAM,2BAA2BA,MAAM,oBAAoB,GAAG;YAC5H6D,sBAAsB,EAAErG,KAAK,CAACsG;UAChC;QACF;MACF,CAAC;MACD,CAAC,GAAG/D,YAAY,gBAAgB,GAAG;QACjCM,MAAM,EAAE7C,KAAK,CAAC4E,aAAa;QAC3B3D,YAAY,EAAE;MAChB,CAAC;MACD;MACA;MACA,CAAC,GAAGsB,YAAY,KAAK,GAAG;QACtBvB,UAAU,EAAEhB,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAACU,YAAY,CAAC,CAACgE,GAAG,CAAC,MAAM,CAAC,CAACjB,KAAK,CAAC;MAC/D,CAAC;MACD,CAAC,KAAKlB,YAAY,QAAQ,GAAG;QAC3B,CAAC,KAAKA,YAAY,yBAAyB,GAAG;UAC5CgE,gBAAgB,EAAE,CAAC,CAAC;UACpB1F,OAAO,EAAE,CAAC;UACV6C,MAAM,EAAE,CAAC;UACT,CAAC,GAAG0C,eAAe,SAAS,GAAG;YAC7B;YACA/B,eAAe,EAAE,CAAC,CAAC;YACnBH,sBAAsB,EAAE,CAAC;YACzBC,oBAAoB,EAAE,CAAC;YACvBhE,SAAS,EAAE;UACb,CAAC;UACD,CAAC,GAAGiG,eAAe,eAAe5D,MAAM,qBAAqB,GAAG;YAC9D7C,KAAK,EAAEK,KAAK,CAACwF,oBAAoB;YACjC,SAAS,EAAE;cACT7F,KAAK,EAAEK,KAAK,CAACsG;YACf,CAAC;YACD,UAAU,EAAE;cACV3G,KAAK,EAAEK,KAAK,CAACwG;YACf,CAAC;YACD,CAAC,IAAIhE,MAAM,sBAAsB,GAAG;cAClCiE,KAAK,EAAE;YACT;UACF;QACF;MACF,CAAC;MACD,CAAC,GAAGL,eAAe,SAAS,GAAG;QAC7BvD,MAAM,EAAE7C,KAAK,CAAC4E,aAAa;QAC3B,kBAAkB,EAAE;UAClBd,MAAM,EAAE;QACV;MACF,CAAC;MACD,SAAS,EAAE;QACT,CAAC,GAAGvB,YAAY,mBAAmB6D,eAAe,SAAS,GAAG;UAC5DvD,MAAM,EAAE7C,KAAK,CAAC8C;QAChB;MACF,CAAC;MACD,SAAS,EAAE;QACT,CAAC,GAAGP,YAAY,mBAAmB6D,eAAe,SAAS,GAAG;UAC5DvD,MAAM,EAAE7C,KAAK,CAAC+C;QAChB;MACF,CAAC;MACD,OAAO,EAAE;QACPV,SAAS,EAAE;MACb,CAAC;MACD;MACA,CAAC,IAAIE,YAAY,eAAe,GAAG;QACjC,CAAC,SAASA,YAAY,qBAAqB,GAAG;UAC5C,CAAC,GAAGA,YAAY,cAAc,GAAG;YAC/B,CAAC,GAAGA,YAAY,gBAAgB,GAAG;cACjC8B,eAAe,EAAErE,KAAK,CAACsD,IAAI,CAACtD,KAAK,CAAC2D,SAAS,CAAC,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;cAC5DxC,YAAY,EAAE;YAChB;UACF;QACF,CAAC;QACD,CAAC,SAASsB,YAAY,sBAAsB,GAAG;UAC7C,CAAC,GAAGA,YAAY,IAAIA,YAAY,gBAAgB,GAAG;YACjDtB,YAAY,EAAE;UAChB;QACF,CAAC;QACD,CAAC,KAAKsB,YAAY,gBAAgBA,YAAY;AACtD,YAAYA,YAAY;AACxB,UAAUA,YAAY,gBAAgB,GAAG;UAC/B,4BAA4B,EAAE;YAC5BuB,MAAM,EAAE;UACV;QACF,CAAC;QACD,CAAC,KAAKvB,YAAY,wBAAwB,GAAG;UAC3CuB,MAAM,EAAE;QACV;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,MAAM4C,aAAa,GAAG1G,KAAK,IAAI;EAC7B,MAAM;IACJuC;EACF,CAAC,GAAGvC,KAAK;EACT,OAAO;IACL,CAAC,GAAGuC,YAAY,eAAe,GAAG;MAChC,CAAC,2BAA2BA,YAAY,uBAAuBA,YAAY,aAAa,GAAG;QACzF5C,KAAK,EAAEK,KAAK,CAAC2G;MACf;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMC,cAAc,GAAG5H,aAAa,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAEgB,KAAK,IAAI;EACxE,MAAM6G,UAAU,GAAG5H,UAAU,CAACe,KAAK,EAAEb,cAAc,CAACa,KAAK,CAAC,CAAC;EAC3D,OAAO,CAACuE,aAAa,CAACsC,UAAU,CAAC,EAAEtB,aAAa,CAACsB,UAAU,CAAC,CAAC;AAC/D,CAAC,EAAE3H,kBAAkB,EAAE;EACrB4H,SAAS,EAAE;AACb,CAAC,CAAC;AACF,eAAe9H,aAAa,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,EAAEgB,KAAK,IAAI;EAC5D,MAAM6G,UAAU,GAAG5H,UAAU,CAACe,KAAK,EAAEb,cAAc,CAACa,KAAK,CAAC,CAAC;EAC3D,OAAO,CAACkG,aAAa,CAACW,UAAU,CAAC,EAAEV,mBAAmB,CAACU,UAAU,CAAC,EAAEH,aAAa,CAACG,UAAU,CAAC;EAC7F;EACA;EACA;EACA9H,mBAAmB,CAAC8H,UAAU,CAAC,CAAC;AAClC,CAAC,EAAE3H,kBAAkB,EAAE;EACrB4H,SAAS,EAAE;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}