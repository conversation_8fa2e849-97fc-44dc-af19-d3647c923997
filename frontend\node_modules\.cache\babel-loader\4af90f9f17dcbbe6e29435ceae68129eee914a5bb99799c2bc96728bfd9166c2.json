{"ast": null, "code": "import React from 'react';\nexport const PanelPickerContext = /*#__PURE__*/React.createContext({});\nexport const PanelPresetsContext = /*#__PURE__*/React.createContext({});", "map": {"version": 3, "names": ["React", "PanelPickerContext", "createContext", "PanelPresetsContext"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/color-picker/context.js"], "sourcesContent": ["import React from 'react';\nexport const PanelPickerContext = /*#__PURE__*/React.createContext({});\nexport const PanelPresetsContext = /*#__PURE__*/React.createContext({});"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,kBAAkB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACtE,OAAO,MAAMC,mBAAmB,GAAG,aAAaH,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}