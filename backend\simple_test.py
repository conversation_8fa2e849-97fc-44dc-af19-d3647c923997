#!/usr/bin/env python3
"""
简化的工作流功能测试
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_condition_parser():
    """测试条件解析器"""
    print("=== 测试条件解析器 ===")
    
    try:
        from app.utils.condition_parser import ConditionParser
        
        cp = ConditionParser()
        
        test_conditions = [
            ('success', {'status': 'success'}),
            ('failed', {'status': 'failed'}),
            ('status == "success"', {'status': 'success'}),
            ('duration < 60', {'duration': 30}),
        ]
        
        for condition, context in test_conditions:
            result = cp.parse_condition(condition, context)
            print(f"条件: {condition}, 上下文: {context}, 结果: {result}")
            
        print("条件解析器测试完成")
        
    except Exception as e:
        print(f"条件解析器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_dependency_manager():
    """测试依赖管理器"""
    print("\n=== 测试依赖管理器 ===")
    
    try:
        from app.utils.dependency_manager import DependencyManager
        
        nodes = [
            {'id': 'task1', 'name': '任务1'},
            {'id': 'task2', 'name': '任务2'},
        ]
        
        edges = [
            {'source': 'task1', 'target': 'task2'},
        ]
        
        dm = DependencyManager()
        is_valid, message = dm.validate_workflow(nodes, edges)
        print(f"工作流验证: {is_valid}, {message}")
        
        if is_valid:
            execution_plan = dm.get_execution_plan(nodes, edges)
            print(f"执行计划: {execution_plan}")
            
        print("依赖管理器测试完成")
        
    except Exception as e:
        print(f"依赖管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_parameter_manager():
    """测试参数管理器"""
    print("\n=== 测试参数管理器 ===")
    
    try:
        from app.utils.parameter_manager import ParameterManager, ParameterType
        
        pm = ParameterManager()
        
        # 测试参数验证
        is_valid, message = pm.validate_parameter('hello', ParameterType.STRING, {'min_length': 3})
        print(f"参数验证: 'hello' -> {is_valid}, {message}")
        
        # 测试参数引用解析
        parameters = {'input_file': '${previous_output}', 'count': 10}
        context = {'previous_output': '/tmp/data.txt'}
        resolved = pm.resolve_parameter_references(parameters, context)
        print(f"参数引用解析: {parameters} -> {resolved}")
        
        print("参数管理器测试完成")
        
    except Exception as e:
        print(f"参数管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("开始简化测试...")
    test_condition_parser()
    test_dependency_manager()
    test_parameter_manager()
    print("\n=== 简化测试完成 ===")
