{"ast": null, "code": "\"use client\";\n\nimport Skeleton from './Skeleton';\nexport default Skeleton;", "map": {"version": 3, "names": ["Skeleton"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/skeleton/index.js"], "sourcesContent": ["\"use client\";\n\nimport Skeleton from './Skeleton';\nexport default Skeleton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,YAAY;AACjC,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}