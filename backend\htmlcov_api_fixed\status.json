{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "0dc39e671e2b7b11856fe7a5ba8dc043", "files": {"z_a44f0ac069e85531_mock_app_py": {"hash": "278a2580067008e8e02efce5337bce75", "index": {"url": "z_a44f0ac069e85531_mock_app_py.html", "file": "tests\\mock_app.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 270, "n_excluded": 0, "n_missing": 142, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}