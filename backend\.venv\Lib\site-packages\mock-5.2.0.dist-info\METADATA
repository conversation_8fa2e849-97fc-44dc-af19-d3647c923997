Metadata-Version: 2.2
Name: mock
Version: 5.2.0
Summary: Rolling backport of unittest.mock for all Pythons
Home-page: http://mock.readthedocs.org/en/latest/
Author: Testing Cabal
Author-email: <EMAIL>
Project-URL: Source, https://github.com/testing-cabal/mock
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: Testing
Requires-Python: >=3.6
License-File: LICENSE.txt
Provides-Extra: docs
Requires-Dist: sphinx; extra == "docs"
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Provides-Extra: build
Requires-Dist: twine; extra == "build"
Requires-Dist: wheel; extra == "build"
Requires-Dist: blurb; extra == "build"
Dynamic: description

mock is a library for testing in Python. It allows you to replace parts of
your system under test with mock objects and make assertions about how they
have been used.

mock is now part of the Python standard library, available as `unittest.mock
<https://docs.python.org/dev/library/unittest.mock.html>`_ in Python 3.3
onwards.

This package contains a rolling backport of the standard library mock code
compatible with Python 3.6 and up.

Please see the standard library documentation for more details.

:Homepage: `Mock Homepage`_
:Download: `Mock on PyPI`_
:Documentation: `Python Docs`_
:License: `BSD License`_
:Support: `Mailing list (<EMAIL>)
 <http://lists.idyll.org/listinfo/testing-in-python>`_
:Code: `GitHub
 <https://github.com/testing-cabal/mock>`_
:Issue tracker: `GitHub Issues
 <https://github.com/testing-cabal/mock/issues>`_
:Build status:
    |CircleCI|_ |Docs|_

    .. |CircleCI| image:: https://circleci.com/gh/testing-cabal/mock/tree/master.svg?style=shield
    .. _CircleCI: https://circleci.com/gh/testing-cabal/mock/tree/master

    .. |Docs| image:: https://readthedocs.org/projects/mock/badge/?version=latest
    .. _Docs: http://mock.readthedocs.org/en/latest/

.. _Mock Homepage: http://mock.readthedocs.org/en/latest/
.. _BSD License: https://github.com/testing-cabal/mock/blob/master/LICENSE.txt
.. _Python Docs: https://docs.python.org/dev/library/unittest.mock.html
.. _mock on PyPI: https://pypi.org/project/mock/
