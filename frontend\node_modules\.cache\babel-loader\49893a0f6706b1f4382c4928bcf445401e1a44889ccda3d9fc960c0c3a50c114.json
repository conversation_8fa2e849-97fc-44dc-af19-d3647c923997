{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport TimelineItem from './TimelineItem';\nconst TimelineItemList = _a => {\n  var {\n      prefixCls,\n      className,\n      pending = false,\n      children,\n      items,\n      rootClassName,\n      reverse = false,\n      direction,\n      hashId,\n      pendingDot,\n      mode = ''\n    } = _a,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"pending\", \"children\", \"items\", \"rootClassName\", \"reverse\", \"direction\", \"hashId\", \"pendingDot\", \"mode\"]);\n  const getPositionCls = (position, idx) => {\n    if (mode === 'alternate') {\n      if (position === 'right') return `${prefixCls}-item-right`;\n      if (position === 'left') return `${prefixCls}-item-left`;\n      return idx % 2 === 0 ? `${prefixCls}-item-left` : `${prefixCls}-item-right`;\n    }\n    if (mode === 'left') return `${prefixCls}-item-left`;\n    if (mode === 'right') return `${prefixCls}-item-right`;\n    if (position === 'right') return `${prefixCls}-item-right`;\n    return '';\n  };\n  const mergedItems = _toConsumableArray(items || []);\n  const pendingNode = typeof pending === 'boolean' ? null : pending;\n  if (pending) {\n    mergedItems.push({\n      pending: !!pending,\n      dot: pendingDot || /*#__PURE__*/React.createElement(LoadingOutlined, null),\n      children: pendingNode\n    });\n  }\n  if (reverse) {\n    mergedItems.reverse();\n  }\n  const itemsCount = mergedItems.length;\n  const lastCls = `${prefixCls}-item-last`;\n  const itemsList = mergedItems.filter(item => !!item).map((item, idx) => {\n    var _a;\n    const pendingClass = idx === itemsCount - 2 ? lastCls : '';\n    const readyClass = idx === itemsCount - 1 ? lastCls : '';\n    const {\n        className: itemClassName\n      } = item,\n      itemProps = __rest(item, [\"className\"]);\n    return /*#__PURE__*/React.createElement(TimelineItem, Object.assign({}, itemProps, {\n      className: classNames([itemClassName, !reverse && !!pending ? pendingClass : readyClass, getPositionCls((_a = item === null || item === void 0 ? void 0 : item.position) !== null && _a !== void 0 ? _a : '', idx)]),\n      key: (item === null || item === void 0 ? void 0 : item.key) || idx\n    }));\n  });\n  const hasLabelItem = mergedItems.some(item => !!(item === null || item === void 0 ? void 0 : item.label));\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-pending`]: !!pending,\n    [`${prefixCls}-reverse`]: !!reverse,\n    [`${prefixCls}-${mode}`]: !!mode && !hasLabelItem,\n    [`${prefixCls}-label`]: hasLabelItem,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId);\n  return /*#__PURE__*/React.createElement(\"ol\", Object.assign({}, restProps, {\n    className: classString\n  }), itemsList);\n};\nexport default TimelineItemList;", "map": {"version": 3, "names": ["_toConsumableArray", "__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "LoadingOutlined", "classNames", "TimelineItem", "TimelineItemList", "_a", "prefixCls", "className", "pending", "children", "items", "rootClassName", "reverse", "direction", "hashId", "pendingDot", "mode", "restProps", "getPositionCls", "position", "idx", "mergedItems", "pendingNode", "push", "dot", "createElement", "itemsCount", "lastCls", "itemsList", "filter", "item", "map", "pendingClass", "readyClass", "itemClassName", "itemProps", "assign", "key", "hasLabelItem", "some", "label", "classString"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/timeline/TimelineItemList.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport TimelineItem from './TimelineItem';\nconst TimelineItemList = _a => {\n  var {\n      prefixCls,\n      className,\n      pending = false,\n      children,\n      items,\n      rootClassName,\n      reverse = false,\n      direction,\n      hashId,\n      pendingDot,\n      mode = ''\n    } = _a,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"pending\", \"children\", \"items\", \"rootClassName\", \"reverse\", \"direction\", \"hashId\", \"pendingDot\", \"mode\"]);\n  const getPositionCls = (position, idx) => {\n    if (mode === 'alternate') {\n      if (position === 'right') return `${prefixCls}-item-right`;\n      if (position === 'left') return `${prefixCls}-item-left`;\n      return idx % 2 === 0 ? `${prefixCls}-item-left` : `${prefixCls}-item-right`;\n    }\n    if (mode === 'left') return `${prefixCls}-item-left`;\n    if (mode === 'right') return `${prefixCls}-item-right`;\n    if (position === 'right') return `${prefixCls}-item-right`;\n    return '';\n  };\n  const mergedItems = _toConsumableArray(items || []);\n  const pendingNode = typeof pending === 'boolean' ? null : pending;\n  if (pending) {\n    mergedItems.push({\n      pending: !!pending,\n      dot: pendingDot || /*#__PURE__*/React.createElement(LoadingOutlined, null),\n      children: pendingNode\n    });\n  }\n  if (reverse) {\n    mergedItems.reverse();\n  }\n  const itemsCount = mergedItems.length;\n  const lastCls = `${prefixCls}-item-last`;\n  const itemsList = mergedItems.filter(item => !!item).map((item, idx) => {\n    var _a;\n    const pendingClass = idx === itemsCount - 2 ? lastCls : '';\n    const readyClass = idx === itemsCount - 1 ? lastCls : '';\n    const {\n        className: itemClassName\n      } = item,\n      itemProps = __rest(item, [\"className\"]);\n    return /*#__PURE__*/React.createElement(TimelineItem, Object.assign({}, itemProps, {\n      className: classNames([itemClassName, !reverse && !!pending ? pendingClass : readyClass, getPositionCls((_a = item === null || item === void 0 ? void 0 : item.position) !== null && _a !== void 0 ? _a : '', idx)]),\n      key: (item === null || item === void 0 ? void 0 : item.key) || idx\n    }));\n  });\n  const hasLabelItem = mergedItems.some(item => !!(item === null || item === void 0 ? void 0 : item.label));\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-pending`]: !!pending,\n    [`${prefixCls}-reverse`]: !!reverse,\n    [`${prefixCls}-${mode}`]: !!mode && !hasLabelItem,\n    [`${prefixCls}-label`]: hasLabelItem,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId);\n  return /*#__PURE__*/React.createElement(\"ol\", Object.assign({}, restProps, {\n    className: classString\n  }), itemsList);\n};\nexport default TimelineItemList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,MAAMC,gBAAgB,GAAGC,EAAE,IAAI;EAC7B,IAAI;MACAC,SAAS;MACTC,SAAS;MACTC,OAAO,GAAG,KAAK;MACfC,QAAQ;MACRC,KAAK;MACLC,aAAa;MACbC,OAAO,GAAG,KAAK;MACfC,SAAS;MACTC,MAAM;MACNC,UAAU;MACVC,IAAI,GAAG;IACT,CAAC,GAAGX,EAAE;IACNY,SAAS,GAAG/B,MAAM,CAACmB,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;EAC7J,MAAMa,cAAc,GAAGA,CAACC,QAAQ,EAAEC,GAAG,KAAK;IACxC,IAAIJ,IAAI,KAAK,WAAW,EAAE;MACxB,IAAIG,QAAQ,KAAK,OAAO,EAAE,OAAO,GAAGb,SAAS,aAAa;MAC1D,IAAIa,QAAQ,KAAK,MAAM,EAAE,OAAO,GAAGb,SAAS,YAAY;MACxD,OAAOc,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,GAAGd,SAAS,YAAY,GAAG,GAAGA,SAAS,aAAa;IAC7E;IACA,IAAIU,IAAI,KAAK,MAAM,EAAE,OAAO,GAAGV,SAAS,YAAY;IACpD,IAAIU,IAAI,KAAK,OAAO,EAAE,OAAO,GAAGV,SAAS,aAAa;IACtD,IAAIa,QAAQ,KAAK,OAAO,EAAE,OAAO,GAAGb,SAAS,aAAa;IAC1D,OAAO,EAAE;EACX,CAAC;EACD,MAAMe,WAAW,GAAGpC,kBAAkB,CAACyB,KAAK,IAAI,EAAE,CAAC;EACnD,MAAMY,WAAW,GAAG,OAAOd,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO;EACjE,IAAIA,OAAO,EAAE;IACXa,WAAW,CAACE,IAAI,CAAC;MACff,OAAO,EAAE,CAAC,CAACA,OAAO;MAClBgB,GAAG,EAAET,UAAU,IAAI,aAAaf,KAAK,CAACyB,aAAa,CAACxB,eAAe,EAAE,IAAI,CAAC;MAC1EQ,QAAQ,EAAEa;IACZ,CAAC,CAAC;EACJ;EACA,IAAIV,OAAO,EAAE;IACXS,WAAW,CAACT,OAAO,CAAC,CAAC;EACvB;EACA,MAAMc,UAAU,GAAGL,WAAW,CAACvB,MAAM;EACrC,MAAM6B,OAAO,GAAG,GAAGrB,SAAS,YAAY;EACxC,MAAMsB,SAAS,GAAGP,WAAW,CAACQ,MAAM,CAACC,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,CAACC,GAAG,CAAC,CAACD,IAAI,EAAEV,GAAG,KAAK;IACtE,IAAIf,EAAE;IACN,MAAM2B,YAAY,GAAGZ,GAAG,KAAKM,UAAU,GAAG,CAAC,GAAGC,OAAO,GAAG,EAAE;IAC1D,MAAMM,UAAU,GAAGb,GAAG,KAAKM,UAAU,GAAG,CAAC,GAAGC,OAAO,GAAG,EAAE;IACxD,MAAM;QACFpB,SAAS,EAAE2B;MACb,CAAC,GAAGJ,IAAI;MACRK,SAAS,GAAGjD,MAAM,CAAC4C,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC;IACzC,OAAO,aAAa9B,KAAK,CAACyB,aAAa,CAACtB,YAAY,EAAEZ,MAAM,CAAC6C,MAAM,CAAC,CAAC,CAAC,EAAED,SAAS,EAAE;MACjF5B,SAAS,EAAEL,UAAU,CAAC,CAACgC,aAAa,EAAE,CAACtB,OAAO,IAAI,CAAC,CAACJ,OAAO,GAAGwB,YAAY,GAAGC,UAAU,EAAEf,cAAc,CAAC,CAACb,EAAE,GAAGyB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACX,QAAQ,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAEe,GAAG,CAAC,CAAC,CAAC;MACpNiB,GAAG,EAAE,CAACP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACO,GAAG,KAAKjB;IACjE,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,MAAMkB,YAAY,GAAGjB,WAAW,CAACkB,IAAI,CAACT,IAAI,IAAI,CAAC,EAAEA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACU,KAAK,CAAC,CAAC;EACzG,MAAMC,WAAW,GAAGvC,UAAU,CAACI,SAAS,EAAE;IACxC,CAAC,GAAGA,SAAS,UAAU,GAAG,CAAC,CAACE,OAAO;IACnC,CAAC,GAAGF,SAAS,UAAU,GAAG,CAAC,CAACM,OAAO;IACnC,CAAC,GAAGN,SAAS,IAAIU,IAAI,EAAE,GAAG,CAAC,CAACA,IAAI,IAAI,CAACsB,YAAY;IACjD,CAAC,GAAGhC,SAAS,QAAQ,GAAGgC,YAAY;IACpC,CAAC,GAAGhC,SAAS,MAAM,GAAGO,SAAS,KAAK;EACtC,CAAC,EAAEN,SAAS,EAAEI,aAAa,EAAEG,MAAM,CAAC;EACpC,OAAO,aAAad,KAAK,CAACyB,aAAa,CAAC,IAAI,EAAElC,MAAM,CAAC6C,MAAM,CAAC,CAAC,CAAC,EAAEnB,SAAS,EAAE;IACzEV,SAAS,EAAEkC;EACb,CAAC,CAAC,EAAEb,SAAS,CAAC;AAChB,CAAC;AACD,eAAexB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}