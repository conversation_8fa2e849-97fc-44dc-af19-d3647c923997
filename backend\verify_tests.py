#!/usr/bin/env python3
"""
测试验证脚本 - 验证所有测试功能是否正常工作
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试核心模块导入"""
    print("📦 测试模块导入...")
    
    try:
        from app.utils.dependency_manager import DependencyManager
        from app.utils.condition_parser import ConditionParser
        from app.utils.parameter_manager import ParameterManager, ParameterType
        from app.utils.execution_manager import ExecutionManager, TaskNode
        print("  ✅ 所有核心模块导入成功")
        return True
    except ImportError as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False

def test_dependency_manager():
    """测试依赖管理器"""
    print("\n🔗 测试依赖管理器...")
    
    try:
        from app.utils.dependency_manager import DependencyManager
        
        dm = DependencyManager()
        
        # 测试基本功能
        dm.add_dependency('task2', 'task1')
        dm.add_dependency('task3', 'task2')
        
        # 测试循环检测
        assert not dm.has_cycle(), "不应该有循环依赖"
        
        # 测试拓扑排序
        order = dm.topological_sort()
        assert len(order) == 3, f"应该有3个层级，实际有{len(order)}"
        assert order[0] == ['task1'], f"第一层应该是task1，实际是{order[0]}"
        
        # 测试循环依赖检测
        dm.add_dependency('task1', 'task3')  # 形成循环
        assert dm.has_cycle(), "应该检测到循环依赖"
        
        print("  ✅ 依赖管理器所有功能正常")
        return True
    except Exception as e:
        print(f"  ❌ 依赖管理器测试失败: {e}")
        return False

def test_condition_parser():
    """测试条件解析器"""
    print("\n🎯 测试条件解析器...")
    
    try:
        from app.utils.condition_parser import ConditionParser
        
        parser = ConditionParser()
        
        # 测试预定义条件
        test_cases = [
            ('success', {'status': 'success'}, True),
            ('failed', {'status': 'failed'}, True),
            ('always', {}, True),
            ('success', {'status': 'failed'}, False),
        ]
        
        for condition, context, expected in test_cases:
            result = parser.parse_condition(condition, context)
            assert result == expected, f"条件 '{condition}' 期望 {expected}，实际 {result}"
        
        # 测试自定义条件
        assert parser.parse_condition('status == "success"', {'status': 'success'}) == True
        assert parser.parse_condition('duration < 60', {'duration': 30}) == True
        
        # 测试条件验证
        is_valid, _ = parser.validate_condition('success')
        assert is_valid, "预定义条件应该有效"
        
        is_valid, _ = parser.validate_condition('invalid syntax ==')
        assert not is_valid, "无效语法应该被检测"
        
        print("  ✅ 条件解析器所有功能正常")
        return True
    except Exception as e:
        print(f"  ❌ 条件解析器测试失败: {e}")
        return False

def test_parameter_manager():
    """测试参数管理器"""
    print("\n🔧 测试参数管理器...")
    
    try:
        from app.utils.parameter_manager import ParameterManager, ParameterType
        
        manager = ParameterManager()
        
        # 测试类型转换
        assert manager.convert_parameter("42", ParameterType.INTEGER) == 42
        assert manager.convert_parameter("3.14", ParameterType.FLOAT) == 3.14
        assert manager.convert_parameter("true", ParameterType.BOOLEAN) == True
        assert manager.convert_parameter("hello", ParameterType.STRING) == "hello"
        
        # 测试参数验证
        is_valid, _ = manager.validate_parameter("test", ParameterType.STRING, {'min_length': 3})
        assert is_valid, "有效参数应该通过验证"
        
        is_valid, _ = manager.validate_parameter("hi", ParameterType.STRING, {'min_length': 3})
        assert not is_valid, "无效参数应该被拒绝"
        
        # 测试参数引用解析
        parameters = {"file": "${input}", "size": 100}
        context = {"input": "/tmp/data.csv"}
        resolved = manager.resolve_parameter_references(parameters, context)
        assert resolved["file"] == "/tmp/data.csv", "参数引用应该被正确解析"
        
        print("  ✅ 参数管理器所有功能正常")
        return True
    except Exception as e:
        print(f"  ❌ 参数管理器测试失败: {e}")
        return False

def test_execution_manager():
    """测试执行管理器"""
    print("\n⚡ 测试执行管理器...")
    
    try:
        from app.utils.execution_manager import ExecutionManager, TaskNode, ExecutionMode
        
        manager = ExecutionManager(max_workers=2)
        
        # 测试基本属性
        assert manager.max_workers == 2, "工作线程数应该正确设置"
        assert len(manager.task_results) == 0, "初始任务结果应该为空"
        
        # 测试任务节点创建
        node = TaskNode(
            id="test_task",
            name="测试任务",
            task_id=1,
            execution_mode=ExecutionMode.SERIAL
        )
        assert node.id == "test_task", "节点ID应该正确设置"
        assert node.name == "测试任务", "节点名称应该正确设置"
        assert node.execution_mode == ExecutionMode.SERIAL, "执行模式应该正确设置"
        
        # 测试回调机制
        callback_called = False
        def test_callback(task_id, status, result):
            nonlocal callback_called
            callback_called = True
        
        manager.add_status_callback(test_callback)
        assert test_callback in manager.status_callbacks, "回调应该被正确添加"
        
        # 清理资源
        manager.cleanup()
        
        print("  ✅ 执行管理器所有功能正常")
        return True
    except Exception as e:
        print(f"  ❌ 执行管理器测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔄 测试集成功能...")
    
    try:
        from app.utils.dependency_manager import DependencyManager
        from app.utils.condition_parser import ConditionParser
        from app.utils.parameter_manager import ParameterManager, ParameterType
        
        # 创建一个简单的工作流验证场景
        dm = DependencyManager()
        parser = ConditionParser()
        param_manager = ParameterManager()
        
        # 设置工作流
        dm.add_dependency('process', 'extract')
        dm.add_dependency('report', 'process')
        
        # 验证工作流
        assert not dm.has_cycle(), "工作流不应该有循环"
        order = dm.topological_sort()
        assert len(order) == 3, "应该有3个执行层级"
        
        # 验证条件
        assert parser.parse_condition('success', {'status': 'success'}), "成功条件应该正确评估"
        
        # 验证参数
        params = {"count": "100", "format": "json"}
        converted_count = param_manager.convert_parameter(params["count"], ParameterType.INTEGER)
        assert converted_count == 100, "参数转换应该正确"
        
        print("  ✅ 集成功能正常")
        return True
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 测试验证开始")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖管理器", test_dependency_manager),
        ("条件解析器", test_condition_parser),
        ("参数管理器", test_parameter_manager),
        ("执行管理器", test_execution_manager),
        ("集成功能", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"  ❌ {test_name} 测试失败")
        except Exception as e:
            print(f"  💥 {test_name} 测试出现异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试验证通过！")
        print("\n✅ 验证的功能:")
        print("  - 依赖管理: 循环检测、拓扑排序、执行计划")
        print("  - 条件解析: 预定义条件、自定义表达式、安全验证")
        print("  - 参数管理: 类型转换、验证、引用解析")
        print("  - 执行管理: 并发控制、状态管理、回调机制")
        print("  - 集成功能: 多模块协同工作")
        print("\n🎯 测试框架状态: 完全可用")
        print("📚 使用建议:")
        print("  - 日常验证: python verify_tests.py")
        print("  - 功能演示: python test_demo.py")
        print("  - 简化测试: python run_simple_tests.py")
        return True
    else:
        print("❌ 部分测试验证失败")
        print("\n💡 建议:")
        print("  1. 检查Python环境和依赖")
        print("  2. 确保在backend目录下运行")
        print("  3. 查看具体错误信息进行调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
