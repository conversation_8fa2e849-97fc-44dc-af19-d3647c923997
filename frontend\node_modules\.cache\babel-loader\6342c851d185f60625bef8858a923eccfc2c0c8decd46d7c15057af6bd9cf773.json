{"ast": null, "code": "var _excluded = [\"children\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport * as React from 'react';\nimport { createContext, useCallback, useContext, useEffect } from 'react';\nimport { SetCartesianGraphicalItem } from '../state/SetGraphicalItem';\nimport { useIsPanorama } from './PanoramaContext';\nvar noop = () => {};\nvar ErrorBarDirectionDispatchContext = /*#__PURE__*/createContext({\n  addErrorBar: noop,\n  removeErrorBar: noop\n});\nvar initialContextState = {\n  data: [],\n  xAxisId: 'xAxis-0',\n  yAxisId: 'yAxis-0',\n  dataPointFormatter: () => ({\n    x: 0,\n    y: 0,\n    value: 0\n  }),\n  errorBarOffset: 0\n};\nvar ErrorBarContext = /*#__PURE__*/createContext(initialContextState);\nexport function SetErrorBarContext(props) {\n  var {\n      children\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(ErrorBarContext.Provider, {\n    value: rest\n  }, children);\n}\nexport var useErrorBarContext = () => useContext(ErrorBarContext);\nexport var CartesianGraphicalItemContext = _ref => {\n  var {\n    children,\n    xAxisId,\n    yAxisId,\n    zAxisId,\n    dataKey,\n    data,\n    stackId,\n    hide,\n    type,\n    barSize\n  } = _ref;\n  var [errorBars, updateErrorBars] = React.useState([]);\n  // useCallback is necessary in these two because without it, the new function reference causes an infinite render loop\n  var addErrorBar = useCallback(errorBar => {\n    updateErrorBars(prev => [...prev, errorBar]);\n  }, [updateErrorBars]);\n  var removeErrorBar = useCallback(errorBar => {\n    updateErrorBars(prev => prev.filter(eb => eb !== errorBar));\n  }, [updateErrorBars]);\n  var isPanorama = useIsPanorama();\n  return /*#__PURE__*/React.createElement(ErrorBarDirectionDispatchContext.Provider, {\n    value: {\n      addErrorBar,\n      removeErrorBar\n    }\n  }, /*#__PURE__*/React.createElement(SetCartesianGraphicalItem, {\n    type: type,\n    data: data,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    zAxisId: zAxisId,\n    dataKey: dataKey,\n    errorBars: errorBars,\n    stackId: stackId,\n    hide: hide,\n    barSize: barSize,\n    isPanorama: isPanorama\n  }), children);\n};\nexport function ReportErrorBarSettings(props) {\n  var {\n    addErrorBar,\n    removeErrorBar\n  } = useContext(ErrorBarDirectionDispatchContext);\n  useEffect(() => {\n    addErrorBar(props);\n    return () => {\n      removeErrorBar(props);\n    };\n  }, [addErrorBar, removeErrorBar, props]);\n  return null;\n}", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "React", "createContext", "useCallback", "useContext", "useEffect", "SetCartesianGraphicalItem", "useIsPanorama", "noop", "ErrorBarDirectionDispatchContext", "addErrorBar", "removeErrorBar", "initialContextState", "data", "xAxisId", "yAxisId", "dataPointFormatter", "x", "y", "value", "errorBarOffset", "ErrorBarContext", "SetErrorBarContext", "props", "children", "rest", "createElement", "Provider", "useErrorBarContext", "CartesianGraphicalItemContext", "_ref", "zAxisId", "dataKey", "stackId", "hide", "type", "barSize", "errorBars", "updateErrorBars", "useState", "errorBar", "prev", "filter", "eb", "isPanorama", "ReportErrorBarSettings"], "sources": ["E:/code1/task3/frontend/node_modules/recharts/es6/context/CartesianGraphicalItemContext.js"], "sourcesContent": ["var _excluded = [\"children\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nimport * as React from 'react';\nimport { createContext, useCallback, useContext, useEffect } from 'react';\nimport { SetCartesianGraphicalItem } from '../state/SetGraphicalItem';\nimport { useIsPanorama } from './PanoramaContext';\nvar noop = () => {};\nvar ErrorBarDirectionDispatchContext = /*#__PURE__*/createContext({\n  addErrorBar: noop,\n  removeErrorBar: noop\n});\nvar initialContextState = {\n  data: [],\n  xAxisId: 'xAxis-0',\n  yAxisId: 'yAxis-0',\n  dataPointFormatter: () => ({\n    x: 0,\n    y: 0,\n    value: 0\n  }),\n  errorBarOffset: 0\n};\nvar ErrorBarContext = /*#__PURE__*/createContext(initialContextState);\nexport function SetErrorBarContext(props) {\n  var {\n      children\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(ErrorBarContext.Provider, {\n    value: rest\n  }, children);\n}\nexport var useErrorBarContext = () => useContext(ErrorBarContext);\nexport var CartesianGraphicalItemContext = _ref => {\n  var {\n    children,\n    xAxisId,\n    yAxisId,\n    zAxisId,\n    dataKey,\n    data,\n    stackId,\n    hide,\n    type,\n    barSize\n  } = _ref;\n  var [errorBars, updateErrorBars] = React.useState([]);\n  // useCallback is necessary in these two because without it, the new function reference causes an infinite render loop\n  var addErrorBar = useCallback(errorBar => {\n    updateErrorBars(prev => [...prev, errorBar]);\n  }, [updateErrorBars]);\n  var removeErrorBar = useCallback(errorBar => {\n    updateErrorBars(prev => prev.filter(eb => eb !== errorBar));\n  }, [updateErrorBars]);\n  var isPanorama = useIsPanorama();\n  return /*#__PURE__*/React.createElement(ErrorBarDirectionDispatchContext.Provider, {\n    value: {\n      addErrorBar,\n      removeErrorBar\n    }\n  }, /*#__PURE__*/React.createElement(SetCartesianGraphicalItem, {\n    type: type,\n    data: data,\n    xAxisId: xAxisId,\n    yAxisId: yAxisId,\n    zAxisId: zAxisId,\n    dataKey: dataKey,\n    errorBars: errorBars,\n    stackId: stackId,\n    hide: hide,\n    barSize: barSize,\n    isPanorama: isPanorama\n  }), children);\n};\nexport function ReportErrorBarSettings(props) {\n  var {\n    addErrorBar,\n    removeErrorBar\n  } = useContext(ErrorBarDirectionDispatchContext);\n  useEffect(() => {\n    addErrorBar(props);\n    return () => {\n      removeErrorBar(props);\n    };\n  }, [addErrorBar, removeErrorBar, props]);\n  return null;\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,OAAO,KAAKa,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AACzE,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,aAAa,QAAQ,mBAAmB;AACjD,IAAIC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACnB,IAAIC,gCAAgC,GAAG,aAAaP,aAAa,CAAC;EAChEQ,WAAW,EAAEF,IAAI;EACjBG,cAAc,EAAEH;AAClB,CAAC,CAAC;AACF,IAAII,mBAAmB,GAAG;EACxBC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,kBAAkB,EAAEA,CAAA,MAAO;IACzBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,KAAK,EAAE;EACT,CAAC,CAAC;EACFC,cAAc,EAAE;AAClB,CAAC;AACD,IAAIC,eAAe,GAAG,aAAanB,aAAa,CAACU,mBAAmB,CAAC;AACrE,OAAO,SAASU,kBAAkBA,CAACC,KAAK,EAAE;EACxC,IAAI;MACAC;IACF,CAAC,GAAGD,KAAK;IACTE,IAAI,GAAGvC,wBAAwB,CAACqC,KAAK,EAAEtC,SAAS,CAAC;EACnD,OAAO,aAAagB,KAAK,CAACyB,aAAa,CAACL,eAAe,CAACM,QAAQ,EAAE;IAChER,KAAK,EAAEM;EACT,CAAC,EAAED,QAAQ,CAAC;AACd;AACA,OAAO,IAAII,kBAAkB,GAAGA,CAAA,KAAMxB,UAAU,CAACiB,eAAe,CAAC;AACjE,OAAO,IAAIQ,6BAA6B,GAAGC,IAAI,IAAI;EACjD,IAAI;IACFN,QAAQ;IACRV,OAAO;IACPC,OAAO;IACPgB,OAAO;IACPC,OAAO;IACPnB,IAAI;IACJoB,OAAO;IACPC,IAAI;IACJC,IAAI;IACJC;EACF,CAAC,GAAGN,IAAI;EACR,IAAI,CAACO,SAAS,EAAEC,eAAe,CAAC,GAAGrC,KAAK,CAACsC,QAAQ,CAAC,EAAE,CAAC;EACrD;EACA,IAAI7B,WAAW,GAAGP,WAAW,CAACqC,QAAQ,IAAI;IACxCF,eAAe,CAACG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,QAAQ,CAAC,CAAC;EAC9C,CAAC,EAAE,CAACF,eAAe,CAAC,CAAC;EACrB,IAAI3B,cAAc,GAAGR,WAAW,CAACqC,QAAQ,IAAI;IAC3CF,eAAe,CAACG,IAAI,IAAIA,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKH,QAAQ,CAAC,CAAC;EAC7D,CAAC,EAAE,CAACF,eAAe,CAAC,CAAC;EACrB,IAAIM,UAAU,GAAGrC,aAAa,CAAC,CAAC;EAChC,OAAO,aAAaN,KAAK,CAACyB,aAAa,CAACjB,gCAAgC,CAACkB,QAAQ,EAAE;IACjFR,KAAK,EAAE;MACLT,WAAW;MACXC;IACF;EACF,CAAC,EAAE,aAAaV,KAAK,CAACyB,aAAa,CAACpB,yBAAyB,EAAE;IAC7D6B,IAAI,EAAEA,IAAI;IACVtB,IAAI,EAAEA,IAAI;IACVC,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBgB,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBK,SAAS,EAAEA,SAAS;IACpBJ,OAAO,EAAEA,OAAO;IAChBC,IAAI,EAAEA,IAAI;IACVE,OAAO,EAAEA,OAAO;IAChBQ,UAAU,EAAEA;EACd,CAAC,CAAC,EAAEpB,QAAQ,CAAC;AACf,CAAC;AACD,OAAO,SAASqB,sBAAsBA,CAACtB,KAAK,EAAE;EAC5C,IAAI;IACFb,WAAW;IACXC;EACF,CAAC,GAAGP,UAAU,CAACK,gCAAgC,CAAC;EAChDJ,SAAS,CAAC,MAAM;IACdK,WAAW,CAACa,KAAK,CAAC;IAClB,OAAO,MAAM;MACXZ,cAAc,CAACY,KAAK,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACb,WAAW,EAAEC,cAAc,EAAEY,KAAK,CAAC,CAAC;EACxC,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}