#!/usr/bin/env python3
"""
测试运行脚本
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_unit_tests(coverage=True, verbose=True):
    """运行单元测试"""
    print("🧪 运行单元测试...")

    cmd = ["python", "-m", "pytest"]

    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term-missing"])

    if verbose:
        cmd.append("-v")

    # 只运行单元测试
    cmd.extend(["-m", "unit"])

    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode == 0

def run_all_tests(coverage=True, verbose=True):
    """运行所有测试"""
    print("🚀 运行所有测试...")

    cmd = ["python", "-m", "pytest"]

    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=90"
        ])

    if verbose:
        cmd.append("-v")

    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode == 0

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="运行测试套件")
    parser.add_argument("--no-coverage", action="store_true", help="禁用覆盖率报告")
    parser.add_argument("--quiet", action="store_true", help="静默模式")

    args = parser.parse_args()

    coverage = not args.no_coverage
    verbose = not args.quiet

    success = run_all_tests(coverage, verbose)

    if success:
        print("✅ 所有测试通过")
    else:
        print("❌ 测试失败")
        sys.exit(1)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 根据测试结果设置退出码
    sys.exit(0 if result.wasSuccessful() else 1)