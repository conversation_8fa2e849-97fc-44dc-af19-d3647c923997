#!/usr/bin/env python3
"""
测试运行脚本
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_unit_tests(coverage=True, verbose=True):
    """运行单元测试"""
    print("🧪 运行单元测试...")

    cmd = ["python", "-m", "pytest"]

    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term-missing"])

    if verbose:
        cmd.append("-v")

    # 只运行单元测试
    cmd.extend(["-m", "unit"])

    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode == 0

def run_all_tests(coverage=True, verbose=True):
    """运行所有测试"""
    print("🚀 运行所有测试...")

    cmd = ["python", "-m", "pytest"]

    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-fail-under=90"
        ])

    if verbose:
        cmd.append("-v")

    result = subprocess.run(cmd, cwd=Path(__file__).parent)
    return result.returncode == 0

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="运行测试套件")
    parser.add_argument("--coverage", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--no-coverage", action="store_true", help="禁用覆盖率报告")
    parser.add_argument("--quiet", action="store_true", help="静默模式")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--file", "-f", help="运行特定测试文件")
    parser.add_argument("--test", "-t", help="运行特定测试方法")

    args = parser.parse_args()

    # 处理覆盖率参数
    if args.coverage:
        coverage = True
    elif args.no_coverage:
        coverage = False
    else:
        coverage = True  # 默认启用覆盖率

    # 处理详细输出参数
    if args.verbose:
        verbose = True
    elif args.quiet:
        verbose = False
    else:
        verbose = True  # 默认详细输出

    # 如果指定了特定文件或测试，使用pytest直接运行
    if args.file or args.test:
        cmd = ["python", "-m", "pytest"]

        if args.file:
            cmd.append(args.file)

        if args.test:
            cmd.extend(["-k", args.test])

        if coverage:
            cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term-missing"])

        if verbose:
            cmd.append("-v")

        result = subprocess.run(cmd, cwd=Path(__file__).parent)
        success = result.returncode == 0
    else:
        success = run_all_tests(coverage, verbose)

    if success:
        print("✅ 所有测试通过")
        if coverage:
            print("📊 覆盖率报告: htmlcov/index.html")
    else:
        print("❌ 测试失败")
        sys.exit(1)