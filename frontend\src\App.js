import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import TaskList from './pages/TaskList';
import TaskWorkflow from './pages/TaskWorkflow';
import ScriptList from './pages/ScriptList';
import Settings from './pages/Settings';
import BasicSettings from './pages/BasicSettings';
import LogSettings from './pages/LogSettings';
import SecuritySettings from './pages/SecuritySettings';
import Dashboard from './pages/Dashboard';
import './App.css';

const { Content, Footer } = Layout;

function App() {
  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sidebar />
        <Layout>
          <Header />
          <Content style={{ margin: '16px' }}>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/tasks" element={<TaskList />} />
              <Route path="/workflows" element={<TaskWorkflow />} />
              <Route path="/scripts" element={<ScriptList />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="/basic-settings" element={<BasicSettings />} />
              <Route path="/log-settings" element={<LogSettings />} />
              <Route path="/security-settings" element={<SecuritySettings />} />
            </Routes>
          </Content>
          <Footer style={{ textAlign: 'center' }}>
            任务管理系统 ©2025 Created by Trae AI
          </Footer>
        </Layout>
      </Layout>
    </Router>
  );
}

export default App;