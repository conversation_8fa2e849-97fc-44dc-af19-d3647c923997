# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

ENVIRONMENT_VARIABLES = [
    "CI",
    "CI_COMMIT_REF_NAME",
    "CI_COMMIT_REF_SLUG",
    "CI_COMMIT_SHA",
    "CI_COMMIT_TAG",
    "CI_DEBUG_TRACE",
    "CI_ENVIRONMENT_NAME",
    "CI_ENVIRONMENT_SLUG",
    "CI_JOB_ID",
    "CI_JOB_MANUAL",
    "CI_JOB_NAME",
    "CI_JOB_STAGE",
    "CI_RUNNER_DESCRIPTION",
    "CI_RUNNER_ID",
    "CI_RUNNER_TAGS",
    "CI_PIPELINE_ID",
    "CI_PIPELINE_TRIGGERED",
    "CI_PROJECT_DIR",
    "CI_PROJECT_ID",
    "CI_PROJECT_NAME",
    "CI_PROJECT_NAMESPACE",
    "CI_PROJECT_PATH",
    "CI_PROJECT_URL",
    "CI_REGISTRY",
    "CI_REGISTRY_IMAGE",
    "CI_REGISTRY_USER",
    "CI_SERVER",
    "CI_SERVER_NAME",
    "CI_SERVER_REVISION",
    "CI_SERVER_VERSION",
    "ARTIFACT_DOWNLOAD_ATTEMPTS",
    "GET_SOURCES_ATTEMPTS",
    "GITLAB_CI",
    "GITLAB_USER_ID",
    "GITLAB_USER_EMAIL",
    "RESTORE_CACHE_ATTEMPTS",
]
