{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"name\", \"initialValues\", \"fields\", \"form\", \"preserve\", \"children\", \"component\", \"validateMessages\", \"validateTrigger\", \"onValuesChange\", \"onFieldsChange\", \"onFinish\", \"onFinishFailed\", \"clearOnDestroy\"];\nimport * as React from 'react';\nimport useForm from \"./useForm\";\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport FormContext from \"./FormContext\";\nimport { isSimilar } from \"./utils/valueUtil\";\nimport ListContext from \"./ListContext\";\nvar Form = function Form(_ref, ref) {\n  var name = _ref.name,\n    initialValues = _ref.initialValues,\n    fields = _ref.fields,\n    form = _ref.form,\n    preserve = _ref.preserve,\n    children = _ref.children,\n    _ref$component = _ref.component,\n    Component = _ref$component === void 0 ? 'form' : _ref$component,\n    validateMessages = _ref.validateMessages,\n    _ref$validateTrigger = _ref.validateTrigger,\n    validateTrigger = _ref$validateTrigger === void 0 ? 'onChange' : _ref$validateTrigger,\n    onValuesChange = _ref.onValuesChange,\n    _onFieldsChange = _ref.onFieldsChange,\n    _onFinish = _ref.onFinish,\n    onFinishFailed = _ref.onFinishFailed,\n    clearOnDestroy = _ref.clearOnDestroy,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var nativeElementRef = React.useRef(null);\n  var formContext = React.useContext(FormContext);\n\n  // We customize handle event since Context will makes all the consumer re-render:\n  // https://reactjs.org/docs/context.html#contextprovider\n  var _useForm = useForm(form),\n    _useForm2 = _slicedToArray(_useForm, 1),\n    formInstance = _useForm2[0];\n  var _getInternalHooks = formInstance.getInternalHooks(HOOK_MARK),\n    useSubscribe = _getInternalHooks.useSubscribe,\n    setInitialValues = _getInternalHooks.setInitialValues,\n    setCallbacks = _getInternalHooks.setCallbacks,\n    setValidateMessages = _getInternalHooks.setValidateMessages,\n    setPreserve = _getInternalHooks.setPreserve,\n    destroyForm = _getInternalHooks.destroyForm;\n\n  // Pass ref with form instance\n  React.useImperativeHandle(ref, function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      nativeElement: nativeElementRef.current\n    });\n  });\n\n  // Register form into Context\n  React.useEffect(function () {\n    formContext.registerForm(name, formInstance);\n    return function () {\n      formContext.unregisterForm(name);\n    };\n  }, [formContext, formInstance, name]);\n\n  // Pass props to store\n  setValidateMessages(_objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages));\n  setCallbacks({\n    onValuesChange: onValuesChange,\n    onFieldsChange: function onFieldsChange(changedFields) {\n      formContext.triggerFormChange(name, changedFields);\n      if (_onFieldsChange) {\n        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n        _onFieldsChange.apply(void 0, [changedFields].concat(rest));\n      }\n    },\n    onFinish: function onFinish(values) {\n      formContext.triggerFormFinish(name, values);\n      if (_onFinish) {\n        _onFinish(values);\n      }\n    },\n    onFinishFailed: onFinishFailed\n  });\n  setPreserve(preserve);\n\n  // Set initial value, init store value when first mount\n  var mountRef = React.useRef(null);\n  setInitialValues(initialValues, !mountRef.current);\n  if (!mountRef.current) {\n    mountRef.current = true;\n  }\n  React.useEffect(function () {\n    return function () {\n      return destroyForm(clearOnDestroy);\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n\n  // Prepare children by `children` type\n  var childrenNode;\n  var childrenRenderProps = typeof children === 'function';\n  if (childrenRenderProps) {\n    var _values = formInstance.getFieldsValue(true);\n    childrenNode = children(_values, formInstance);\n  } else {\n    childrenNode = children;\n  }\n\n  // Not use subscribe when using render props\n  useSubscribe(!childrenRenderProps);\n\n  // Listen if fields provided. We use ref to save prev data here to avoid additional render\n  var prevFieldsRef = React.useRef();\n  React.useEffect(function () {\n    if (!isSimilar(prevFieldsRef.current || [], fields || [])) {\n      formInstance.setFields(fields || []);\n    }\n    prevFieldsRef.current = fields;\n  }, [fields, formInstance]);\n  var formContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      validateTrigger: validateTrigger\n    });\n  }, [formInstance, validateTrigger]);\n  var wrapperNode = /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: formContextValue\n  }, childrenNode));\n  if (Component === false) {\n    return wrapperNode;\n  }\n  return /*#__PURE__*/React.createElement(Component, _extends({}, restProps, {\n    ref: nativeElementRef,\n    onSubmit: function onSubmit(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      formInstance.submit();\n    },\n    onReset: function onReset(event) {\n      var _restProps$onReset;\n      event.preventDefault();\n      formInstance.resetFields();\n      (_restProps$onReset = restProps.onReset) === null || _restProps$onReset === void 0 || _restProps$onReset.call(restProps, event);\n    }\n  }), wrapperNode);\n};\nexport default Form;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "useForm", "FieldContext", "HOOK_MARK", "FormContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ListContext", "Form", "_ref", "ref", "name", "initialValues", "fields", "form", "preserve", "children", "_ref$component", "component", "Component", "validateMessages", "_ref$validateTrigger", "validate<PERSON><PERSON>ger", "onValuesChange", "_onFields<PERSON>hange", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_onFinish", "onFinish", "onFinishFailed", "clearOnD<PERSON>roy", "restProps", "nativeElementRef", "useRef", "formContext", "useContext", "_useForm", "_useForm2", "formInstance", "_getInternalHooks", "getInternalHooks", "useSubscribe", "setInitialValues", "setCallbacks", "setValidateMessages", "setPreserve", "destroyForm", "useImperativeHandle", "nativeElement", "current", "useEffect", "registerForm", "unregisterForm", "changed<PERSON>ields", "triggerForm<PERSON>hange", "_len", "arguments", "length", "rest", "Array", "_key", "apply", "concat", "values", "triggerFormFinish", "mountRef", "childrenNode", "childrenRenderProps", "_values", "getFieldsValue", "prevFieldsRef", "setFields", "formContextValue", "useMemo", "wrapperNode", "createElement", "Provider", "value", "onSubmit", "event", "preventDefault", "stopPropagation", "submit", "onReset", "_restProps$onReset", "resetFields", "call"], "sources": ["E:/code1/task3/frontend/node_modules/rc-field-form/es/Form.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"name\", \"initialValues\", \"fields\", \"form\", \"preserve\", \"children\", \"component\", \"validateMessages\", \"validateTrigger\", \"onValuesChange\", \"onFieldsChange\", \"onFinish\", \"onFinishFailed\", \"clearOnDestroy\"];\nimport * as React from 'react';\nimport useForm from \"./useForm\";\nimport FieldContext, { HOOK_MARK } from \"./FieldContext\";\nimport FormContext from \"./FormContext\";\nimport { isSimilar } from \"./utils/valueUtil\";\nimport ListContext from \"./ListContext\";\nvar Form = function Form(_ref, ref) {\n  var name = _ref.name,\n    initialValues = _ref.initialValues,\n    fields = _ref.fields,\n    form = _ref.form,\n    preserve = _ref.preserve,\n    children = _ref.children,\n    _ref$component = _ref.component,\n    Component = _ref$component === void 0 ? 'form' : _ref$component,\n    validateMessages = _ref.validateMessages,\n    _ref$validateTrigger = _ref.validateTrigger,\n    validateTrigger = _ref$validateTrigger === void 0 ? 'onChange' : _ref$validateTrigger,\n    onValuesChange = _ref.onValuesChange,\n    _onFieldsChange = _ref.onFieldsChange,\n    _onFinish = _ref.onFinish,\n    onFinishFailed = _ref.onFinishFailed,\n    clearOnDestroy = _ref.clearOnDestroy,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var nativeElementRef = React.useRef(null);\n  var formContext = React.useContext(FormContext);\n\n  // We customize handle event since Context will makes all the consumer re-render:\n  // https://reactjs.org/docs/context.html#contextprovider\n  var _useForm = useForm(form),\n    _useForm2 = _slicedToArray(_useForm, 1),\n    formInstance = _useForm2[0];\n  var _getInternalHooks = formInstance.getInternalHooks(HOOK_MARK),\n    useSubscribe = _getInternalHooks.useSubscribe,\n    setInitialValues = _getInternalHooks.setInitialValues,\n    setCallbacks = _getInternalHooks.setCallbacks,\n    setValidateMessages = _getInternalHooks.setValidateMessages,\n    setPreserve = _getInternalHooks.setPreserve,\n    destroyForm = _getInternalHooks.destroyForm;\n\n  // Pass ref with form instance\n  React.useImperativeHandle(ref, function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      nativeElement: nativeElementRef.current\n    });\n  });\n\n  // Register form into Context\n  React.useEffect(function () {\n    formContext.registerForm(name, formInstance);\n    return function () {\n      formContext.unregisterForm(name);\n    };\n  }, [formContext, formInstance, name]);\n\n  // Pass props to store\n  setValidateMessages(_objectSpread(_objectSpread({}, formContext.validateMessages), validateMessages));\n  setCallbacks({\n    onValuesChange: onValuesChange,\n    onFieldsChange: function onFieldsChange(changedFields) {\n      formContext.triggerFormChange(name, changedFields);\n      if (_onFieldsChange) {\n        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n        _onFieldsChange.apply(void 0, [changedFields].concat(rest));\n      }\n    },\n    onFinish: function onFinish(values) {\n      formContext.triggerFormFinish(name, values);\n      if (_onFinish) {\n        _onFinish(values);\n      }\n    },\n    onFinishFailed: onFinishFailed\n  });\n  setPreserve(preserve);\n\n  // Set initial value, init store value when first mount\n  var mountRef = React.useRef(null);\n  setInitialValues(initialValues, !mountRef.current);\n  if (!mountRef.current) {\n    mountRef.current = true;\n  }\n  React.useEffect(function () {\n    return function () {\n      return destroyForm(clearOnDestroy);\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n\n  // Prepare children by `children` type\n  var childrenNode;\n  var childrenRenderProps = typeof children === 'function';\n  if (childrenRenderProps) {\n    var _values = formInstance.getFieldsValue(true);\n    childrenNode = children(_values, formInstance);\n  } else {\n    childrenNode = children;\n  }\n\n  // Not use subscribe when using render props\n  useSubscribe(!childrenRenderProps);\n\n  // Listen if fields provided. We use ref to save prev data here to avoid additional render\n  var prevFieldsRef = React.useRef();\n  React.useEffect(function () {\n    if (!isSimilar(prevFieldsRef.current || [], fields || [])) {\n      formInstance.setFields(fields || []);\n    }\n    prevFieldsRef.current = fields;\n  }, [fields, formInstance]);\n  var formContextValue = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, formInstance), {}, {\n      validateTrigger: validateTrigger\n    });\n  }, [formInstance, validateTrigger]);\n  var wrapperNode = /*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(FieldContext.Provider, {\n    value: formContextValue\n  }, childrenNode));\n  if (Component === false) {\n    return wrapperNode;\n  }\n  return /*#__PURE__*/React.createElement(Component, _extends({}, restProps, {\n    ref: nativeElementRef,\n    onSubmit: function onSubmit(event) {\n      event.preventDefault();\n      event.stopPropagation();\n      formInstance.submit();\n    },\n    onReset: function onReset(event) {\n      var _restProps$onReset;\n      event.preventDefault();\n      formInstance.resetFields();\n      (_restProps$onReset = restProps.onReset) === null || _restProps$onReset === void 0 || _restProps$onReset.call(restProps, event);\n    }\n  }), wrapperNode);\n};\nexport default Form;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;AAC3N,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,YAAY,IAAIC,SAAS,QAAQ,gBAAgB;AACxD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAClC,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAClBC,aAAa,GAAGH,IAAI,CAACG,aAAa;IAClCC,MAAM,GAAGJ,IAAI,CAACI,MAAM;IACpBC,IAAI,GAAGL,IAAI,CAACK,IAAI;IAChBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,cAAc,GAAGR,IAAI,CAACS,SAAS;IAC/BC,SAAS,GAAGF,cAAc,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,cAAc;IAC/DG,gBAAgB,GAAGX,IAAI,CAACW,gBAAgB;IACxCC,oBAAoB,GAAGZ,IAAI,CAACa,eAAe;IAC3CA,eAAe,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,oBAAoB;IACrFE,cAAc,GAAGd,IAAI,CAACc,cAAc;IACpCC,eAAe,GAAGf,IAAI,CAACgB,cAAc;IACrCC,SAAS,GAAGjB,IAAI,CAACkB,QAAQ;IACzBC,cAAc,GAAGnB,IAAI,CAACmB,cAAc;IACpCC,cAAc,GAAGpB,IAAI,CAACoB,cAAc;IACpCC,SAAS,GAAG/B,wBAAwB,CAACU,IAAI,EAAET,SAAS,CAAC;EACvD,IAAI+B,gBAAgB,GAAG9B,KAAK,CAAC+B,MAAM,CAAC,IAAI,CAAC;EACzC,IAAIC,WAAW,GAAGhC,KAAK,CAACiC,UAAU,CAAC7B,WAAW,CAAC;;EAE/C;EACA;EACA,IAAI8B,QAAQ,GAAGjC,OAAO,CAACY,IAAI,CAAC;IAC1BsB,SAAS,GAAGtC,cAAc,CAACqC,QAAQ,EAAE,CAAC,CAAC;IACvCE,YAAY,GAAGD,SAAS,CAAC,CAAC,CAAC;EAC7B,IAAIE,iBAAiB,GAAGD,YAAY,CAACE,gBAAgB,CAACnC,SAAS,CAAC;IAC9DoC,YAAY,GAAGF,iBAAiB,CAACE,YAAY;IAC7CC,gBAAgB,GAAGH,iBAAiB,CAACG,gBAAgB;IACrDC,YAAY,GAAGJ,iBAAiB,CAACI,YAAY;IAC7CC,mBAAmB,GAAGL,iBAAiB,CAACK,mBAAmB;IAC3DC,WAAW,GAAGN,iBAAiB,CAACM,WAAW;IAC3CC,WAAW,GAAGP,iBAAiB,CAACO,WAAW;;EAE7C;EACA5C,KAAK,CAAC6C,mBAAmB,CAACpC,GAAG,EAAE,YAAY;IACzC,OAAOb,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MACxDU,aAAa,EAAEhB,gBAAgB,CAACiB;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA/C,KAAK,CAACgD,SAAS,CAAC,YAAY;IAC1BhB,WAAW,CAACiB,YAAY,CAACvC,IAAI,EAAE0B,YAAY,CAAC;IAC5C,OAAO,YAAY;MACjBJ,WAAW,CAACkB,cAAc,CAACxC,IAAI,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACsB,WAAW,EAAEI,YAAY,EAAE1B,IAAI,CAAC,CAAC;;EAErC;EACAgC,mBAAmB,CAAC9C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoC,WAAW,CAACb,gBAAgB,CAAC,EAAEA,gBAAgB,CAAC,CAAC;EACrGsB,YAAY,CAAC;IACXnB,cAAc,EAAEA,cAAc;IAC9BE,cAAc,EAAE,SAASA,cAAcA,CAAC2B,aAAa,EAAE;MACrDnB,WAAW,CAACoB,iBAAiB,CAAC1C,IAAI,EAAEyC,aAAa,CAAC;MAClD,IAAI5B,eAAe,EAAE;QACnB,KAAK,IAAI8B,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QAClC;QACAnC,eAAe,CAACoC,KAAK,CAAC,KAAK,CAAC,EAAE,CAACR,aAAa,CAAC,CAACS,MAAM,CAACJ,IAAI,CAAC,CAAC;MAC7D;IACF,CAAC;IACD9B,QAAQ,EAAE,SAASA,QAAQA,CAACmC,MAAM,EAAE;MAClC7B,WAAW,CAAC8B,iBAAiB,CAACpD,IAAI,EAAEmD,MAAM,CAAC;MAC3C,IAAIpC,SAAS,EAAE;QACbA,SAAS,CAACoC,MAAM,CAAC;MACnB;IACF,CAAC;IACDlC,cAAc,EAAEA;EAClB,CAAC,CAAC;EACFgB,WAAW,CAAC7B,QAAQ,CAAC;;EAErB;EACA,IAAIiD,QAAQ,GAAG/D,KAAK,CAAC+B,MAAM,CAAC,IAAI,CAAC;EACjCS,gBAAgB,CAAC7B,aAAa,EAAE,CAACoD,QAAQ,CAAChB,OAAO,CAAC;EAClD,IAAI,CAACgB,QAAQ,CAAChB,OAAO,EAAE;IACrBgB,QAAQ,CAAChB,OAAO,GAAG,IAAI;EACzB;EACA/C,KAAK,CAACgD,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjB,OAAOJ,WAAW,CAAChB,cAAc,CAAC;IACpC,CAAC;EACH,CAAC;EACD;EACA,EAAE,CAAC;;EAEH;EACA,IAAIoC,YAAY;EAChB,IAAIC,mBAAmB,GAAG,OAAOlD,QAAQ,KAAK,UAAU;EACxD,IAAIkD,mBAAmB,EAAE;IACvB,IAAIC,OAAO,GAAG9B,YAAY,CAAC+B,cAAc,CAAC,IAAI,CAAC;IAC/CH,YAAY,GAAGjD,QAAQ,CAACmD,OAAO,EAAE9B,YAAY,CAAC;EAChD,CAAC,MAAM;IACL4B,YAAY,GAAGjD,QAAQ;EACzB;;EAEA;EACAwB,YAAY,CAAC,CAAC0B,mBAAmB,CAAC;;EAElC;EACA,IAAIG,aAAa,GAAGpE,KAAK,CAAC+B,MAAM,CAAC,CAAC;EAClC/B,KAAK,CAACgD,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAC3C,SAAS,CAAC+D,aAAa,CAACrB,OAAO,IAAI,EAAE,EAAEnC,MAAM,IAAI,EAAE,CAAC,EAAE;MACzDwB,YAAY,CAACiC,SAAS,CAACzD,MAAM,IAAI,EAAE,CAAC;IACtC;IACAwD,aAAa,CAACrB,OAAO,GAAGnC,MAAM;EAChC,CAAC,EAAE,CAACA,MAAM,EAAEwB,YAAY,CAAC,CAAC;EAC1B,IAAIkC,gBAAgB,GAAGtE,KAAK,CAACuE,OAAO,CAAC,YAAY;IAC/C,OAAO3E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MACxDf,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACe,YAAY,EAAEf,eAAe,CAAC,CAAC;EACnC,IAAImD,WAAW,GAAG,aAAaxE,KAAK,CAACyE,aAAa,CAACnE,WAAW,CAACoE,QAAQ,EAAE;IACvEC,KAAK,EAAE;EACT,CAAC,EAAE,aAAa3E,KAAK,CAACyE,aAAa,CAACvE,YAAY,CAACwE,QAAQ,EAAE;IACzDC,KAAK,EAAEL;EACT,CAAC,EAAEN,YAAY,CAAC,CAAC;EACjB,IAAI9C,SAAS,KAAK,KAAK,EAAE;IACvB,OAAOsD,WAAW;EACpB;EACA,OAAO,aAAaxE,KAAK,CAACyE,aAAa,CAACvD,SAAS,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAEkC,SAAS,EAAE;IACzEpB,GAAG,EAAEqB,gBAAgB;IACrB8C,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;MACjCA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB3C,YAAY,CAAC4C,MAAM,CAAC,CAAC;IACvB,CAAC;IACDC,OAAO,EAAE,SAASA,OAAOA,CAACJ,KAAK,EAAE;MAC/B,IAAIK,kBAAkB;MACtBL,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB1C,YAAY,CAAC+C,WAAW,CAAC,CAAC;MAC1B,CAACD,kBAAkB,GAAGrD,SAAS,CAACoD,OAAO,MAAM,IAAI,IAAIC,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACE,IAAI,CAACvD,SAAS,EAAEgD,KAAK,CAAC;IACjI;EACF,CAAC,CAAC,EAAEL,WAAW,CAAC;AAClB,CAAC;AACD,eAAejE,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}