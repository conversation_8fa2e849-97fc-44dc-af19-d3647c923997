from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from app.database import get_db
from app.models.task import Task
from app.models.task_execution import TaskExecution
from app.models.task_workflow import TaskWorkflow, TaskWorkflowExecution
from app.utils.security import run_script_in_sandbox
from app.utils.dependency_manager import DependencyManager
from app.utils.execution_manager import ExecutionManager, TaskNode, ExecutionResult, TaskStatus, ExecutionMode
from datetime import datetime
import logging
import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入WebSocket事件推送函数
try:
    from app.websocket import emit_task_execution_update
    WEBSOCKET_ENABLED = True
except ImportError:
    WEBSOCKET_ENABLED = False
    logger.warning("WebSocket not enabled, real-time updates will not be available")

class TaskScheduler:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.logger = logger
    
    def start(self):
        """启动调度器"""
        self.scheduler.start()
        self.load_tasks()
        self.logger.info("任务调度器已启动")
    
    def shutdown(self):
        """关闭调度器"""
        self.scheduler.shutdown()
        self.logger.info("任务调度器已关闭")
    
    def load_tasks(self):
        """从数据库加载所有启用的任务"""
        db = next(get_db())
        tasks = db.query(Task).filter(Task.status == 'enabled').all()
        
        for task in tasks:
            try:
                self.add_job(task)
            except Exception as e:
                self.logger.error(f"加载任务 {task.name} 失败: {str(e)}")
    
    def add_job(self, task):
        """添加任务到调度器"""
        if task.status != 'enabled' or not task.cron_expression:
            return
        
        # 移除已存在的任务
        self.remove_job(task.id)
        
        # 添加新任务
        self.scheduler.add_job(
            func=self.execute_task,
            trigger=CronTrigger.from_crontab(task.cron_expression),
            id=str(task.id),
            args=[task.id],
            name=task.name
        )
        self.logger.info(f"任务 {task.name} 已添加到调度器")
        
        # 推送任务状态更新
        if WEBSOCKET_ENABLED:
            from app.websocket import emit_task_status_update
            emit_task_status_update(task.id, 'enabled')
    
    def remove_job(self, task_id):
        """从调度器移除任务"""
        try:
            self.scheduler.remove_job(str(task_id))
            self.logger.info(f"任务 {task_id} 已从调度器移除")
            
            # 推送任务状态更新
            if WEBSOCKET_ENABLED:
                from app.websocket import emit_task_status_update
                emit_task_status_update(task_id, 'disabled')
        except Exception:
            # 任务不存在时忽略异常
            pass
    
    def update_job(self, task):
        """更新调度器中的任务"""
        self.remove_job(task.id)
        if task.status == 'enabled' and task.cron_expression:
            self.add_job(task)
    
    def execute_task(self, task_id):
        """执行任务"""
        db = next(get_db())
        task = db.query(Task).filter(Task.id == task_id).first()
        
        if not task:
            self.logger.error(f"任务 {task_id} 不存在")
            return
        
        # 创建执行记录
        execution = TaskExecution(
            task_id=task.id,
            status='running',
            start_time=datetime.utcnow()
        )
        db.add(execution)
        db.commit()
        
        try:
            # 执行脚本
            if task.script and task.script.content:
                output, error_message = run_script_in_sandbox(
                    task.script.content,
                    task.script.type,
                    timeout=3600  # 1小时超时
                )
                
                # 更新执行记录
                execution.status = 'success' if not error_message else 'failed'
                execution.output = output
                execution.error_message = error_message
                
                # 推送执行更新
                if WEBSOCKET_ENABLED:
                    emit_task_execution_update(execution.id, execution.status)
            else:
                # 没有关联脚本
                execution.status = 'failed'
                execution.error_message = '任务没有关联脚本'
                
                # 推送执行更新
                if WEBSOCKET_ENABLED:
                    emit_task_execution_update(execution.id, execution.status)
        except Exception as e:
            # 更新执行记录
            execution.status = 'failed'
            execution.error_message = str(e)
            
            # 推送执行更新
            if WEBSOCKET_ENABLED:
                emit_task_execution_update(execution.id, execution.status)
        finally:
            execution.end_time = datetime.utcnow()
            execution.duration = (execution.end_time - execution.start_time).total_seconds()
            db.commit()
            self.logger.info(f"任务 {task.name} 执行完成，状态: {execution.status}")

    def execute_workflow(self, workflow_id):
        """执行工作流"""
        db = next(get_db())
        workflow = db.query(TaskWorkflow).filter(TaskWorkflow.id == workflow_id).first()

        if not workflow:
            self.logger.error(f"工作流 {workflow_id} 不存在")
            return

        # 创建工作流执行记录
        execution = TaskWorkflowExecution(
            workflow_id=workflow.id,
            status='running',
            start_time=datetime.utcnow()
        )
        db.add(execution)
        db.commit()
        db.refresh(execution)

        try:
            # 解析工作流定义
            workflow_definition = workflow.workflow_definition or {}
            nodes = workflow_definition.get('nodes', [])
            edges = workflow_definition.get('edges', [])

            if not nodes:
                raise Exception("工作流没有定义任何节点")

            # 执行工作流
            result = self._execute_workflow_nodes(nodes, edges, execution.id)

            # 更新执行记录
            execution.status = 'success' if result['success'] else 'failed'
            execution.output = result['output']
            execution.error_message = result.get('error_message')

        except Exception as e:
            self.logger.error(f"工作流 {workflow.name} 执行失败: {str(e)}")
            execution.status = 'failed'
            execution.error_message = str(e)
        finally:
            execution.end_time = datetime.utcnow()
            execution.duration = (execution.end_time - execution.start_time).total_seconds()
            db.commit()
            self.logger.info(f"工作流 {workflow.name} 执行完成，状态: {execution.status}")

    def _execute_workflow_nodes(self, nodes: List[Dict], edges: List[Dict], execution_id: int) -> Dict[str, Any]:
        """执行工作流节点"""
        try:
            # 使用依赖管理器验证和获取执行顺序
            dependency_manager = DependencyManager()
            execution_plan = dependency_manager.get_execution_plan(nodes, edges)

            if not execution_plan['valid']:
                raise Exception(execution_plan['message'])

            execution_order = execution_plan['execution_order']

            # 转换为TaskNode对象
            task_nodes = []
            for node in nodes:
                task_node = TaskNode(
                    id=node['id'],
                    name=node.get('name', node['id']),
                    task_id=node.get('task_id'),
                    execution_mode=ExecutionMode(node.get('execution_type', 'serial')),
                    timeout=int(node.get('timeout', 3600)),
                    dependencies=[edge['source'] for edge in edges if edge['target'] == node['id']],
                    condition=node.get('condition', 'success')
                )
                task_nodes.append(task_node)

            # 创建执行管理器
            execution_manager = ExecutionManager(max_workers=5)

            # 添加状态回调
            def status_callback(task_id: str, status: TaskStatus, result: ExecutionResult = None):
                self.logger.info(f"任务节点 {task_id} 状态变更: {status.value}")
                # 可以在这里推送WebSocket消息
                if WEBSOCKET_ENABLED:
                    try:
                        from app.websocket import emit_workflow_node_update
                        emit_workflow_node_update(execution_id, task_id, status.value, result)
                    except ImportError:
                        pass

            execution_manager.add_status_callback(status_callback)

            # 执行工作流
            results = execution_manager.execute_workflow(
                task_nodes,
                execution_order,
                self._execute_workflow_task
            )

            # 汇总结果
            all_success = all(result.status == TaskStatus.SUCCESS for result in results.values())
            output_lines = []
            error_lines = []

            for node_id, result in results.items():
                if result.output:
                    output_lines.append(f"节点 {node_id}: {result.output}")
                if result.error_message:
                    error_lines.append(f"节点 {node_id}: {result.error_message}")

            # 清理资源
            execution_manager.cleanup()

            return {
                'success': all_success,
                'output': '\n'.join(output_lines),
                'error_message': '\n'.join(error_lines) if error_lines else None,
                'node_results': {node_id: {
                    'status': result.status.value,
                    'output': result.output,
                    'error_message': result.error_message,
                    'duration': result.duration
                } for node_id, result in results.items()}
            }

        except Exception as e:
            return {
                'success': False,
                'output': '',
                'error_message': str(e),
                'node_results': {}
            }

    def _execute_workflow_task(self, node: TaskNode) -> ExecutionResult:
        """执行工作流中的单个任务"""
        try:
            if not node.task_id:
                return ExecutionResult(
                    task_id=node.id,
                    status=TaskStatus.FAILED,
                    error_message='节点未配置关联任务'
                )

            # 获取任务信息
            db = next(get_db())
            task = db.query(Task).filter(Task.id == node.task_id).first()

            if not task:
                return ExecutionResult(
                    task_id=node.id,
                    status=TaskStatus.FAILED,
                    error_message=f'任务 {node.task_id} 不存在'
                )

            # 准备输入参数
            from app.utils.parameter_manager import ParameterManager
            param_manager = ParameterManager()

            # 解析参数引用
            resolved_params = param_manager.resolve_parameter_references(
                node.input_parameters,
                self._get_parameter_context(node)
            )

            # 创建任务执行记录
            execution = TaskExecution(
                task_id=task.id,
                status='running',
                start_time=datetime.utcnow(),
                input_parameters=resolved_params
            )
            db.add(execution)
            db.commit()

            try:
                # 执行脚本
                if task.script and task.script.content:
                    # 准备脚本内容，注入参数
                    script_content = self._inject_parameters(task.script.content, resolved_params)

                    output, error_message = run_script_in_sandbox(
                        script_content,
                        task.script.type,
                        timeout=node.timeout
                    )

                    # 提取输出参数
                    output_params = param_manager.extract_parameters_from_output(
                        output or '',
                        node.output_extraction
                    )

                    # 确定返回码
                    return_code = 0 if not error_message else 1

                    # 更新执行记录
                    execution.status = 'success' if not error_message else 'failed'
                    execution.output = output
                    execution.error_message = error_message
                    execution.output_parameters = output_params
                    execution.return_code = return_code

                    result_status = TaskStatus.SUCCESS if not error_message else TaskStatus.FAILED

                    return ExecutionResult(
                        task_id=node.id,
                        status=result_status,
                        output=output or '',
                        error_message=error_message,
                        input_parameters=resolved_params,
                        output_parameters=output_params,
                        return_code=return_code
                    )
                else:
                    execution.status = 'failed'
                    execution.error_message = '任务没有关联脚本'
                    execution.return_code = 1

                    return ExecutionResult(
                        task_id=node.id,
                        status=TaskStatus.FAILED,
                        error_message='任务没有关联脚本',
                        return_code=1
                    )
            finally:
                execution.end_time = datetime.utcnow()
                execution.duration = (execution.end_time - execution.start_time).total_seconds()
                db.commit()

        except Exception as e:
            return ExecutionResult(
                task_id=node.id,
                status=TaskStatus.FAILED,
                error_message=str(e),
                return_code=1
            )

    def _get_parameter_context(self, node: TaskNode) -> Dict[str, Any]:
        """获取参数上下文"""
        context = {
            'node_id': node.id,
            'node_name': node.name,
            'task_id': node.task_id
        }

        # 添加依赖任务的输出参数
        for dep_id in node.dependencies:
            # 这里需要从执行管理器获取依赖任务的结果
            # 暂时简化处理
            context[f'{dep_id}_output'] = ''

        return context

    def _inject_parameters(self, script_content: str, parameters: Dict[str, Any]) -> str:
        """向脚本中注入参数"""
        # 简单的参数注入，可以根据脚本类型优化
        injected_content = script_content

        # 为Python脚本注入参数
        if parameters:
            param_lines = []
            for key, value in parameters.items():
                if isinstance(value, str):
                    param_lines.append(f'{key} = "{value}"')
                elif isinstance(value, (int, float, bool)):
                    param_lines.append(f'{key} = {value}')
                else:
                    param_lines.append(f'{key} = {repr(value)}')

            if param_lines:
                injected_content = '\n'.join(param_lines) + '\n\n' + script_content

        return injected_content


