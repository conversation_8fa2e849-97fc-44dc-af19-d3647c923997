[tool:pytest]
testpaths = tests
python_files = test_dependency_manager.py test_condition_parser.py test_parameter_manager.py test_execution_manager.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --cov=app.utils.dependency_manager,app.utils.condition_parser,app.utils.parameter_manager,app.utils.execution_manager
    --cov-report=html:htmlcov_core
    --cov-report=term-missing
    --cov-fail-under=90
markers =
    unit: Unit tests for core modules
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
