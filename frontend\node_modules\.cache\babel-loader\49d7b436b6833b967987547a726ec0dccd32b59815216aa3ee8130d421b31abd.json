{"ast": null, "code": "import { format, isEmptyValue } from \"../util\";\nvar required = function required(rule, value, source, errors, options, type) {\n  if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type || rule.type))) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\nexport default required;", "map": {"version": 3, "names": ["format", "isEmptyValue", "required", "rule", "value", "source", "errors", "options", "type", "hasOwnProperty", "field", "push", "messages", "fullField"], "sources": ["E:/code1/task3/frontend/node_modules/@rc-component/async-validator/es/rule/required.js"], "sourcesContent": ["import { format, isEmptyValue } from \"../util\";\nvar required = function required(rule, value, source, errors, options, type) {\n  if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type || rule.type))) {\n    errors.push(format(options.messages.required, rule.fullField));\n  }\n};\nexport default required;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,YAAY,QAAQ,SAAS;AAC9C,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC3E,IAAIL,IAAI,CAACD,QAAQ,KAAK,CAACG,MAAM,CAACI,cAAc,CAACN,IAAI,CAACO,KAAK,CAAC,IAAIT,YAAY,CAACG,KAAK,EAAEI,IAAI,IAAIL,IAAI,CAACK,IAAI,CAAC,CAAC,EAAE;IACnGF,MAAM,CAACK,IAAI,CAACX,MAAM,CAACO,OAAO,CAACK,QAAQ,CAACV,QAAQ,EAAEC,IAAI,CAACU,SAAS,CAAC,CAAC;EAChE;AACF,CAAC;AACD,eAAeX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}