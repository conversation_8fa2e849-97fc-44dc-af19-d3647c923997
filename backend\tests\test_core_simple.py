#!/usr/bin/env python3
"""
简化的核心功能测试
"""
import sys
import os
import unittest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestCoreFeatures(unittest.TestCase):
    """核心功能测试"""
    
    def test_dependency_manager_basic(self):
        """测试依赖管理器基本功能"""
        from app.utils.dependency_manager import DependencyManager
        
        dm = DependencyManager()
        dm.add_dependency('task2', 'task1')
        dm.add_dependency('task3', 'task2')
        
        # 测试无循环
        self.assertFalse(dm.has_cycle())
        
        # 测试拓扑排序
        order = dm.topological_sort()
        self.assertEqual(len(order), 3)
        self.assertEqual(order[0], ['task1'])
    
    def test_condition_parser_basic(self):
        """测试条件解析器基本功能"""
        from app.utils.condition_parser import ConditionParser
        
        parser = ConditionParser()
        
        # 测试预定义条件
        self.assertTrue(parser.parse_condition('success', {'status': 'success'}))
        self.assertFalse(parser.parse_condition('success', {'status': 'failed'}))
        self.assertTrue(parser.parse_condition('always', {}))
        
        # 测试自定义条件
        self.assertTrue(parser.parse_condition('status == "success"', {'status': 'success'}))
        self.assertTrue(parser.parse_condition('duration < 60', {'duration': 30}))
    
    def test_parameter_manager_basic(self):
        """测试参数管理器基本功能"""
        from app.utils.parameter_manager import ParameterManager, ParameterType
        
        manager = ParameterManager()
        
        # 测试类型转换
        self.assertEqual(manager.convert_parameter("42", ParameterType.INTEGER), 42)
        self.assertEqual(manager.convert_parameter("3.14", ParameterType.FLOAT), 3.14)
        self.assertTrue(manager.convert_parameter("true", ParameterType.BOOLEAN))
        
        # 测试参数验证
        is_valid, _ = manager.validate_parameter("test", ParameterType.STRING, {'min_length': 3})
        self.assertTrue(is_valid)
    
    def test_execution_manager_basic(self):
        """测试执行管理器基本功能"""
        from app.utils.execution_manager import ExecutionManager, TaskNode
        
        manager = ExecutionManager(max_workers=2)
        
        # 测试基本属性
        self.assertEqual(manager.max_workers, 2)
        self.assertEqual(len(manager.task_results), 0)
        
        # 测试任务节点创建
        node = TaskNode(id="test", name="测试任务", task_id=1)
        self.assertEqual(node.id, "test")
        self.assertEqual(node.name, "测试任务")
        
        manager.cleanup()

if __name__ == '__main__':
    unittest.main()
