"""
集成测试
"""
import pytest
import json
import time
from unittest.mock import patch, MagicMock


@pytest.mark.integration
class TestWorkflowIntegration:
    """工作流集成测试"""
    
    def test_complete_workflow_lifecycle(self, client, db_session, test_factory):
        """测试完整的工作流生命周期"""
        # 1. 创建脚本
        script1 = test_factory.create_script(
            db_session,
            name="数据提取脚本",
            content="print('提取了 100 条记录')",
            type="python"
        )
        
        script2 = test_factory.create_script(
            db_session,
            name="数据处理脚本", 
            content="print('处理完成，生成文件: /tmp/result.csv')",
            type="python"
        )
        
        # 2. 创建任务
        task1 = test_factory.create_task(
            db_session,
            name="数据提取任务",
            script_id=script1.id
        )
        
        task2 = test_factory.create_task(
            db_session,
            name="数据处理任务",
            script_id=script2.id
        )
        
        # 3. 创建工作流
        workflow_data = {
            'name': '数据处理工作流',
            'description': '从数据源提取数据并进行处理',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'extract',
                        'name': '数据提取',
                        'task_id': task1.id,
                        'execution_type': 'serial',
                        'output_extraction': [
                            {
                                'name': 'record_count',
                                'pattern': r'提取了 (\d+) 条记录',
                                'type': 'integer'
                            }
                        ]
                    },
                    {
                        'id': 'process',
                        'name': '数据处理',
                        'task_id': task2.id,
                        'execution_type': 'serial',
                        'input_parameters': {
                            'input_count': '${extract.record_count}'
                        },
                        'output_extraction': [
                            {
                                'name': 'output_file',
                                'pattern': r'生成文件: ([^\s]+)',
                                'type': 'string'
                            }
                        ]
                    }
                ],
                'edges': [
                    {
                        'source': 'extract',
                        'target': 'process',
                        'condition': 'success'
                    }
                ]
            }
        }
        
        # 4. 创建工作流
        response = client.post(
            '/task-workflows/workflows',
            data=json.dumps(workflow_data),
            content_type='application/json'
        )
        assert response.status_code == 200
        workflow_id = json.loads(response.data)['data']['id']
        
        # 5. 验证工作流
        response = client.post(
            '/task-workflows/workflows/validate',
            data=json.dumps({'workflow_definition': workflow_data['workflow_definition']}),
            content_type='application/json'
        )
        assert response.status_code == 200
        validation_data = json.loads(response.data)
        assert validation_data['data']['valid'] is True
        
        # 6. 获取执行计划
        response = client.get(f'/task-workflows/workflows/{workflow_id}/execution-plan')
        assert response.status_code == 200
        plan_data = json.loads(response.data)
        assert plan_data['data']['valid'] is True
        assert len(plan_data['data']['execution_order']) == 2
        
        # 7. 执行工作流（模拟）
        with patch('app.api.task_workflows.current_app') as mock_app:
            mock_scheduler = MagicMock()
            mock_app.scheduler = mock_scheduler
            
            response = client.post(f'/task-workflows/workflows/{workflow_id}/execute')
            assert response.status_code == 200
            execution_data = json.loads(response.data)
            assert 'execution_id' in execution_data['data']
            
            # 验证调度器被调用
            mock_scheduler.execute_workflow.assert_called_once_with(workflow_id)
    
    def test_workflow_with_conditions(self, client, db_session, test_factory):
        """测试带条件的工作流"""
        # 创建任务
        task1 = test_factory.create_task(db_session, name="检查任务")
        task2 = test_factory.create_task(db_session, name="成功处理任务")
        task3 = test_factory.create_task(db_session, name="失败处理任务")
        
        workflow_data = {
            'name': '条件分支工作流',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'check',
                        'name': '检查',
                        'task_id': task1.id
                    },
                    {
                        'id': 'success_handler',
                        'name': '成功处理',
                        'task_id': task2.id
                    },
                    {
                        'id': 'failure_handler',
                        'name': '失败处理',
                        'task_id': task3.id
                    }
                ],
                'edges': [
                    {
                        'source': 'check',
                        'target': 'success_handler',
                        'condition': 'success'
                    },
                    {
                        'source': 'check',
                        'target': 'failure_handler',
                        'condition': 'failed'
                    }
                ]
            }
        }
        
        # 验证工作流
        response = client.post(
            '/task-workflows/workflows/validate',
            data=json.dumps({'workflow_definition': workflow_data['workflow_definition']}),
            content_type='application/json'
        )
        assert response.status_code == 200
        validation_data = json.loads(response.data)
        assert validation_data['data']['valid'] is True
    
    def test_workflow_with_parallel_execution(self, client, db_session, test_factory):
        """测试并行执行工作流"""
        # 创建任务
        task1 = test_factory.create_task(db_session, name="初始任务")
        task2 = test_factory.create_task(db_session, name="并行任务1")
        task3 = test_factory.create_task(db_session, name="并行任务2")
        task4 = test_factory.create_task(db_session, name="汇总任务")
        
        workflow_data = {
            'name': '并行执行工作流',
            'workflow_definition': {
                'nodes': [
                    {
                        'id': 'init',
                        'name': '初始化',
                        'task_id': task1.id,
                        'execution_type': 'serial'
                    },
                    {
                        'id': 'parallel1',
                        'name': '并行处理1',
                        'task_id': task2.id,
                        'execution_type': 'parallel'
                    },
                    {
                        'id': 'parallel2',
                        'name': '并行处理2',
                        'task_id': task3.id,
                        'execution_type': 'parallel'
                    },
                    {
                        'id': 'summary',
                        'name': '汇总',
                        'task_id': task4.id,
                        'execution_type': 'serial'
                    }
                ],
                'edges': [
                    {'source': 'init', 'target': 'parallel1'},
                    {'source': 'init', 'target': 'parallel2'},
                    {'source': 'parallel1', 'target': 'summary'},
                    {'source': 'parallel2', 'target': 'summary'}
                ]
            }
        }
        
        # 获取执行计划
        response = client.post(
            '/task-workflows/workflows/validate',
            data=json.dumps({'workflow_definition': workflow_data['workflow_definition']}),
            content_type='application/json'
        )
        assert response.status_code == 200
        
        validation_data = json.loads(response.data)
        assert validation_data['data']['valid'] is True
        
        # 检查执行计划
        execution_plan = validation_data['data']['execution_plan']
        assert len(execution_plan['execution_order']) == 3
        assert execution_plan['execution_order'][0] == ['init']
        assert set(execution_plan['execution_order'][1]) == {'parallel1', 'parallel2'}
        assert execution_plan['execution_order'][2] == ['summary']


@pytest.mark.integration
class TestParameterIntegration:
    """参数管理集成测试"""
    
    def test_parameter_validation_api(self, client):
        """测试参数验证API"""
        parameters_data = {
            'parameters': {
                'string_param': {
                    'type': 'string',
                    'value': 'test_value',
                    'constraints': {'min_length': 3, 'max_length': 20}
                },
                'integer_param': {
                    'type': 'integer',
                    'value': 42,
                    'constraints': {'min': 0, 'max': 100}
                },
                'boolean_param': {
                    'type': 'boolean',
                    'value': True,
                    'constraints': {}
                }
            }
        }
        
        response = client.post(
            '/task-workflows/parameters/validate',
            data=json.dumps(parameters_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert data['data']['all_valid'] is True
        assert len(data['data']['results']) == 3
    
    def test_parameter_schema_api(self, client):
        """测试参数模式API"""
        response = client.get('/task-workflows/parameters/schema')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert 'types' in data['data']
        assert 'constraints' in data['data']
        assert 'examples' in data['data']


@pytest.mark.integration
class TestConditionIntegration:
    """条件处理集成测试"""
    
    def test_condition_validation_api(self, client):
        """测试条件验证API"""
        test_conditions = [
            {'condition': 'success', 'expected_valid': True},
            {'condition': 'status == "success"', 'expected_valid': True},
            {'condition': 'duration < 60', 'expected_valid': True},
            {'condition': 'invalid syntax ==', 'expected_valid': False},
        ]
        
        for test_case in test_conditions:
            response = client.post(
                '/task-workflows/conditions/validate',
                data=json.dumps({'condition': test_case['condition']}),
                content_type='application/json'
            )
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['code'] == 200
            assert data['data']['valid'] == test_case['expected_valid']
    
    def test_condition_help_api(self, client):
        """测试条件帮助API"""
        response = client.get('/task-workflows/conditions/help')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['code'] == 200
        assert '预定义条件' in data['data']
        assert '变量' in data['data']
        assert '操作符' in data['data']


@pytest.mark.integration
@pytest.mark.slow
class TestPerformanceIntegration:
    """性能集成测试"""
    
    def test_large_workflow_validation(self, client):
        """测试大型工作流验证性能"""
        # 创建一个包含100个节点的工作流
        nodes = []
        edges = []
        
        for i in range(100):
            nodes.append({
                'id': f'task{i}',
                'name': f'任务{i}',
                'task_id': i + 1
            })
            
            if i > 0:
                edges.append({
                    'source': f'task{i-1}',
                    'target': f'task{i}'
                })
        
        workflow_definition = {
            'workflow_definition': {
                'nodes': nodes,
                'edges': edges
            }
        }
        
        start_time = time.time()
        response = client.post(
            '/task-workflows/workflows/validate',
            data=json.dumps(workflow_definition),
            content_type='application/json'
        )
        end_time = time.time()
        
        # 验证响应
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['valid'] is True
        
        # 验证性能（应该在1秒内完成）
        execution_time = end_time - start_time
        assert execution_time < 1.0, f"大型工作流验证耗时过长: {execution_time:.2f}秒"
    
    def test_concurrent_workflow_operations(self, client, db_session, test_factory):
        """测试并发工作流操作"""
        import threading
        import queue
        
        # 创建测试工作流
        workflow = test_factory.create_workflow(
            db_session,
            name="并发测试工作流"
        )
        
        results = queue.Queue()
        
        def make_request():
            try:
                response = client.get(f'/task-workflows/workflows/{workflow.id}')
                results.put(response.status_code)
            except Exception as e:
                results.put(str(e))
        
        # 创建10个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查结果
        success_count = 0
        while not results.empty():
            result = results.get()
            if result == 200:
                success_count += 1
        
        # 所有请求都应该成功
        assert success_count == 10
