"""
依赖管理器测试
"""
import pytest
from app.utils.dependency_manager import DependencyManager


class TestDependencyManager:
    """依赖管理器测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.dm = DependencyManager()
    
    def test_add_dependency(self):
        """测试添加依赖关系"""
        self.dm.add_dependency('task2', 'task1')
        
        assert 'task1' in self.dm.graph['task2']
        assert 'task2' in self.dm.reverse_graph['task1']
        assert 'task1' in self.dm.nodes
        assert 'task2' in self.dm.nodes
    
    def test_remove_dependency(self):
        """测试移除依赖关系"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.remove_dependency('task2', 'task1')
        
        assert 'task1' not in self.dm.graph['task2']
        assert 'task2' not in self.dm.reverse_graph['task1']
    
    def test_has_cycle_no_cycle(self):
        """测试无循环依赖"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        
        assert not self.dm.has_cycle()
    
    def test_has_cycle_with_cycle(self):
        """测试有循环依赖"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        self.dm.add_dependency('task1', 'task3')  # 形成循环
        
        assert self.dm.has_cycle()
    
    def test_has_cycle_self_dependency(self):
        """测试自依赖循环"""
        self.dm.add_dependency('task1', 'task1')
        
        assert self.dm.has_cycle()
    
    def test_find_cycle(self):
        """测试找到循环路径"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        self.dm.add_dependency('task1', 'task3')
        
        cycle = self.dm.find_cycle()
        assert len(cycle) > 0
        assert cycle[0] == cycle[-1]  # 循环路径首尾相同
    
    def test_find_cycle_no_cycle(self):
        """测试无循环时的路径查找"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        
        cycle = self.dm.find_cycle()
        assert len(cycle) == 0
    
    def test_topological_sort_simple(self):
        """测试简单拓扑排序"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        
        order = self.dm.topological_sort()
        assert len(order) == 3
        assert order[0] == ['task1']
        assert order[1] == ['task2']
        assert order[2] == ['task3']
    
    def test_topological_sort_parallel(self):
        """测试并行任务的拓扑排序"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task1')
        self.dm.add_dependency('task4', 'task2')
        self.dm.add_dependency('task4', 'task3')
        
        order = self.dm.topological_sort()
        assert len(order) == 3
        assert order[0] == ['task1']
        assert set(order[1]) == {'task2', 'task3'}  # 并行执行
        assert order[2] == ['task4']
    
    def test_topological_sort_with_cycle(self):
        """测试有循环时的拓扑排序"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task1', 'task2')
        
        with pytest.raises(ValueError, match="图中存在循环依赖"):
            self.dm.topological_sort()
    
    def test_get_dependencies(self):
        """测试获取节点依赖"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task2', 'task3')
        
        deps = self.dm.get_dependencies('task2')
        assert set(deps) == {'task1', 'task3'}
        
        deps = self.dm.get_dependencies('task1')
        assert deps == []
    
    def test_get_dependents(self):
        """测试获取依赖于该节点的节点"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task1')
        
        dependents = self.dm.get_dependents('task1')
        assert set(dependents) == {'task2', 'task3'}
        
        dependents = self.dm.get_dependents('task2')
        assert dependents == []
    
    def test_get_all_dependencies(self):
        """测试获取所有传递依赖"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        self.dm.add_dependency('task4', 'task3')
        
        all_deps = self.dm.get_all_dependencies('task4')
        assert all_deps == {'task1', 'task2', 'task3'}
        
        all_deps = self.dm.get_all_dependencies('task1')
        assert all_deps == set()
    
    def test_get_all_dependents(self):
        """测试获取所有传递依赖者"""
        self.dm.add_dependency('task2', 'task1')
        self.dm.add_dependency('task3', 'task2')
        self.dm.add_dependency('task4', 'task3')
        
        all_dependents = self.dm.get_all_dependents('task1')
        assert all_dependents == {'task2', 'task3', 'task4'}
        
        all_dependents = self.dm.get_all_dependents('task4')
        assert all_dependents == set()
    
    def test_can_add_dependency_safe(self):
        """测试安全添加依赖"""
        self.dm.add_dependency('task2', 'task1')
        
        # 可以安全添加
        assert self.dm.can_add_dependency('task3', 'task2')
        
        # 会形成循环，不能添加
        assert not self.dm.can_add_dependency('task1', 'task2')
    
    def test_validate_workflow_valid(self):
        """测试验证有效工作流"""
        nodes = [
            {'id': 'task1', 'name': '任务1'},
            {'id': 'task2', 'name': '任务2'},
            {'id': 'task3', 'name': '任务3'}
        ]
        edges = [
            {'source': 'task1', 'target': 'task2'},
            {'source': 'task2', 'target': 'task3'}
        ]
        
        is_valid, message = self.dm.validate_workflow(nodes, edges)
        assert is_valid
        assert "有效" in message
    
    def test_validate_workflow_cycle(self):
        """测试验证有循环的工作流"""
        nodes = [
            {'id': 'task1', 'name': '任务1'},
            {'id': 'task2', 'name': '任务2'}
        ]
        edges = [
            {'source': 'task1', 'target': 'task2'},
            {'source': 'task2', 'target': 'task1'}
        ]
        
        is_valid, message = self.dm.validate_workflow(nodes, edges)
        assert not is_valid
        assert "循环依赖" in message
    
    def test_validate_workflow_missing_node(self):
        """测试验证缺少节点的工作流"""
        nodes = [
            {'id': 'task1', 'name': '任务1'}
        ]
        edges = [
            {'source': 'task1', 'target': 'task2'}  # task2不存在
        ]
        
        is_valid, message = self.dm.validate_workflow(nodes, edges)
        assert not is_valid
        assert "不存在" in message
    
    def test_get_execution_plan_valid(self):
        """测试获取有效执行计划"""
        nodes = [
            {'id': 'task1', 'name': '任务1'},
            {'id': 'task2', 'name': '任务2'},
            {'id': 'task3', 'name': '任务3'}
        ]
        edges = [
            {'source': 'task1', 'target': 'task2'},
            {'source': 'task1', 'target': 'task3'}
        ]
        
        plan = self.dm.get_execution_plan(nodes, edges)
        assert plan['valid']
        assert len(plan['execution_order']) == 2
        assert plan['execution_order'][0] == ['task1']
        assert set(plan['execution_order'][1]) == {'task2', 'task3'}
        assert plan['total_levels'] == 2
        assert plan['total_nodes'] == 3
    
    def test_get_execution_plan_invalid(self):
        """测试获取无效执行计划"""
        nodes = [
            {'id': 'task1', 'name': '任务1'},
            {'id': 'task2', 'name': '任务2'}
        ]
        edges = [
            {'source': 'task1', 'target': 'task2'},
            {'source': 'task2', 'target': 'task1'}
        ]
        
        plan = self.dm.get_execution_plan(nodes, edges)
        assert not plan['valid']
        assert "循环依赖" in plan['message']
        assert plan['execution_order'] == []
    
    def test_complex_workflow(self):
        """测试复杂工作流"""
        # 创建一个复杂的依赖图
        dependencies = [
            ('task2', 'task1'),
            ('task3', 'task1'),
            ('task4', 'task2'),
            ('task5', 'task3'),
            ('task6', 'task4'),
            ('task6', 'task5'),
            ('task7', 'task6')
        ]
        
        for target, source in dependencies:
            self.dm.add_dependency(target, source)
        
        assert not self.dm.has_cycle()
        
        order = self.dm.topological_sort()
        assert len(order) == 5
        assert order[0] == ['task1']
        assert set(order[1]) == {'task2', 'task3'}
        assert set(order[2]) == {'task4', 'task5'}
        assert order[3] == ['task6']
        assert order[4] == ['task7']
    
    def test_empty_graph(self):
        """测试空图"""
        assert not self.dm.has_cycle()
        assert self.dm.topological_sort() == []
        assert self.dm.get_dependencies('nonexistent') == []
        assert self.dm.get_dependents('nonexistent') == []
