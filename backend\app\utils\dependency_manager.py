"""
工作流依赖关系管理器
"""
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict, deque
import logging

logger = logging.getLogger(__name__)

class DependencyManager:
    """依赖关系管理器"""
    
    def __init__(self):
        self.graph = defaultdict(list)  # 依赖图: node -> [dependencies]
        self.reverse_graph = defaultdict(list)  # 反向图: node -> [dependents]
        self.nodes = set()
    
    def add_dependency(self, node: str, dependency: str):
        """添加依赖关系: node依赖于dependency"""
        self.graph[node].append(dependency)
        self.reverse_graph[dependency].append(node)
        self.nodes.add(node)
        self.nodes.add(dependency)
    
    def remove_dependency(self, node: str, dependency: str):
        """移除依赖关系"""
        if dependency in self.graph[node]:
            self.graph[node].remove(dependency)
        if node in self.reverse_graph[dependency]:
            self.reverse_graph[dependency].remove(node)
    
    def has_cycle(self) -> bool:
        """检查是否存在循环依赖"""
        WHITE, GRAY, BLACK = 0, 1, 2
        color = {node: WHITE for node in self.nodes}
        
        def dfs(node):
            if color[node] == GRAY:
                return True  # 发现循环
            if color[node] == BLACK:
                return False
            
            color[node] = GRAY
            for dependency in self.graph[node]:
                if dfs(dependency):
                    return True
            color[node] = BLACK
            return False
        
        for node in self.nodes:
            if color[node] == WHITE:
                if dfs(node):
                    return True
        return False
    
    def find_cycle(self) -> List[str]:
        """找到循环依赖路径"""
        WHITE, GRAY, BLACK = 0, 1, 2
        color = {node: WHITE for node in self.nodes}
        parent = {}
        cycle_path = []
        
        def dfs(node, path):
            if color[node] == GRAY:
                # 找到循环，构建循环路径
                cycle_start = path.index(node)
                return path[cycle_start:] + [node]
            if color[node] == BLACK:
                return None
            
            color[node] = GRAY
            path.append(node)
            
            for dependency in self.graph[node]:
                result = dfs(dependency, path.copy())
                if result:
                    return result
            
            color[node] = BLACK
            path.pop()
            return None
        
        for node in self.nodes:
            if color[node] == WHITE:
                result = dfs(node, [])
                if result:
                    return result
        return []
    
    def topological_sort(self) -> List[List[str]]:
        """拓扑排序，返回分层的执行顺序"""
        if self.has_cycle():
            raise ValueError("图中存在循环依赖，无法进行拓扑排序")
        
        in_degree = {node: len(self.graph[node]) for node in self.nodes}
        execution_order = []
        
        while in_degree:
            # 找到所有入度为0的节点（可以并行执行）
            current_level = [node for node, degree in in_degree.items() if degree == 0]
            
            if not current_level:
                raise ValueError("无法确定执行顺序，可能存在循环依赖")
            
            execution_order.append(current_level)
            
            # 移除当前级别的节点，并更新其他节点的入度
            for node in current_level:
                del in_degree[node]
                for dependent in self.reverse_graph[node]:
                    if dependent in in_degree:
                        in_degree[dependent] -= 1
        
        return execution_order
    
    def get_dependencies(self, node: str) -> List[str]:
        """获取节点的所有依赖"""
        return self.graph.get(node, [])
    
    def get_dependents(self, node: str) -> List[str]:
        """获取依赖于该节点的所有节点"""
        return self.reverse_graph.get(node, [])
    
    def get_all_dependencies(self, node: str) -> Set[str]:
        """获取节点的所有传递依赖"""
        visited = set()
        
        def dfs(current):
            if current in visited:
                return
            visited.add(current)
            for dependency in self.graph[current]:
                dfs(dependency)
        
        for dependency in self.graph[node]:
            dfs(dependency)
        
        return visited
    
    def get_all_dependents(self, node: str) -> Set[str]:
        """获取所有依赖于该节点的节点（传递依赖）"""
        visited = set()
        
        def dfs(current):
            if current in visited:
                return
            visited.add(current)
            for dependent in self.reverse_graph[current]:
                dfs(dependent)
        
        for dependent in self.reverse_graph[node]:
            dfs(dependent)
        
        return visited
    
    def can_add_dependency(self, node: str, dependency: str) -> bool:
        """检查是否可以安全添加依赖关系（不会产生循环）"""
        # 临时添加依赖
        original_deps = self.graph[node].copy()
        original_reverse_deps = self.reverse_graph[dependency].copy()
        
        self.add_dependency(node, dependency)
        has_cycle = self.has_cycle()
        
        # 恢复原状态
        self.graph[node] = original_deps
        self.reverse_graph[dependency] = original_reverse_deps
        
        return not has_cycle
    
    def validate_workflow(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]) -> Tuple[bool, str]:
        """验证工作流的依赖关系是否有效"""
        try:
            # 清空当前图
            self.graph.clear()
            self.reverse_graph.clear()
            self.nodes.clear()
            
            # 添加所有节点
            for node in nodes:
                node_id = node.get('id')
                if node_id:
                    self.nodes.add(node_id)
            
            # 添加依赖关系
            for edge in edges:
                source = edge.get('source')
                target = edge.get('target')
                if source and target:
                    self.add_dependency(target, source)  # target依赖于source
            
            # 检查循环依赖
            if self.has_cycle():
                cycle_path = self.find_cycle()
                return False, f"存在循环依赖: {' -> '.join(cycle_path)}"
            
            # 检查节点是否都存在
            for edge in edges:
                source = edge.get('source')
                target = edge.get('target')
                if source not in self.nodes:
                    return False, f"源节点 {source} 不存在"
                if target not in self.nodes:
                    return False, f"目标节点 {target} 不存在"
            
            return True, "工作流依赖关系有效"
            
        except Exception as e:
            logger.error(f"验证工作流依赖关系时出错: {str(e)}")
            return False, f"验证失败: {str(e)}"
    
    def get_execution_plan(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取执行计划"""
        is_valid, message = self.validate_workflow(nodes, edges)
        if not is_valid:
            return {
                'valid': False,
                'message': message,
                'execution_order': []
            }
        
        try:
            execution_order = self.topological_sort()
            return {
                'valid': True,
                'message': '执行计划生成成功',
                'execution_order': execution_order,
                'total_levels': len(execution_order),
                'total_nodes': len(self.nodes)
            }
        except Exception as e:
            return {
                'valid': False,
                'message': f"生成执行计划失败: {str(e)}",
                'execution_order': []
            }
