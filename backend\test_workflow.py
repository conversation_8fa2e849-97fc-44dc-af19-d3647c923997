#!/usr/bin/env python3
"""
测试工作流编排功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.dependency_manager import DependencyManager
from app.utils.condition_parser import ConditionParser
from app.utils.parameter_manager import ParameterManager, ParameterType
from app.utils.execution_manager import ExecutionManager, TaskNode, ExecutionMode

def test_dependency_manager():
    """测试依赖管理器"""
    print("=== 测试依赖管理器 ===")
    
    nodes = [
        {'id': 'task1', 'name': '任务1'},
        {'id': 'task2', 'name': '任务2'},
        {'id': 'task3', 'name': '任务3'},
        {'id': 'task4', 'name': '任务4'}
    ]
    
    edges = [
        {'source': 'task1', 'target': 'task2'},
        {'source': 'task1', 'target': 'task3'},
        {'source': 'task2', 'target': 'task4'},
        {'source': 'task3', 'target': 'task4'}
    ]
    
    dm = DependencyManager()
    is_valid, message = dm.validate_workflow(nodes, edges)
    print(f"工作流验证: {is_valid}, {message}")
    
    if is_valid:
        execution_plan = dm.get_execution_plan(nodes, edges)
        print(f"执行计划: {execution_plan}")

def test_condition_parser():
    """测试条件解析器"""
    print("\n=== 测试条件解析器 ===")
    
    cp = ConditionParser()
    
    test_conditions = [
        ('success', {'status': 'success'}),
        ('failed', {'status': 'failed'}),
        ('status == "success" and duration < 60', {'status': 'success', 'duration': 30}),
        ('output contains "error"', {'output': 'This is an error message'}),
        ('return_code != 0', {'return_code': 1})
    ]
    
    for condition, context in test_conditions:
        result = cp.parse_condition(condition, context)
        print(f"条件: {condition}, 上下文: {context}, 结果: {result}")

def test_parameter_manager():
    """测试参数管理器"""
    print("\n=== 测试参数管理器 ===")
    
    pm = ParameterManager()
    
    # 测试参数验证
    test_params = [
        ('hello', ParameterType.STRING, {'min_length': 3}),
        (42, ParameterType.INTEGER, {'min': 0, 'max': 100}),
        ('true', ParameterType.BOOLEAN, {}),
        ('[1,2,3]', ParameterType.LIST, {'min_items': 2})
    ]
    
    for value, param_type, constraints in test_params:
        is_valid, message = pm.validate_parameter(value, param_type, constraints)
        print(f"参数: {value}, 类型: {param_type.value}, 约束: {constraints}, 有效: {is_valid}, 消息: {message}")
    
    # 测试参数引用解析
    parameters = {
        'input_file': '${previous_output}',
        'count': 10,
        'message': 'Processing ${input_file}'
    }
    context = {'previous_output': '/tmp/data.txt'}
    resolved = pm.resolve_parameter_references(parameters, context)
    print(f"参数引用解析: {parameters} -> {resolved}")
    
    # 测试输出提取
    output = "处理了 150 条记录，生成文件 /tmp/result.txt"
    extraction_rules = [
        {'name': 'record_count', 'pattern': r'处理了 (\d+) 条记录', 'type': 'integer'},
        {'name': 'output_file', 'pattern': r'生成文件 ([^\s]+)', 'type': 'string'}
    ]
    extracted = pm.extract_parameters_from_output(output, extraction_rules)
    print(f"输出提取: {output} -> {extracted}")

def test_execution_manager():
    """测试执行管理器"""
    print("\n=== 测试执行管理器 ===")
    
    # 创建测试任务节点
    nodes = [
        TaskNode(
            id='task1',
            name='任务1',
            task_id=1,
            execution_mode=ExecutionMode.SERIAL,
            input_parameters={'input': 'test_data'}
        ),
        TaskNode(
            id='task2',
            name='任务2',
            task_id=2,
            execution_mode=ExecutionMode.PARALLEL,
            dependencies=['task1'],
            condition='success'
        ),
        TaskNode(
            id='task3',
            name='任务3',
            task_id=3,
            execution_mode=ExecutionMode.PARALLEL,
            dependencies=['task1'],
            condition='success'
        )
    ]
    
    execution_order = [['task1'], ['task2', 'task3']]
    
    def mock_task_executor(node):
        """模拟任务执行器"""
        from app.utils.execution_manager import ExecutionResult, TaskStatus
        import time
        import random
        
        time.sleep(random.uniform(0.1, 0.5))  # 模拟执行时间
        
        return ExecutionResult(
            task_id=node.id,
            status=TaskStatus.SUCCESS,
            output=f"任务 {node.name} 执行完成",
            input_parameters=node.input_parameters,
            output_parameters={'result': f'{node.name}_output'}
        )
    
    em = ExecutionManager(max_workers=3)
    
    # 添加状态回调
    def status_callback(task_id, status, result=None):
        print(f"任务 {task_id} 状态变更: {status.value}")
    
    em.add_status_callback(status_callback)
    
    try:
        results = em.execute_workflow(nodes, execution_order, mock_task_executor)
        print(f"执行结果: {results}")
    finally:
        em.cleanup()

if __name__ == '__main__':
    try:
        test_dependency_manager()
        test_condition_parser()
        test_parameter_manager()
        test_execution_manager()
        print("\n=== 所有测试完成 ===")
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
