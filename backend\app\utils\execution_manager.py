"""
任务执行管理器
"""
import threading
import time
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed, Future
from typing import Dict, List, Any, Callable, Optional
from enum import Enum
from dataclasses import dataclass
import logging
from .condition_parser import ConditionParser
from .parameter_manager import ParameterManager

logger = logging.getLogger(__name__)

class ExecutionMode(Enum):
    """执行模式"""
    SERIAL = "serial"      # 串行执行
    PARALLEL = "parallel"  # 并行执行
    MIXED = "mixed"        # 混合执行

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"

@dataclass
class ExecutionResult:
    """执行结果"""
    task_id: str
    status: TaskStatus
    output: str = ""
    error_message: str = ""
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration: Optional[float] = None
    input_parameters: Optional[Dict[str, Any]] = None
    output_parameters: Optional[Dict[str, Any]] = None
    return_code: int = 0

@dataclass
class TaskNode:
    """任务节点"""
    id: str
    name: str
    task_id: int
    execution_mode: ExecutionMode = ExecutionMode.SERIAL
    timeout: int = 3600
    retry_count: int = 0
    max_retries: int = 0
    dependencies: List[str] = None
    condition: str = "success"  # success, failed, always
    input_parameters: Dict[str, Any] = None
    parameter_mapping: List[Dict] = None
    output_extraction: List[Dict] = None

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.input_parameters is None:
            self.input_parameters = {}
        if self.parameter_mapping is None:
            self.parameter_mapping = []
        if self.output_extraction is None:
            self.output_extraction = []

class ExecutionManager:
    """任务执行管理器"""
    
    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.task_results: Dict[str, ExecutionResult] = {}
        self.task_futures: Dict[str, Future] = {}
        self.task_status: Dict[str, TaskStatus] = {}
        self.execution_lock = threading.Lock()
        self.status_callbacks: List[Callable] = []
        self.condition_parser = ConditionParser()
        self.parameter_manager = ParameterManager()
        self.global_parameters: Dict[str, Any] = {}
        
    def add_status_callback(self, callback: Callable):
        """添加状态变化回调"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, task_id: str, status: TaskStatus, result: ExecutionResult = None):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(task_id, status, result)
            except Exception as e:
                logger.error(f"状态回调执行失败: {str(e)}")
    
    def execute_workflow(self, nodes: List[TaskNode], execution_order: List[List[str]], 
                        task_executor: Callable) -> Dict[str, ExecutionResult]:
        """执行工作流"""
        try:
            # 初始化任务状态
            for node in nodes:
                self.task_status[node.id] = TaskStatus.PENDING
                self.task_results[node.id] = ExecutionResult(
                    task_id=node.id,
                    status=TaskStatus.PENDING
                )
            
            # 按层级执行任务
            for level, node_ids in enumerate(execution_order):
                logger.info(f"开始执行第 {level + 1} 层任务: {node_ids}")
                
                # 获取当前层级的节点
                level_nodes = [node for node in nodes if node.id in node_ids]
                
                # 根据执行模式执行任务
                self._execute_level(level_nodes, task_executor)
                
                # 检查是否有失败的任务需要停止执行
                failed_nodes = [node_id for node_id in node_ids 
                              if self.task_status[node_id] == TaskStatus.FAILED]
                
                if failed_nodes:
                    logger.warning(f"第 {level + 1} 层有任务失败: {failed_nodes}")
                    # 可以根据策略决定是否继续执行
            
            return self.task_results
            
        except Exception as e:
            logger.error(f"工作流执行失败: {str(e)}")
            raise
    
    def _execute_level(self, nodes: List[TaskNode], task_executor: Callable):
        """执行一个层级的任务"""
        # 分组：串行任务和并行任务
        serial_nodes = [node for node in nodes if node.execution_mode == ExecutionMode.SERIAL]
        parallel_nodes = [node for node in nodes if node.execution_mode == ExecutionMode.PARALLEL]
        
        # 先执行串行任务
        for node in serial_nodes:
            if self._should_execute_node(node):
                self._execute_single_task(node, task_executor)
        
        # 并行执行并行任务
        if parallel_nodes:
            self._execute_parallel_tasks(parallel_nodes, task_executor)
    
    def _should_execute_node(self, node: TaskNode) -> bool:
        """检查节点是否应该执行"""
        # 检查依赖条件
        for dep_id in node.dependencies:
            if dep_id not in self.task_results:
                return False

            dep_result = self.task_results[dep_id]

            # 构建条件上下文
            context = {
                'status': dep_result.status.value,
                'output': dep_result.output or '',
                'error_message': dep_result.error_message or '',
                'duration': dep_result.duration or 0,
                'return_code': 0 if dep_result.status == TaskStatus.SUCCESS else 1
            }

            # 使用条件解析器评估条件
            try:
                should_execute = self.condition_parser.parse_condition(node.condition, context)
                if not should_execute:
                    self.task_status[node.id] = TaskStatus.SKIPPED
                    self.task_results[node.id].status = TaskStatus.SKIPPED
                    self._notify_status_change(node.id, TaskStatus.SKIPPED, self.task_results[node.id])
                    return False
            except Exception as e:
                logger.error(f"条件评估失败，节点 {node.id}: {str(e)}")
                # 条件评估失败时，默认跳过执行
                self.task_status[node.id] = TaskStatus.SKIPPED
                self.task_results[node.id].status = TaskStatus.SKIPPED
                self._notify_status_change(node.id, TaskStatus.SKIPPED, self.task_results[node.id])
                return False

        return True
    
    def _execute_single_task(self, node: TaskNode, task_executor: Callable):
        """执行单个任务"""
        try:
            with self.execution_lock:
                self.task_status[node.id] = TaskStatus.RUNNING
                self.task_results[node.id].status = TaskStatus.RUNNING
                self.task_results[node.id].start_time = time.time()
            
            self._notify_status_change(node.id, TaskStatus.RUNNING)
            
            # 执行任务
            result = task_executor(node)
            
            with self.execution_lock:
                self.task_results[node.id] = result
                self.task_status[node.id] = result.status
                self.task_results[node.id].end_time = time.time()
                self.task_results[node.id].duration = (
                    self.task_results[node.id].end_time - self.task_results[node.id].start_time
                )
            
            self._notify_status_change(node.id, result.status, result)
            
        except Exception as e:
            error_result = ExecutionResult(
                task_id=node.id,
                status=TaskStatus.FAILED,
                error_message=str(e),
                end_time=time.time()
            )
            
            with self.execution_lock:
                self.task_results[node.id] = error_result
                self.task_status[node.id] = TaskStatus.FAILED
            
            self._notify_status_change(node.id, TaskStatus.FAILED, error_result)
            logger.error(f"任务 {node.id} 执行失败: {str(e)}")
    
    def _execute_parallel_tasks(self, nodes: List[TaskNode], task_executor: Callable):
        """并行执行任务"""
        # 过滤出需要执行的节点
        executable_nodes = [node for node in nodes if self._should_execute_node(node)]
        
        if not executable_nodes:
            return
        
        # 提交任务到线程池
        future_to_node = {}
        for node in executable_nodes:
            with self.execution_lock:
                self.task_status[node.id] = TaskStatus.RUNNING
                self.task_results[node.id].status = TaskStatus.RUNNING
                self.task_results[node.id].start_time = time.time()
            
            self._notify_status_change(node.id, TaskStatus.RUNNING)
            
            future = self.executor.submit(task_executor, node)
            future_to_node[future] = node
            self.task_futures[node.id] = future
        
        # 等待所有任务完成
        for future in as_completed(future_to_node):
            node = future_to_node[future]
            try:
                result = future.result()
                
                with self.execution_lock:
                    self.task_results[node.id] = result
                    self.task_status[node.id] = result.status
                    self.task_results[node.id].end_time = time.time()
                    self.task_results[node.id].duration = (
                        self.task_results[node.id].end_time - self.task_results[node.id].start_time
                    )
                
                self._notify_status_change(node.id, result.status, result)
                
            except Exception as e:
                error_result = ExecutionResult(
                    task_id=node.id,
                    status=TaskStatus.FAILED,
                    error_message=str(e),
                    end_time=time.time()
                )
                
                with self.execution_lock:
                    self.task_results[node.id] = error_result
                    self.task_status[node.id] = TaskStatus.FAILED
                
                self._notify_status_change(node.id, TaskStatus.FAILED, error_result)
                logger.error(f"并行任务 {node.id} 执行失败: {str(e)}")
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务执行"""
        if task_id in self.task_futures:
            future = self.task_futures[task_id]
            if future.cancel():
                with self.execution_lock:
                    self.task_status[task_id] = TaskStatus.CANCELLED
                    self.task_results[task_id].status = TaskStatus.CANCELLED
                
                self._notify_status_change(task_id, TaskStatus.CANCELLED)
                return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        return self.task_status.get(task_id)
    
    def get_task_result(self, task_id: str) -> Optional[ExecutionResult]:
        """获取任务结果"""
        return self.task_results.get(task_id)
    
    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=True)
        self.task_futures.clear()
        self.task_results.clear()
        self.task_status.clear()
