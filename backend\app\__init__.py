"""
Flask应用工厂
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS

# 全局数据库对象
db = SQLAlchemy()


def create_app(config=None):
    """创建Flask应用"""
    app = Flask(__name__)

    # 默认配置
    app.config.update({
        'SECRET_KEY': 'dev-secret-key',
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///task_manager.db',
        'SQLALCHEMY_TRACK_MODIFICATIONS': False,
        'SQLALCHEMY_ECHO': False,
    })

    # 应用自定义配置
    if config:
        app.config.update(config)

    # 初始化扩展
    db.init_app(app)
    CORS(app)

    # 注册蓝图
    register_blueprints(app)

    # 创建数据库表
    with app.app_context():
        if app.config.get('TESTING'):
            db.create_all()

    return app


def register_blueprints(app):
    """注册蓝图"""
    from app.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')


# 为了向后兼容，创建默认应用实例
app = create_app()