"""
参数管理器测试
"""
import pytest
import json
from app.utils.parameter_manager import ParameterManager, ParameterType


class TestParameterManager:
    """参数管理器测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.manager = ParameterManager()
    
    def test_convert_parameter_string(self):
        """测试字符串类型转换"""
        result = self.manager.convert_parameter("hello", ParameterType.STRING)
        assert result == "hello"
        
        result = self.manager.convert_parameter(123, ParameterType.STRING)
        assert result == "123"
        
        result = self.manager.convert_parameter(None, ParameterType.STRING)
        assert result is None
    
    def test_convert_parameter_integer(self):
        """测试整数类型转换"""
        result = self.manager.convert_parameter("42", ParameterType.INTEGER)
        assert result == 42
        
        result = self.manager.convert_parameter(42.7, ParameterType.INTEGER)
        assert result == 42
        
        with pytest.raises(ValueError):
            self.manager.convert_parameter("not_a_number", ParameterType.INTEGER)
    
    def test_convert_parameter_float(self):
        """测试浮点数类型转换"""
        result = self.manager.convert_parameter("3.14", ParameterType.FLOAT)
        assert result == 3.14
        
        result = self.manager.convert_parameter(42, ParameterType.FLOAT)
        assert result == 42.0
        
        with pytest.raises(ValueError):
            self.manager.convert_parameter("not_a_number", ParameterType.FLOAT)
    
    def test_convert_parameter_boolean(self):
        """测试布尔类型转换"""
        true_values = ["true", "True", "1", "yes", "on", True, 1]
        false_values = ["false", "False", "0", "no", "off", False, 0, ""]
        
        for value in true_values:
            result = self.manager.convert_parameter(value, ParameterType.BOOLEAN)
            assert result is True, f"值 {value} 应该转换为 True"
        
        for value in false_values:
            result = self.manager.convert_parameter(value, ParameterType.BOOLEAN)
            assert result is False, f"值 {value} 应该转换为 False"
    
    def test_convert_parameter_json(self):
        """测试JSON类型转换"""
        # 字典对象
        dict_obj = {"key": "value", "number": 42}
        result = self.manager.convert_parameter(dict_obj, ParameterType.JSON)
        assert result == dict_obj
        
        # JSON字符串
        json_str = '{"key": "value", "number": 42}'
        result = self.manager.convert_parameter(json_str, ParameterType.JSON)
        assert result == {"key": "value", "number": 42}
        
        # 无效JSON
        with pytest.raises(ValueError):
            self.manager.convert_parameter("invalid json", ParameterType.JSON)
    
    def test_convert_parameter_list(self):
        """测试列表类型转换"""
        # 列表对象
        list_obj = [1, 2, 3]
        result = self.manager.convert_parameter(list_obj, ParameterType.LIST)
        assert result == list_obj
        
        # JSON数组字符串
        json_array = '[1, 2, 3]'
        result = self.manager.convert_parameter(json_array, ParameterType.LIST)
        assert result == [1, 2, 3]
        
        # 逗号分隔字符串
        csv_str = "apple, banana, cherry"
        result = self.manager.convert_parameter(csv_str, ParameterType.LIST)
        assert result == ["apple", "banana", "cherry"]
        
        # 元组转换
        tuple_obj = (1, 2, 3)
        result = self.manager.convert_parameter(tuple_obj, ParameterType.LIST)
        assert result == [1, 2, 3]
    
    def test_validate_parameter_required(self):
        """测试必填参数验证"""
        # 必填参数为None
        is_valid, message = self.manager.validate_parameter(
            None, ParameterType.STRING, {'required': True}
        )
        assert not is_valid
        assert "不能为空" in message
        
        # 必填参数有值
        is_valid, message = self.manager.validate_parameter(
            "value", ParameterType.STRING, {'required': True}
        )
        assert is_valid
    
    def test_validate_parameter_numeric_range(self):
        """测试数值范围验证"""
        # 整数范围
        is_valid, message = self.manager.validate_parameter(
            50, ParameterType.INTEGER, {'min': 0, 'max': 100}
        )
        assert is_valid
        
        # 超出最小值
        is_valid, message = self.manager.validate_parameter(
            -10, ParameterType.INTEGER, {'min': 0, 'max': 100}
        )
        assert not is_valid
        assert "不能小于" in message
        
        # 超出最大值
        is_valid, message = self.manager.validate_parameter(
            150, ParameterType.INTEGER, {'min': 0, 'max': 100}
        )
        assert not is_valid
        assert "不能大于" in message
    
    def test_validate_parameter_string_length(self):
        """测试字符串长度验证"""
        # 正常长度
        is_valid, message = self.manager.validate_parameter(
            "hello", ParameterType.STRING, {'min_length': 3, 'max_length': 10}
        )
        assert is_valid
        
        # 太短
        is_valid, message = self.manager.validate_parameter(
            "hi", ParameterType.STRING, {'min_length': 3, 'max_length': 10}
        )
        assert not is_valid
        assert "不能小于" in message
        
        # 太长
        is_valid, message = self.manager.validate_parameter(
            "this is a very long string", ParameterType.STRING, {'min_length': 3, 'max_length': 10}
        )
        assert not is_valid
        assert "不能大于" in message
    
    def test_validate_parameter_pattern(self):
        """测试正则表达式验证"""
        # 匹配模式
        is_valid, message = self.manager.validate_parameter(
            "<EMAIL>", ParameterType.STRING, {'pattern': r'^[^@]+@[^@]+\.[^@]+$'}
        )
        assert is_valid
        
        # 不匹配模式
        is_valid, message = self.manager.validate_parameter(
            "invalid-email", ParameterType.STRING, {'pattern': r'^[^@]+@[^@]+\.[^@]+$'}
        )
        assert not is_valid
        assert "格式不匹配" in message
    
    def test_validate_parameter_enum(self):
        """测试枚举值验证"""
        # 有效枚举值
        is_valid, message = self.manager.validate_parameter(
            "red", ParameterType.STRING, {'enum': ['red', 'green', 'blue']}
        )
        assert is_valid
        
        # 无效枚举值
        is_valid, message = self.manager.validate_parameter(
            "yellow", ParameterType.STRING, {'enum': ['red', 'green', 'blue']}
        )
        assert not is_valid
        assert "必须是以下之一" in message
    
    def test_extract_parameters_from_output(self):
        """测试从输出中提取参数"""
        output = """
        数据处理完成
        处理了 1500 条记录
        清洗后剩余 1350 条有效记录
        生成文件: /tmp/results/processed_data_20241220.json
        执行时间: 45.6 秒
        """
        
        extraction_rules = [
            {"name": "total_records", "pattern": r"处理了 (\d+) 条记录", "type": "integer"},
            {"name": "valid_records", "pattern": r"剩余 (\d+) 条有效记录", "type": "integer"},
            {"name": "output_file", "pattern": r"生成文件: ([^\s]+)", "type": "string"},
            {"name": "execution_time", "pattern": r"执行时间: ([\d.]+) 秒", "type": "float"},
            {"name": "missing_param", "pattern": r"不存在的模式", "type": "string", "default": "default_value"}
        ]
        
        extracted = self.manager.extract_parameters_from_output(output, extraction_rules)
        
        assert extracted["total_records"] == 1500
        assert extracted["valid_records"] == 1350
        assert extracted["output_file"] == "/tmp/results/processed_data_20241220.json"
        assert extracted["execution_time"] == 45.6
        assert extracted["missing_param"] == "default_value"
    
    def test_resolve_parameter_references(self):
        """测试解析参数引用"""
        parameters = {
            "input_file": "${previous_output}",
            "batch_size": 500,
            "output_dir": "/tmp/results/${date}",
            "config": {
                "format": "${output_format}",
                "compression": True
            },
            "list_param": ["${item1}", "${item2}", "static_item"]
        }
        
        context = {
            "previous_output": "/tmp/raw_data.csv",
            "date": "2024-12-20",
            "output_format": "json",
            "item1": "dynamic_item1",
            "item2": "dynamic_item2"
        }
        
        resolved = self.manager.resolve_parameter_references(parameters, context)
        
        assert resolved["input_file"] == "/tmp/raw_data.csv"
        assert resolved["batch_size"] == 500
        assert resolved["output_dir"] == "/tmp/results/2024-12-20"
        assert resolved["config"]["format"] == "json"
        assert resolved["config"]["compression"] is True
        assert resolved["list_param"] == ["dynamic_item1", "dynamic_item2", "static_item"]
    
    def test_resolve_parameter_references_missing_variable(self):
        """测试解析缺少变量的参数引用"""
        parameters = {
            "existing_var": "${found}",
            "missing_var": "${not_found}",
            "partial_missing": "prefix_${missing}_suffix"
        }
        
        context = {
            "found": "found_value"
        }
        
        resolved = self.manager.resolve_parameter_references(parameters, context)
        
        assert resolved["existing_var"] == "found_value"
        assert resolved["missing_var"] == "${not_found}"  # 保持原样
        assert resolved["partial_missing"] == "prefix_${missing}_suffix"  # 保持原样
    
    def test_create_parameter_mapping(self):
        """测试创建参数映射"""
        source_params = {
            "raw_count": 1000,
            "output_path": "/tmp/data.csv",
            "status_message": "  Processing Complete  ",
            "error_count": "5"
        }
        
        mapping_rules = [
            {"source": "raw_count", "target": "input_count"},
            {"source": "output_path", "target": "next_input_file"},
            {"source": "status_message", "target": "clean_message", "transform": "strip"},
            {"source": "error_count", "target": "error_num", "transform": "int"},
            {"source": "missing_source", "target": "default_target", "default": "default_value"}
        ]
        
        mapped = self.manager.create_parameter_mapping(source_params, mapping_rules)
        
        assert mapped["input_count"] == 1000
        assert mapped["next_input_file"] == "/tmp/data.csv"
        assert mapped["clean_message"] == "Processing Complete"
        assert mapped["error_num"] == 5
        assert mapped["default_target"] == "default_value"
    
    def test_apply_transform(self):
        """测试参数转换"""
        test_cases = [
            ("hello world", "upper", "HELLO WORLD"),
            ("HELLO WORLD", "lower", "hello world"),
            ("  spaced  ", "strip", "spaced"),
            ("42", "int", 42),
            ("3.14", "float", 3.14),
            ("true", "bool", True),
            ('{"key": "value"}', "json", {"key": "value"}),
            ("a,b,c", "list", ["a", "b", "c"]),
            ("test123", "regex:test(\\d+)", "123")
        ]
        
        for value, transform, expected in test_cases:
            result = self.manager._apply_transform(value, transform)
            assert result == expected, f"转换 {value} 使用 {transform} 应该得到 {expected}"
    
    def test_get_parameter_schema(self):
        """测试获取参数模式"""
        schema = self.manager.get_parameter_schema()
        
        assert "types" in schema
        assert "constraints" in schema
        assert "transforms" in schema
        assert "examples" in schema
        
        # 检查类型
        types = schema["types"]
        assert "string" in types
        assert "integer" in types
        assert "float" in types
        assert "boolean" in types
        assert "json" in types
        assert "list" in types
        
        # 检查约束
        constraints = schema["constraints"]
        assert "required" in constraints
        assert "min" in constraints
        assert "max" in constraints
        
        # 检查转换
        transforms = schema["transforms"]
        assert "upper" in transforms
        assert "lower" in transforms
        assert "int" in transforms
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 空值处理
        result = self.manager.convert_parameter(None, ParameterType.STRING)
        assert result is None
        
        # 空字符串转换为列表
        result = self.manager.convert_parameter("", ParameterType.LIST)
        assert result == []
        
        # 单个值转换为列表
        result = self.manager.convert_parameter("single", ParameterType.LIST)
        assert result == ["single"]
        
        # 空约束验证
        is_valid, message = self.manager.validate_parameter("test", ParameterType.STRING, {})
        assert is_valid
        
        # 无效参数类型
        with pytest.raises(ValueError):
            self.manager.convert_parameter("test", "invalid_type")
