{"ast": null, "code": "import { NotificationPlacements } from '../interface';\nconst placementAlignProperty = {\n  topLeft: 'left',\n  topRight: 'right',\n  bottomLeft: 'left',\n  bottomRight: 'right',\n  top: 'left',\n  bottom: 'left'\n};\nconst genPlacementStackStyle = (token, placement) => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-${placement}`]: {\n      [`&${componentCls}-stack > ${componentCls}-notice-wrapper`]: {\n        [placement.startsWith('top') ? 'top' : 'bottom']: 0,\n        [placementAlignProperty[placement]]: {\n          value: 0,\n          _skip_check_: true\n        }\n      }\n    }\n  };\n};\nconst genStackChildrenStyle = token => {\n  const childrenStyle = {};\n  for (let i = 1; i < token.notificationStackLayer; i++) {\n    childrenStyle[`&:nth-last-child(${i + 1})`] = {\n      overflow: 'hidden',\n      [`& > ${token.componentCls}-notice`]: {\n        opacity: 0,\n        transition: `opacity ${token.motionDurationMid}`\n      }\n    };\n  }\n  return Object.assign({\n    [`&:not(:nth-last-child(-n+${token.notificationStackLayer}))`]: {\n      opacity: 0,\n      overflow: 'hidden',\n      color: 'transparent',\n      pointerEvents: 'none'\n    }\n  }, childrenStyle);\n};\nconst genStackedNoticeStyle = token => {\n  const childrenStyle = {};\n  for (let i = 1; i < token.notificationStackLayer; i++) {\n    childrenStyle[`&:nth-last-child(${i + 1})`] = {\n      background: token.colorBgBlur,\n      backdropFilter: 'blur(10px)',\n      '-webkit-backdrop-filter': 'blur(10px)'\n    };\n  }\n  return Object.assign({}, childrenStyle);\n};\nconst genStackStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return Object.assign({\n    [`${componentCls}-stack`]: {\n      [`& > ${componentCls}-notice-wrapper`]: Object.assign({\n        transition: `transform ${token.motionDurationSlow}, backdrop-filter 0s`,\n        willChange: 'transform, opacity',\n        position: 'absolute'\n      }, genStackChildrenStyle(token))\n    },\n    [`${componentCls}-stack:not(${componentCls}-stack-expanded)`]: {\n      [`& > ${componentCls}-notice-wrapper`]: Object.assign({}, genStackedNoticeStyle(token))\n    },\n    [`${componentCls}-stack${componentCls}-stack-expanded`]: {\n      [`& > ${componentCls}-notice-wrapper`]: {\n        '&:not(:nth-last-child(-n + 1))': {\n          opacity: 1,\n          overflow: 'unset',\n          color: 'inherit',\n          pointerEvents: 'auto',\n          [`& > ${token.componentCls}-notice`]: {\n            opacity: 1\n          }\n        },\n        '&:after': {\n          content: '\"\"',\n          position: 'absolute',\n          height: token.margin,\n          width: '100%',\n          insetInline: 0,\n          bottom: token.calc(token.margin).mul(-1).equal(),\n          background: 'transparent',\n          pointerEvents: 'auto'\n        }\n      }\n    }\n  }, NotificationPlacements.map(placement => genPlacementStackStyle(token, placement)).reduce((acc, cur) => Object.assign(Object.assign({}, acc), cur), {}));\n};\nexport default genStackStyle;", "map": {"version": 3, "names": ["NotificationPlacements", "placementAlignProperty", "topLeft", "topRight", "bottomLeft", "bottomRight", "top", "bottom", "genPlacementStackStyle", "token", "placement", "componentCls", "startsWith", "value", "_skip_check_", "genStackChildrenStyle", "childrenStyle", "i", "notificationS<PERSON>ck<PERSON><PERSON><PERSON>", "overflow", "opacity", "transition", "motionDurationMid", "Object", "assign", "color", "pointerEvents", "genStackedNoticeStyle", "background", "colorBgBlur", "<PERSON><PERSON>ilter", "genStackStyle", "motionDurationSlow", "<PERSON><PERSON><PERSON><PERSON>", "position", "content", "height", "margin", "width", "insetInline", "calc", "mul", "equal", "map", "reduce", "acc", "cur"], "sources": ["E:/code1/task3/frontend/node_modules/antd/es/notification/style/stack.js"], "sourcesContent": ["import { NotificationPlacements } from '../interface';\nconst placementAlignProperty = {\n  topLeft: 'left',\n  topRight: 'right',\n  bottomLeft: 'left',\n  bottomRight: 'right',\n  top: 'left',\n  bottom: 'left'\n};\nconst genPlacementStackStyle = (token, placement) => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-${placement}`]: {\n      [`&${componentCls}-stack > ${componentCls}-notice-wrapper`]: {\n        [placement.startsWith('top') ? 'top' : 'bottom']: 0,\n        [placementAlignProperty[placement]]: {\n          value: 0,\n          _skip_check_: true\n        }\n      }\n    }\n  };\n};\nconst genStackChildrenStyle = token => {\n  const childrenStyle = {};\n  for (let i = 1; i < token.notificationStackLayer; i++) {\n    childrenStyle[`&:nth-last-child(${i + 1})`] = {\n      overflow: 'hidden',\n      [`& > ${token.componentCls}-notice`]: {\n        opacity: 0,\n        transition: `opacity ${token.motionDurationMid}`\n      }\n    };\n  }\n  return Object.assign({\n    [`&:not(:nth-last-child(-n+${token.notificationStackLayer}))`]: {\n      opacity: 0,\n      overflow: 'hidden',\n      color: 'transparent',\n      pointerEvents: 'none'\n    }\n  }, childrenStyle);\n};\nconst genStackedNoticeStyle = token => {\n  const childrenStyle = {};\n  for (let i = 1; i < token.notificationStackLayer; i++) {\n    childrenStyle[`&:nth-last-child(${i + 1})`] = {\n      background: token.colorBgBlur,\n      backdropFilter: 'blur(10px)',\n      '-webkit-backdrop-filter': 'blur(10px)'\n    };\n  }\n  return Object.assign({}, childrenStyle);\n};\nconst genStackStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return Object.assign({\n    [`${componentCls}-stack`]: {\n      [`& > ${componentCls}-notice-wrapper`]: Object.assign({\n        transition: `transform ${token.motionDurationSlow}, backdrop-filter 0s`,\n        willChange: 'transform, opacity',\n        position: 'absolute'\n      }, genStackChildrenStyle(token))\n    },\n    [`${componentCls}-stack:not(${componentCls}-stack-expanded)`]: {\n      [`& > ${componentCls}-notice-wrapper`]: Object.assign({}, genStackedNoticeStyle(token))\n    },\n    [`${componentCls}-stack${componentCls}-stack-expanded`]: {\n      [`& > ${componentCls}-notice-wrapper`]: {\n        '&:not(:nth-last-child(-n + 1))': {\n          opacity: 1,\n          overflow: 'unset',\n          color: 'inherit',\n          pointerEvents: 'auto',\n          [`& > ${token.componentCls}-notice`]: {\n            opacity: 1\n          }\n        },\n        '&:after': {\n          content: '\"\"',\n          position: 'absolute',\n          height: token.margin,\n          width: '100%',\n          insetInline: 0,\n          bottom: token.calc(token.margin).mul(-1).equal(),\n          background: 'transparent',\n          pointerEvents: 'auto'\n        }\n      }\n    }\n  }, NotificationPlacements.map(placement => genPlacementStackStyle(token, placement)).reduce((acc, cur) => Object.assign(Object.assign({}, acc), cur), {}));\n};\nexport default genStackStyle;"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,cAAc;AACrD,MAAMC,sBAAsB,GAAG;EAC7BC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,OAAO;EACjBC,UAAU,EAAE,MAAM;EAClBC,WAAW,EAAE,OAAO;EACpBC,GAAG,EAAE,MAAM;EACXC,MAAM,EAAE;AACV,CAAC;AACD,MAAMC,sBAAsB,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;EACnD,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAAC,GAAGE,YAAY,IAAID,SAAS,EAAE,GAAG;MAChC,CAAC,IAAIC,YAAY,YAAYA,YAAY,iBAAiB,GAAG;QAC3D,CAACD,SAAS,CAACE,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ,GAAG,CAAC;QACnD,CAACX,sBAAsB,CAACS,SAAS,CAAC,GAAG;UACnCG,KAAK,EAAE,CAAC;UACRC,YAAY,EAAE;QAChB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMC,qBAAqB,GAAGN,KAAK,IAAI;EACrC,MAAMO,aAAa,GAAG,CAAC,CAAC;EACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACS,sBAAsB,EAAED,CAAC,EAAE,EAAE;IACrDD,aAAa,CAAC,oBAAoBC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;MAC5CE,QAAQ,EAAE,QAAQ;MAClB,CAAC,OAAOV,KAAK,CAACE,YAAY,SAAS,GAAG;QACpCS,OAAO,EAAE,CAAC;QACVC,UAAU,EAAE,WAAWZ,KAAK,CAACa,iBAAiB;MAChD;IACF,CAAC;EACH;EACA,OAAOC,MAAM,CAACC,MAAM,CAAC;IACnB,CAAC,4BAA4Bf,KAAK,CAACS,sBAAsB,IAAI,GAAG;MAC9DE,OAAO,EAAE,CAAC;MACVD,QAAQ,EAAE,QAAQ;MAClBM,KAAK,EAAE,aAAa;MACpBC,aAAa,EAAE;IACjB;EACF,CAAC,EAAEV,aAAa,CAAC;AACnB,CAAC;AACD,MAAMW,qBAAqB,GAAGlB,KAAK,IAAI;EACrC,MAAMO,aAAa,GAAG,CAAC,CAAC;EACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACS,sBAAsB,EAAED,CAAC,EAAE,EAAE;IACrDD,aAAa,CAAC,oBAAoBC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;MAC5CW,UAAU,EAAEnB,KAAK,CAACoB,WAAW;MAC7BC,cAAc,EAAE,YAAY;MAC5B,yBAAyB,EAAE;IAC7B,CAAC;EACH;EACA,OAAOP,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,aAAa,CAAC;AACzC,CAAC;AACD,MAAMe,aAAa,GAAGtB,KAAK,IAAI;EAC7B,MAAM;IACJE;EACF,CAAC,GAAGF,KAAK;EACT,OAAOc,MAAM,CAACC,MAAM,CAAC;IACnB,CAAC,GAAGb,YAAY,QAAQ,GAAG;MACzB,CAAC,OAAOA,YAAY,iBAAiB,GAAGY,MAAM,CAACC,MAAM,CAAC;QACpDH,UAAU,EAAE,aAAaZ,KAAK,CAACuB,kBAAkB,sBAAsB;QACvEC,UAAU,EAAE,oBAAoB;QAChCC,QAAQ,EAAE;MACZ,CAAC,EAAEnB,qBAAqB,CAACN,KAAK,CAAC;IACjC,CAAC;IACD,CAAC,GAAGE,YAAY,cAAcA,YAAY,kBAAkB,GAAG;MAC7D,CAAC,OAAOA,YAAY,iBAAiB,GAAGY,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEG,qBAAqB,CAAClB,KAAK,CAAC;IACxF,CAAC;IACD,CAAC,GAAGE,YAAY,SAASA,YAAY,iBAAiB,GAAG;MACvD,CAAC,OAAOA,YAAY,iBAAiB,GAAG;QACtC,gCAAgC,EAAE;UAChCS,OAAO,EAAE,CAAC;UACVD,QAAQ,EAAE,OAAO;UACjBM,KAAK,EAAE,SAAS;UAChBC,aAAa,EAAE,MAAM;UACrB,CAAC,OAAOjB,KAAK,CAACE,YAAY,SAAS,GAAG;YACpCS,OAAO,EAAE;UACX;QACF,CAAC;QACD,SAAS,EAAE;UACTe,OAAO,EAAE,IAAI;UACbD,QAAQ,EAAE,UAAU;UACpBE,MAAM,EAAE3B,KAAK,CAAC4B,MAAM;UACpBC,KAAK,EAAE,MAAM;UACbC,WAAW,EAAE,CAAC;UACdhC,MAAM,EAAEE,KAAK,CAAC+B,IAAI,CAAC/B,KAAK,CAAC4B,MAAM,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UAChDd,UAAU,EAAE,aAAa;UACzBF,aAAa,EAAE;QACjB;MACF;IACF;EACF,CAAC,EAAE1B,sBAAsB,CAAC2C,GAAG,CAACjC,SAAS,IAAIF,sBAAsB,CAACC,KAAK,EAAEC,SAAS,CAAC,CAAC,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKvB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqB,GAAG,CAAC,EAAEC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC5J,CAAC;AACD,eAAef,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}