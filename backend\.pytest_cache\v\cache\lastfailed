{"tests/test_dependency_manager.py::TestDependencyManager::test_validate_workflow_missing_node": true, "tests/test_condition_parser.py::TestConditionParser::test_type_conversions": true, "tests/test_condition_parser.py::TestConditionParser::test_validate_condition_invalid": true, "tests/test_execution_manager.py::TestExecutionManager::test_execute_parallel_tasks": true, "test_api.py::test_tasks_api": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_workflows_empty": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_create_workflow_success": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_create_workflow_missing_name": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_create_workflow_invalid_definition": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_workflow_by_id": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_workflow_not_found": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_update_workflow": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_delete_workflow": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_execute_workflow": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_workflow_valid": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_workflow_invalid": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_execution_plan": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_condition_valid": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_condition_invalid": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_condition_help": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_validate_parameters": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_get_parameter_schema": true, "tests/test_api_task_workflows.py::TestTaskWorkflowsAPI::test_api_error_handling": true, "tests/test_integration.py::TestWorkflowIntegration::test_complete_workflow_lifecycle": true, "tests/test_integration.py::TestWorkflowIntegration::test_workflow_with_conditions": true, "tests/test_integration.py::TestWorkflowIntegration::test_workflow_with_parallel_execution": true, "tests/test_integration.py::TestParameterIntegration::test_parameter_validation_api": true, "tests/test_integration.py::TestParameterIntegration::test_parameter_schema_api": true, "tests/test_integration.py::TestConditionIntegration::test_condition_validation_api": true, "tests/test_integration.py::TestConditionIntegration::test_condition_help_api": true, "tests/test_integration.py::TestPerformanceIntegration::test_large_workflow_validation": true, "tests/test_integration.py::TestPerformanceIntegration::test_concurrent_workflow_operations": true, "tests/test_models.py::TestTaskModel::test_create_task": true, "tests/test_models.py::TestTaskModel::test_task_name_required": true, "tests/test_models.py::TestTaskModel::test_task_script_relationship": true, "tests/test_models.py::TestTaskModel::test_task_executions_relationship": true, "tests/test_models.py::TestScriptModel::test_create_script": true, "tests/test_models.py::TestScriptModel::test_script_name_required": true, "tests/test_models.py::TestScriptModel::test_script_type_required": true, "tests/test_models.py::TestTaskExecutionModel::test_create_execution": true, "tests/test_models.py::TestTaskExecutionModel::test_execution_duration_calculation": true, "tests/test_models.py::TestTaskExecutionModel::test_execution_task_required": true, "tests/test_models.py::TestTaskWorkflowModel::test_create_workflow": true, "tests/test_models.py::TestTaskWorkflowModel::test_workflow_name_required": true, "tests/test_models.py::TestTaskWorkflowModel::test_workflow_executions_relationship": true, "tests/test_models.py::TestTaskWorkflowExecutionModel::test_create_workflow_execution": true, "tests/test_models.py::TestTaskWorkflowExecutionModel::test_workflow_execution_workflow_required": true, "tests/test_models.py::TestWorkflowTaskModel::test_create_workflow_task": true, "tests/test_models.py::TestWorkflowTaskModel::test_workflow_task_relationships": true, "tests/test_models.py::TestModelIntegration::test_complete_workflow_setup": true, "tests/test_security.py::TestSecurity::test_limit_execution_time_decorator": true, "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_complex_logical_expressions": true, "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_nested_conditions": true, "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_mathematical_operations": true, "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_string_methods": true, "tests/test_condition_parser_additional.py::TestConditionParserAdditional::test_list_operations": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_absolute": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_relative": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_dangerous": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_none": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_edge_cases": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_with_spaces": true, "tests/test_security_additional.py::TestSecurityAdditional::test_validate_file_path_unicode": true}