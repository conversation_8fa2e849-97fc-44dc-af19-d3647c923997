{"tests/test_task_workflows.py::TestTaskWorkflows::test_create_workflow": true, "tests/test_task_workflows.py::TestTaskWorkflows::test_create_workflow_execution": true, "tests/test_task_workflows.py::TestTaskWorkflows::test_update_workflow": true, "tests/test_task_workflows.py::TestTaskWorkflows::test_workflow_execution_status_transition": true, "tests/test_dependency_manager.py::TestDependencyManager::test_validate_workflow_missing_node": true, "tests/test_condition_parser.py::TestConditionParser::test_missing_variables": true, "tests/test_condition_parser.py::TestConditionParser::test_type_conversions": true, "tests/test_condition_parser.py::TestConditionParser::test_validate_condition_invalid": true, "tests/test_execution_manager.py::TestExecutionManager::test_execute_parallel_tasks": true, "tests/test_security.py": true}